/*
 *  BSD LICENSE
 *
 *  Copyright(c) 2015 Broadcom Corporation.  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    * Redistributions of source code must retain the above copyright
 *      notice, this list of conditions and the following disclaimer.
 *    * Redistributions in binary form must reproduce the above copyright
 *      notice, this list of conditions and the following disclaimer in
 *      the documentation and/or other materials provided with the
 *      distribution.
 *    * Neither the name of Broadcom Corporation nor the names of its
 *      contributors may be used to endorse or promote products derived
 *      from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>,
 *  <PERSON>ECIA<PERSON>, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/dts-v1/;

#include "bcm-cygnus.dtsi"
#include "bcm9hmidc.dtsi"

/ {
	model = "Cygnus Wireless Audio (BCM958305K)";
	compatible = "brcm,bcm58305", "brcm,cygnus";

	aliases {
		serial0 = &uart3;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&pcie0 {
	status = "okay";
};

&pcie1 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&nand_controller {
	nand@1 {
		compatible = "brcm,nandcs";
		reg = <0>;
		nand-on-flash-bbt;

		#address-cells = <1>;
		#size-cells = <1>;

		nand-ecc-strength = <24>;
		nand-ecc-step-size = <1024>;

		brcm,nand-oob-sector-size = <27>;
	};
};
