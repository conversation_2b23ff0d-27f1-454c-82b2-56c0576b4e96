// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2020 Facebook Inc.

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	aliases {
		mmc0 = &emmc;
		spi1 = &spi1;
		spi2 = &spi_gpio;
	};

	chosen {
		bootargs = "console=ttyS0,9600n8 root=/dev/ram rw vmalloc=640M";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	/*
	 * GPIO-based SPI Master is required to access SPI TPM, because
	 * full-duplex SPI transactions are not supported by ASPEED SPI
	 * Controllers.
	 */
	spi_gpio: spi {
		status = "okay";
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-sck = <&gpio0 ASPEED_GPIO(X, 3) GPIO_ACTIVE_HIGH>;
		gpio-mosi = <&gpio0 ASPEED_GPIO(X, 4) GPIO_ACTIVE_HIGH>;
		gpio-miso = <&gpio0 ASPEED_GPIO(X, 5) GPIO_ACTIVE_HIGH>;

		tpm@0 {
			compatible = "infineon,slb9670", "tcg,tpm_tis-spi";
			spi-max-frequency = <33000000>;
			reg = <0>;
		};
	};
};

&fmc {
	status = "okay";

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "spi0.0";

#include "facebook-bmc-flash-layout-128.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "spi0.1";

		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			flash1@0 {
				reg = <0x0 0x8000000>;
				label = "flash1";
			};
		};
	};
};

&spi1 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&wdt1 {
	status = "okay";
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&i2c15 {
	status = "okay";
};

&vhub {
	status = "okay";
};

&emmc_controller {
	status = "okay";
};

&emmc {
	status = "okay";

	non-removable;
	max-frequency = <25000000>;
	bus-width = <4>;
};

&rtc {
	status = "okay";
};
