// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2022 Broadcom Ltd.
 */

/dts-v1/;

#include "bcm6846.dtsi"

/ {
	model = "Broadcom BCM96846 Reference Board";
	compatible = "brcm,bcm96846", "brcm,bcm6846", "brcm,bcmbca";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x08000000>;
	};
};

&uart0 {
	status = "okay";
};

&hsspi {
	status = "okay";
};

&nand_controller {
	brcm,wp-not-connected;
	status = "okay";
};

&nandcs {
	nand-on-flash-bbt;
	brcm,nand-ecc-use-strap;
};
