/*
 * Copyright 2015 <PERSON>
 *
 * <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

#include <dt-bindings/clock/sun8i-a83t-ccu.h>
#include <dt-bindings/clock/sun8i-de2.h>
#include <dt-bindings/clock/sun8i-r-ccu.h>
#include <dt-bindings/reset/sun8i-a83t-ccu.h>
#include <dt-bindings/reset/sun8i-de2.h>
#include <dt-bindings/reset/sun8i-r-ccu.h>
#include <dt-bindings/thermal/thermal.h>

/ {
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C0CPUX>;
			operating-points-v2 = <&cpu0_opp_table>;
			cci-control-port = <&cci_control0>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <0>;
			#cooling-cells = <2>;
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C0CPUX>;
			operating-points-v2 = <&cpu0_opp_table>;
			cci-control-port = <&cci_control0>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <1>;
			#cooling-cells = <2>;
		};

		cpu2: cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C0CPUX>;
			operating-points-v2 = <&cpu0_opp_table>;
			cci-control-port = <&cci_control0>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <2>;
			#cooling-cells = <2>;
		};

		cpu3: cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C0CPUX>;
			operating-points-v2 = <&cpu0_opp_table>;
			cci-control-port = <&cci_control0>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <3>;
			#cooling-cells = <2>;
		};

		cpu100: cpu@100 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C1CPUX>;
			operating-points-v2 = <&cpu1_opp_table>;
			cci-control-port = <&cci_control1>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <0x100>;
			#cooling-cells = <2>;
		};

		cpu101: cpu@101 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C1CPUX>;
			operating-points-v2 = <&cpu1_opp_table>;
			cci-control-port = <&cci_control1>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <0x101>;
			#cooling-cells = <2>;
		};

		cpu102: cpu@102 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C1CPUX>;
			operating-points-v2 = <&cpu1_opp_table>;
			cci-control-port = <&cci_control1>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <0x102>;
			#cooling-cells = <2>;
		};

		cpu103: cpu@103 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			clocks = <&ccu CLK_C1CPUX>;
			operating-points-v2 = <&cpu1_opp_table>;
			cci-control-port = <&cci_control1>;
			enable-method = "allwinner,sun8i-a83t-smp";
			reg = <0x103>;
			#cooling-cells = <2>;
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>;
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		/* TODO: PRCM block has a mux for this. */
		osc24M: osc24M-clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-accuracy = <50000>;
			clock-output-names = "osc24M";
		};

		/*
		 * This is called "internal OSC" in some places.
		 * It is an internal RC-based oscillator.
		 * TODO: Its controls are in the PRCM block.
		 */
		osc16M: osc16M-clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <16000000>;
			clock-output-names = "osc16M";
		};

		osc16Md512: osc16Md512-clk {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clock-div = <512>;
			clock-mult = <1>;
			clocks = <&osc16M>;
			clock-output-names = "osc16M-d512";
		};
	};

	de: display-engine {
		compatible = "allwinner,sun8i-a83t-display-engine";
		allwinner,pipelines = <&mixer0>, <&mixer1>;
		status = "disabled";
	};

	cpu0_opp_table: opp-table-cluster0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-480000000 {
			opp-hz = /bits/ 64 <480000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-720000000 {
			opp-hz = /bits/ 64 <720000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-864000000 {
			opp-hz = /bits/ 64 <864000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-912000000 {
			opp-hz = /bits/ 64 <912000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1128000000 {
			opp-hz = /bits/ 64 <1128000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};
	};

	cpu1_opp_table: opp-table-cluster1 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-480000000 {
			opp-hz = /bits/ 64 <480000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-720000000 {
			opp-hz = /bits/ 64 <720000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-864000000 {
			opp-hz = /bits/ 64 <864000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-912000000 {
			opp-hz = /bits/ 64 <912000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1128000000 {
			opp-hz = /bits/ 64 <1128000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <840000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		display_clocks: clock@1000000 {
			compatible = "allwinner,sun8i-a83t-de2-clk";
			reg = <0x01000000 0x10000>;
			clocks = <&ccu CLK_BUS_DE>,
				 <&ccu CLK_PLL_DE>;
			clock-names = "bus",
				      "mod";
			resets = <&ccu RST_BUS_DE>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		rotate: rotate@1020000 {
			compatible = "allwinner,sun8i-a83t-de2-rotate";
			reg = <0x1020000 0x10000>;
			interrupts = <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&display_clocks CLK_BUS_ROT>,
				 <&display_clocks CLK_ROT>;
			clock-names = "bus",
				      "mod";
			resets = <&display_clocks RST_ROT>;
		};

		mixer0: mixer@1100000 {
			compatible = "allwinner,sun8i-a83t-de2-mixer-0";
			reg = <0x01100000 0x100000>;
			clocks = <&display_clocks CLK_BUS_MIXER0>,
				 <&display_clocks CLK_MIXER0>;
			clock-names = "bus",
				      "mod";
			resets = <&display_clocks RST_MIXER0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				mixer0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					mixer0_out_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_in_mixer0>;
					};

					mixer0_out_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_in_mixer0>;
					};
				};
			};
		};

		mixer1: mixer@1200000 {
			compatible = "allwinner,sun8i-a83t-de2-mixer-1";
			reg = <0x01200000 0x100000>;
			clocks = <&display_clocks CLK_BUS_MIXER1>,
				 <&display_clocks CLK_MIXER1>;
			clock-names = "bus",
				      "mod";
			resets = <&display_clocks RST_WB>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				mixer1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					mixer1_out_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_in_mixer1>;
					};

					mixer1_out_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_in_mixer1>;
					};
				};
			};
		};

		cpucfg@1700000 {
			compatible = "allwinner,sun8i-a83t-cpucfg";
			reg = <0x01700000 0x400>;
		};

		cci@1790000 {
			compatible = "arm,cci-400";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x01790000 0x10000>;
			ranges = <0x0 0x01790000 0x10000>;

			cci_control0: slave-if@4000 {
				compatible = "arm,cci-400-ctrl-if";
				interface-type = "ace";
				reg = <0x4000 0x1000>;
			};

			cci_control1: slave-if@5000 {
				compatible = "arm,cci-400-ctrl-if";
				interface-type = "ace";
				reg = <0x5000 0x1000>;
			};

			pmu@9000 {
				compatible = "arm,cci-400-pmu,r1";
				reg = <0x9000 0x5000>;
				interrupts = <GIC_SPI 132 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 133 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 152 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		syscon: syscon@1c00000 {
			compatible = "allwinner,sun8i-a83t-system-controller",
				"syscon";
			reg = <0x01c00000 0x1000>;
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun8i-a83t-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DMA>;
			resets = <&ccu RST_BUS_DMA>;
			#dma-cells = <1>;
		};

		tcon0: lcd-controller@1c0c000 {
			compatible = "allwinner,sun8i-a83t-tcon-lcd";
			reg = <0x01c0c000 0x1000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_TCON0>, <&ccu CLK_TCON0>;
			clock-names = "ahb", "tcon-ch0";
			clock-output-names = "tcon-data-clock";
			#clock-cells = <0>;
			resets = <&ccu RST_BUS_TCON0>, <&ccu RST_BUS_LVDS>;
			reset-names = "lcd", "lvds";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon0_in_mixer0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&mixer0_out_tcon0>;
					};

					tcon0_in_mixer1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&mixer1_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					reg = <1>;
				};
			};
		};

		tcon1: lcd-controller@1c0d000 {
			compatible = "allwinner,sun8i-a83t-tcon-tv";
			reg = <0x01c0d000 0x1000>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_TCON1>, <&ccu CLK_TCON1>;
			clock-names = "ahb", "tcon-ch1";
			resets = <&ccu RST_BUS_TCON1>;
			reset-names = "lcd";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon1_in_mixer0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&mixer0_out_tcon1>;
					};

					tcon1_in_mixer1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&mixer1_out_tcon1>;
					};
				};

				tcon1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon1_out_hdmi: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&hdmi_in_tcon1>;
					};
				};
			};
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun8i-a83t-mmc",
				     "allwinner,sun7i-a20-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC0>,
				 <&ccu CLK_MMC0>,
				 <&ccu CLK_MMC0_OUTPUT>,
				 <&ccu CLK_MMC0_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC0>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun8i-a83t-mmc",
				     "allwinner,sun7i-a20-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC1>,
				 <&ccu CLK_MMC1>,
				 <&ccu CLK_MMC1_OUTPUT>,
				 <&ccu CLK_MMC1_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC1>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun8i-a83t-emmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC2>,
				 <&ccu CLK_MMC2>,
				 <&ccu CLK_MMC2_OUTPUT>,
				 <&ccu CLK_MMC2_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC2>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		sid: eeprom@1c14000 {
			compatible = "allwinner,sun8i-a83t-sid";
			reg = <0x1c14000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;

			ths_calibration: thermal-sensor-calibration@34 {
				reg = <0x34 8>;
			};
		};

		crypto: crypto@1c15000 {
			compatible = "allwinner,sun8i-a83t-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&ccu RST_BUS_SS>;
			clocks = <&ccu CLK_BUS_SS>, <&ccu CLK_SS>;
			clock-names = "bus", "mod";
		};

		msgbox: mailbox@1c17000 {
			compatible = "allwinner,sun8i-a83t-msgbox",
				     "allwinner,sun6i-a31-msgbox";
			reg = <0x01c17000 0x1000>;
			clocks = <&ccu CLK_BUS_MSGBOX>;
			resets = <&ccu RST_BUS_MSGBOX>;
			interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <1>;
		};

		usb_otg: usb@1c19000 {
			compatible = "allwinner,sun8i-a83t-musb",
				     "allwinner,sun8i-a33-musb";
			reg = <0x01c19000 0x0400>;
			clocks = <&ccu CLK_BUS_OTG>;
			resets = <&ccu RST_BUS_OTG>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			dr_mode = "otg";
			status = "disabled";
		};

		usbphy: phy@1c19400 {
			compatible = "allwinner,sun8i-a83t-usb-phy";
			reg = <0x01c19400 0x10>,
			      <0x01c1a800 0x14>,
			      <0x01c1b800 0x14>;
			reg-names = "phy_ctrl",
				    "pmu1",
				    "pmu2";
			clocks = <&ccu CLK_USB_PHY0>,
				 <&ccu CLK_USB_PHY1>,
				 <&ccu CLK_USB_HSIC>,
				 <&ccu CLK_USB_HSIC_12M>;
			clock-names = "usb0_phy",
				      "usb1_phy",
				      "usb2_phy",
				      "usb2_hsic_12M";
			resets = <&ccu RST_USB_PHY0>,
				 <&ccu RST_USB_PHY1>,
				 <&ccu RST_USB_HSIC>;
			reset-names = "usb0_reset",
				      "usb1_reset",
				      "usb2_reset";
			status = "disabled";
			#phy-cells = <1>;
		};

		ehci0: usb@1c1a000 {
			compatible = "allwinner,sun8i-a83t-ehci",
				     "generic-ehci";
			reg = <0x01c1a000 0x100>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI0>;
			resets = <&ccu RST_BUS_EHCI0>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci0: usb@1c1a400 {
			compatible = "allwinner,sun8i-a83t-ohci",
				     "generic-ohci";
			reg = <0x01c1a400 0x100>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_OHCI0>, <&ccu CLK_USB_OHCI0>;
			resets = <&ccu RST_BUS_OHCI0>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ehci1: usb@1c1b000 {
			compatible = "allwinner,sun8i-a83t-ehci",
				     "generic-ehci";
			reg = <0x01c1b000 0x100>;
			interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI1>;
			resets = <&ccu RST_BUS_EHCI1>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		ccu: clock@1c20000 {
			compatible = "allwinner,sun8i-a83t-ccu";
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&osc16Md512>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			compatible = "allwinner,sun8i-a83t-pinctrl";
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
			reg = <0x01c20800 0x400>;
			clocks = <&ccu CLK_BUS_PIO>, <&osc24M>, <&osc16Md512>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			/omit-if-no-ref/
			csi_8bit_parallel_pins: csi-8bit-parallel-pins {
				pins = "PE0", "PE2", "PE3", "PE6", "PE7",
				       "PE8", "PE9", "PE10", "PE11",
				       "PE12", "PE13";
				function = "csi";
			};

			/omit-if-no-ref/
			csi_mclk_pin: csi-mclk-pin {
				pins = "PE1";
				function = "csi";
			};

			emac_rgmii_pins: emac-rgmii-pins {
				pins = "PD2", "PD3", "PD4", "PD5", "PD6", "PD7",
				       "PD11", "PD12", "PD13", "PD14", "PD18",
				       "PD19", "PD21", "PD22", "PD23";
				function = "gmac";
				/*
				 * data lines in RGMII mode use DDR mode
				 * and need a higher signal drive strength
				 */
				drive-strength = <40>;
			};

			hdmi_pins: hdmi-pins {
				pins = "PH6", "PH7", "PH8";
				function = "hdmi";
			};

			i2c0_pins: i2c0-pins {
				pins = "PH0", "PH1";
				function = "i2c0";
			};

			i2c1_pins: i2c1-pins {
				pins = "PH2", "PH3";
				function = "i2c1";
			};

			/omit-if-no-ref/
			i2c2_pe_pins: i2c2-pe-pins {
				pins = "PE14", "PE15";
				function = "i2c2";
			};

			i2c2_ph_pins: i2c2-ph-pins {
				pins = "PH4", "PH5";
				function = "i2c2";
			};

			i2s1_pins: i2s1-pins {
				/* I2S1 does not have external MCLK pin */
				pins = "PG10", "PG11", "PG12", "PG13";
				function = "i2s1";
			};

			lcd_lvds_pins: lcd-lvds-pins {
				pins = "PD18", "PD19", "PD20", "PD21", "PD22",
				       "PD23", "PD24", "PD25", "PD26", "PD27";
				function = "lvds0";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2",
				       "PF3", "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc1_pins: mmc1-pins {
				pins = "PG0", "PG1", "PG2",
				       "PG3", "PG4", "PG5";
				function = "mmc1";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_8bit_emmc_pins: mmc2-8bit-emmc-pins {
				pins = "PC5", "PC6", "PC8", "PC9",
				       "PC10", "PC11", "PC12", "PC13",
				       "PC14", "PC15", "PC16";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			pwm_pin: pwm-pin {
				pins = "PD28";
				function = "pwm";
			};

			spdif_tx_pin: spdif-tx-pin {
				pins = "PE18";
				function = "spdif";
			};

			uart0_pb_pins: uart0-pb-pins {
				pins = "PB9", "PB10";
				function = "uart0";
			};

			uart0_pf_pins: uart0-pf-pins {
				pins = "PF2", "PF4";
				function = "uart0";
			};

			uart1_pins: uart1-pins {
				pins = "PG6", "PG7";
				function = "uart1";
			};

			uart1_rts_cts_pins: uart1-rts-cts-pins {
				pins = "PG8", "PG9";
				function = "uart1";
			};

			/omit-if-no-ref/
			uart2_pb_pins: uart2-pb-pins {
				pins = "PB0", "PB1";
				function = "uart2";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun8i-a23-timer";
			reg = <0x01c20c00 0xa0>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		watchdog@1c20ca0 {
			compatible = "allwinner,sun6i-a31-wdt";
			reg = <0x01c20ca0 0x20>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		spdif: spdif@1c21000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-a83t-spdif",
				     "allwinner,sun8i-h3-spdif";
			reg = <0x01c21000 0x400>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPDIF>, <&ccu CLK_SPDIF>;
			resets = <&ccu RST_BUS_SPDIF>;
			clock-names = "apb", "spdif";
			dmas = <&dma 2>;
			dma-names = "tx";
			pinctrl-names = "default";
			pinctrl-0 = <&spdif_tx_pin>;
			status = "disabled";
		};

		i2s0: i2s@1c22000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-a83t-i2s";
			reg = <0x01c22000 0x400>;
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S0>, <&ccu CLK_I2S0>;
			clock-names = "apb", "mod";
			dmas = <&dma 3>, <&dma 3>;
			resets = <&ccu RST_BUS_I2S0>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s1: i2s@1c22400 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-a83t-i2s";
			reg = <0x01c22400 0x400>;
			interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S1>, <&ccu CLK_I2S1>;
			clock-names = "apb", "mod";
			dmas = <&dma 4>, <&dma 4>;
			resets = <&ccu RST_BUS_I2S1>;
			dma-names = "rx", "tx";
			pinctrl-names = "default";
			pinctrl-0 = <&i2s1_pins>;
			status = "disabled";
		};

		i2s2: i2s@1c22800 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-a83t-i2s";
			reg = <0x01c22800 0x400>;
			interrupts = <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S2>, <&ccu CLK_I2S2>;
			clock-names = "apb", "mod";
			dmas = <&dma 27>;
			resets = <&ccu RST_BUS_I2S2>;
			dma-names = "tx";
			status = "disabled";
		};

		pwm: pwm@1c21400 {
			compatible = "allwinner,sun8i-a83t-pwm",
				     "allwinner,sun8i-h3-pwm";
			reg = <0x01c21400 0x400>;
			clocks = <&osc24M>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART0>;
			resets = <&ccu RST_BUS_UART0>;
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART1>;
			resets = <&ccu RST_BUS_UART1>;
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART2>;
			resets = <&ccu RST_BUS_UART2>;
			status = "disabled";
		};

		uart3: serial@1c28c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28c00 0x400>;
			interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART3>;
			resets = <&ccu RST_BUS_UART3>;
			status = "disabled";
		};

		uart4: serial@1c29000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29000 0x400>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART4>;
			resets = <&ccu RST_BUS_UART4>;
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun8i-a83t-i2c",
				     "allwinner,sun6i-a31-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C0>;
			resets = <&ccu RST_BUS_I2C0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun8i-a83t-i2c",
				     "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C1>;
			resets = <&ccu RST_BUS_I2C1>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@1c2b400 {
			compatible = "allwinner,sun8i-a83t-i2c",
				     "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b400 0x400>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C2>;
			resets = <&ccu RST_BUS_I2C2>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		emac: ethernet@1c30000 {
			compatible = "allwinner,sun8i-a83t-emac";
			syscon = <&syscon>;
			reg = <0x01c30000 0x104>;
			interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq";
			clocks = <&ccu CLK_BUS_EMAC>;
			clock-names = "stmmaceth";
			resets = <&ccu RST_BUS_EMAC>;
			reset-names = "stmmaceth";
			status = "disabled";

			mdio: mdio {
				compatible = "snps,dwmac-mdio";
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};

		gic: interrupt-controller@1c81000 {
			compatible = "arm,gic-400";
			reg = <0x01c81000 0x1000>,
			      <0x01c82000 0x2000>,
			      <0x01c84000 0x2000>,
			      <0x01c86000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		csi: camera@1cb0000 {
			compatible = "allwinner,sun8i-a83t-csi";
			reg = <0x01cb0000 0x1000>;
			interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CSI>,
				 <&ccu CLK_CSI_SCLK>,
				 <&ccu CLK_DRAM_CSI>;
			clock-names = "bus", "mod", "ram";
			resets = <&ccu RST_BUS_CSI>;
			status = "disabled";
		};

		hdmi: hdmi@1ee0000 {
			compatible = "allwinner,sun8i-a83t-dw-hdmi";
			reg = <0x01ee0000 0x10000>;
			reg-io-width = <1>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_HDMI>, <&ccu CLK_HDMI_SLOW>,
				 <&ccu CLK_HDMI>;
			clock-names = "iahb", "isfr", "tmds";
			resets = <&ccu RST_BUS_HDMI1>;
			reset-names = "ctrl";
			phys = <&hdmi_phy>;
			phy-names = "phy";
			pinctrl-names = "default";
			pinctrl-0 = <&hdmi_pins>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi_in: port@0 {
					reg = <0>;

					hdmi_in_tcon1: endpoint {
						remote-endpoint = <&tcon1_out_hdmi>;
					};
				};

				hdmi_out: port@1 {
					reg = <1>;
				};
			};
		};

		hdmi_phy: hdmi-phy@1ef0000 {
			compatible = "allwinner,sun8i-a83t-hdmi-phy";
			reg = <0x01ef0000 0x10000>;
			clocks = <&ccu CLK_BUS_HDMI>, <&ccu CLK_HDMI_SLOW>;
			clock-names = "bus", "mod";
			resets = <&ccu RST_BUS_HDMI0>;
			reset-names = "phy";
			#phy-cells = <0>;
		};

		r_intc: interrupt-controller@1f00c00 {
			compatible = "allwinner,sun8i-a83t-r-intc",
				     "allwinner,sun6i-a31-r-intc";
			interrupt-controller;
			#interrupt-cells = <3>;
			reg = <0x01f00c00 0x400>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		};

		r_ccu: clock@1f01400 {
			compatible = "allwinner,sun8i-a83t-r-ccu";
			reg = <0x01f01400 0x400>;
			clocks = <&osc24M>, <&osc16Md512>, <&osc16M>,
				 <&ccu CLK_PLL_PERIPH>;
			clock-names = "hosc", "losc", "iosc", "pll-periph";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		cpucfg@1f01c00 {
			compatible = "allwinner,sun8i-a83t-r-cpucfg";
			reg = <0x1f01c00 0x400>;
		};

		r_cir: ir@1f02000 {
			compatible = "allwinner,sun8i-a83t-ir",
				"allwinner,sun6i-a31-ir";
			clocks = <&r_ccu CLK_APB0_IR>, <&r_ccu CLK_IR>;
			clock-names = "apb", "ir";
			resets = <&r_ccu RST_APB0_IR>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			reg = <0x01f02000 0x400>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_cir_pin>;
			status = "disabled";
		};

		r_lradc: lradc@1f03c00 {
			compatible = "allwinner,sun8i-a83t-r-lradc";
			reg = <0x01f03c00 0x100>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		r_pio: pinctrl@1f02c00 {
			compatible = "allwinner,sun8i-a83t-r-pinctrl";
			reg = <0x01f02c00 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&r_ccu CLK_APB0_PIO>, <&osc24M>,
				 <&osc16Md512>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			#gpio-cells = <3>;
			interrupt-controller;
			#interrupt-cells = <3>;

			r_cir_pin: r-cir-pin {
				pins = "PL12";
				function = "s_cir_rx";
			};

			r_rsb_pins: r-rsb-pins {
				pins = "PL0", "PL1";
				function = "s_rsb";
				drive-strength = <20>;
				bias-pull-up;
			};
		};

		r_rsb: rsb@1f03400 {
			compatible = "allwinner,sun8i-a83t-rsb",
				     "allwinner,sun8i-a23-rsb";
			reg = <0x01f03400 0x400>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&r_ccu CLK_APB0_RSB>;
			clock-frequency = <3000000>;
			resets = <&r_ccu RST_APB0_RSB>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_rsb_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		ths: thermal-sensor@1f04000 {
			compatible = "allwinner,sun8i-a83t-ths";
			reg = <0x01f04000 0x100>;
			interrupts = <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
			nvmem-cells = <&ths_calibration>;
			nvmem-cell-names = "calibration";
			#thermal-sensor-cells = <1>;
		};
	};

	thermal-zones {
		cpu0_thermal: cpu0-thermal {
			polling-delay-passive = <0>;
			polling-delay = <0>;
			thermal-sensors = <&ths 0>;

			trips {
				cpu0_hot: cpu-hot {
					temperature = <80000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu0_very_hot: cpu-very-hot {
					temperature = <100000>;
					hysteresis = <0>;
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&cpu0_hot>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};

		cpu1_thermal: cpu1-thermal {
			polling-delay-passive = <0>;
			polling-delay = <0>;
			thermal-sensors = <&ths 1>;

			trips {
				cpu1_hot: cpu-hot {
					temperature = <80000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu1_very_hot: cpu-very-hot {
					temperature = <100000>;
					hysteresis = <0>;
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&cpu1_hot>;
					cooling-device = <&cpu100 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu101 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu102 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu103 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};

		gpu_thermal: gpu-thermal {
			polling-delay-passive = <0>;
			polling-delay = <0>;
			thermal-sensors = <&ths 2>;
		};
	};
};
