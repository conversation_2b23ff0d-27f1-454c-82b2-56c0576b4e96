// SPDX-License-Identifier: GPL-2.0-only OR MIT
// Copyright (C) 2025 <PERSON><PERSON> <<EMAIL>>

/dts-v1/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/linux-event-codes.h>
#include <dt-bindings/leds/common.h>

#include "meson8.dtsi"

/ {
	model = "Fernsehfee 3.0";
	compatible = "tcu,fernsehfee3", "amlogic,meson8";

	aliases {
		serial0 = &uart_AO;
		gpiochip0 = &gpio;
		gpiochip1 = &gpio_ao;
		i2c0 = &i2c_AO;
		i2c1 = &i2c_B;
		mmc0 = &sdhc;
		mmc1 = &sdio;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;  /* 1 GiB */
	};

	gpio-keys {
		compatible = "gpio-keys-polled";
		poll-interval = <100>;

		power-button {
			label = "Power button";
			linux,code = <KEY_POWER>;
			gpios = <&gpio_ao GPIOAO_3 GPIO_ACTIVE_LOW>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			/*
			 * The power LED can be turned red, otherwise it is green.
			 */
			gpios = <&gpio_ao GPIO_TEST_N GPIO_ACTIVE_LOW>;
			function = LED_FUNCTION_POWER;
			color = <LED_COLOR_ID_RED>;
		};
	};

	vcc_5v: regulator-5v {
		/* 5V rail, always on as long as the system is running */
		compatible = "regulator-fixed";
		regulator-name = "5V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	vcc_3v3: regulator-3v3 {
		/* Chipown AP2420 step-down converter */
		compatible = "regulator-fixed";
		regulator-name = "3.3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&vcc_5v>;
	};

	wifi_3v3: regulator-wifi {
		compatible = "regulator-fixed";
		regulator-name = "3.3V-WIFI";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&vcc_3v3>;
		gpio = <&gpio GPIOX_11 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};
};

&cpu0 {
	cpu-supply = <&vcck>;
};

&ethmac {
	status = "okay";
	pinctrl-0 = <&eth_pins>;
	pinctrl-names = "default";
	phy-handle = <&eth_phy0>;
	phy-mode = "rmii";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		eth_phy0: ethernet-phy@0 {
			/* IC Plus IP101A (0x02430c54) */
			reg = <0>;

			reset-assert-us = <10000>;
			reset-deassert-us = <10000>;
			reset-gpios = <&gpio GPIOH_4 GPIO_ACTIVE_LOW>;
		};
	};
};

&i2c_AO {
	status = "okay";
	pinctrl-0 = <&i2c_ao_pins>;
	pinctrl-names = "default";

	pmic@32 {
		compatible = "ricoh,rn5t618";
		reg = <0x32>;
		system-power-controller;

		regulators {
			vcck: DCDC1 {
				regulator-name = "VCCK";
				regulator-min-microvolt = <825000>;
				regulator-max-microvolt = <1150000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vddee: DCDC2 {
				/* the output is also used as VDDAO */
				regulator-name = "VDD_EE";
				regulator-min-microvolt = <950000>;
				regulator-max-microvolt = <1150000>;
				regulator-boot-on;
				regulator-always-on;
			};

			DCDC3 {
				regulator-name = "VDD_DDR";
				regulator-min-microvolt = <1500000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDO1 {
				regulator-name = "VDDIO_AO28";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDO2 {
				regulator-name = "VDDIO_AO18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vcc1v8_usb: LDO3 {
				regulator-name = "VCC1V8_USB";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-boot-on;
			};

			LDO4 {
				/* This one appears to be unused */
				regulator-name = "VCC2V8";
				regulator-min-microvolt = <2850000>;
				regulator-max-microvolt = <2850000>;
			};

			LDO5 {
				regulator-name = "AVDD1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDORTC1 {
				regulator-name = "VDD_LDO";
				regulator-min-microvolt = <2700000>;
				regulator-max-microvolt = <2700000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDORTC2 {
				regulator-name = "RTC_0V9";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <900000>;
				regulator-boot-on;
				regulator-always-on;
			};
		};
	};

	eeprom@50 {
		/* Fairchild FM24C08A */
		compatible = "atmel,24c08";
		reg = <0x50>;
		pagesize = <16>;
		wp-gpios = <&gpio GPIOH_3 GPIO_ACTIVE_HIGH>;
		num-addresses = <4>;
	};
};

&i2c_B {
	status = "okay";
	pinctrl-0 = <&i2c_b_pins>;
	pinctrl-names = "default";

	/* TODO: SiI9293 HDMI receiver @ 0x39 */
};

&mali {
	mali-supply = <&vddee>;
};

&sdhc {
	status = "okay";
	pinctrl-0 = <&sdxc_c_pins>;
	pinctrl-names = "default";

	/* eMMC */
	bus-width = <8>;
	max-frequency = <100000000>;

	disable-wp;
	cap-mmc-highspeed;
	mmc-hs200-1_8v;
	no-sdio;

	vmmc-supply = <&vcc_3v3>;
	vqmmc-supply = <&vcc_3v3>;
};

&sdio {
	status = "okay";
	pinctrl-0 = <&sd_b_pins>;

	/* SD card */
	slot@1 {
		compatible = "mmc-slot";
		reg = <1>;
		status = "okay";

		bus-width = <4>;
		cap-mmc-highspeed;
		cap-sd-highspeed;
		disable-wp;

		cd-gpios = <&gpio CARD_6 GPIO_ACTIVE_LOW>;

		vmmc-supply = <&vcc_3v3>;
	};
};

&uart_AO {
	status = "okay";
	pinctrl-0 = <&uart_ao_a_pins>;
	pinctrl-names = "default";
};

&usb0 {
	status = "okay";
};

&usb0_phy {
	status = "okay";
	phy-supply = <&vcc1v8_usb>;
};

&usb1 {
	status = "okay";
	dr_mode = "host";
	#address-cells = <1>;
	#size-cells = <0>;

	wifi: wifi@1 {
		/* Realtek RTL8188 2.4GHz WiFi module */
		compatible = "usbbda,179";
		reg = <1>;
		vdd-supply = <&wifi_3v3>;
	};
};

&usb1_phy {
	status = "okay";
	phy-supply = <&vcc1v8_usb>;
};

&ir_receiver {
	status = "okay";
	pinctrl-0 = <&ir_recv_pins>;
	pinctrl-names = "default";
};
