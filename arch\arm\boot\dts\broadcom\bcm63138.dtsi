// SPDX-License-Identifier: GPL-2.0
/*
 * Broadcom BCM63138 DSL SoCs Device Tree
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm63138", "brcm,bcmbca";
	model = "Broadcom BCM963138 Reference Board";
	interrupt-parent = <&gic>;

	aliases {
		uart0 = &serial0;
		uart1 = &serial1;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0>;
			enable-method = "brcm,bcm63138";
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <1>;
			enable-method = "brcm,bcm63138";
			resets = <&pmb0 4 1>;
		};
	};

	clocks {
		/* UBUS peripheral clock */
		periph_clk: periph_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <50000000>;
			clock-output-names = "periph";
		};

		/* peripheral clock for system timer */
		axi_clk: axi_clk {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clocks = <&armpll>;
			clock-div = <2>;
			clock-mult = <1>;
		};

		/* APB bus clock */
		apb_clk: apb_clk {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clocks = <&armpll>;
			clock-div = <4>;
			clock-mult = <1>;
		};

		hsspi_pll: hsspi-pll {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <400000000>;
		};
	};

	/* ARM bus */
	axi@80000000 {
		compatible = "simple-bus";
		ranges = <0 0x80000000 0x784000>;
		#address-cells = <1>;
		#size-cells = <1>;

		L2: cache-controller@1d000 {
			compatible = "arm,pl310-cache";
			reg = <0x1d000 0x1000>;
			cache-unified;
			cache-level = <2>;
			cache-size = <524288>;
			cache-sets = <1024>;
			cache-line-size = <32>;
			interrupts = <GIC_PPI 0 IRQ_TYPE_LEVEL_HIGH>;
		};

		scu: scu@1e000 {
			compatible = "arm,cortex-a9-scu";
			reg = <0x1e000 0x100>;
		};

		gic: interrupt-controller@1f000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0x1f000 0x1000
				0x1e100 0x100>;
			#interrupt-cells = <3>;
			#address-cells = <0>;
			interrupt-controller;
		};

		global_timer: timer@1e200 {
			compatible = "arm,cortex-a9-global-timer";
			reg = <0x1e200 0x20>;
			interrupts = <GIC_PPI 11 IRQ_TYPE_EDGE_RISING>;
			clocks = <&axi_clk>;
		};

		local_timer: local-timer@1e600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0x1e600 0x20>;
			interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2) |
						  IRQ_TYPE_EDGE_RISING)>;
			clocks = <&axi_clk>;
		};

		twd_watchdog: watchdog@1e620 {
			compatible = "arm,cortex-a9-twd-wdt";
			reg = <0x1e620 0x20>;
			interrupts = <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2) |
						  IRQ_TYPE_LEVEL_HIGH)>;
		};

		armpll: armpll@20000 {
			#clock-cells = <0>;
			compatible = "brcm,bcm63138-armpll";
			clocks = <&periph_clk>;
			reg = <0x20000 0xf00>;
		};

		pmb0: reset-controller@4800c0 {
			compatible = "brcm,bcm63138-pmb";
			reg = <0x4800c0 0x10>;
			#reset-cells = <2>;
		};

		pmb1: reset-controller@4800e0 {
			compatible = "brcm,bcm63138-pmb";
			reg = <0x4800e0 0x10>;
			#reset-cells = <2>;
		};

		ahci: sata@a000 {
			compatible = "brcm,bcm63138-ahci", "brcm,sata3-ahci";
			reg-names = "ahci", "top-ctrl";
			reg = <0xa000 0x9ac>, <0x8040 0x24>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			resets = <&pmb0 3 1>;
			reset-names = "ahci";
			status = "disabled";

			sata0: sata-port@0 {
				reg = <0>;
				phys = <&sata_phy0>;
			};
		};

		sata_phy: sata-phy@8100 {
			compatible = "brcm,bcm63138-sata-phy", "brcm,phy-sata3";
			reg = <0x8100 0x1e00>;
			reg-names = "phy";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			sata_phy0: sata-phy@0 {
				reg = <0>;
				#phy-cells = <0>;
			};
		};
	};

	/* Legacy UBUS base */
	ubus@fffe8000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xfffe8000 0x8100>;

		timer: timer@80 {
			compatible = "brcm,bcm6328-timer", "syscon";
			reg = <0x80 0x3c>;
		};

		serial0: serial@600 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x600 0x1b>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&periph_clk>;
			clock-names = "periph";
			status = "disabled";
		};

		serial1: serial@620 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x620 0x1b>;
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&periph_clk>;
			clock-names = "periph";
			status = "disabled";
		};

		hsspi: spi@1000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,bcm63138-hsspi", "brcm,bcmbca-hsspi-v1.0";
			reg = <0x1000 0x600>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&hsspi_pll &hsspi_pll>;
			clock-names = "hsspi", "pll";
			num-cs = <8>;
			status = "disabled";
		};

		nand_controller: nand-controller@2000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,nand-bcm63138", "brcm,brcmnand-v7.0", "brcm,brcmnand";
			reg = <0x2000 0x600>, <0xf0 0x10>;
			reg-names = "nand", "nand-int-base";
			status = "disabled";
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "nand_ctlrdy";

			nandcs: nand@0 {
				compatible = "brcm,nandcs";
				reg = <0>;
			};
		};

		serial@4400 {
			compatible = "brcm,bcm63138-hs-uart", "brcm,bcmbca-hs-uart";
			reg = <0x4400 0x1e0>;
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
		};

		bootlut: bootlut@8000 {
			compatible = "brcm,bcm63138-bootlut";
			reg = <0x8000 0x50>;
		};

		reboot {
			compatible = "syscon-reboot";
			regmap = <&timer>;
			offset = <0x34>;
			mask = <1>;
		};
	};
};
