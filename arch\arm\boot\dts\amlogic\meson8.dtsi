// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright 2014 <PERSON> <<EMAIL>>
 */

#include <dt-bindings/clock/meson8-ddr-clkc.h>
#include <dt-bindings/clock/meson8b-clkc.h>
#include <dt-bindings/gpio/meson8-gpio.h>
#include <dt-bindings/power/meson8-power.h>
#include <dt-bindings/reset/amlogic,meson8b-clkc-reset.h>
#include <dt-bindings/reset/amlogic,meson8b-reset.h>
#include <dt-bindings/thermal/thermal.h>
#include "meson.dtsi"

/ {
	model = "Amlogic Meson8 SoC";
	compatible = "amlogic,meson8";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@200 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x200>;
			enable-method = "amlogic,meson8-smp";
			resets = <&clkc CLKC_RESET_CPU0_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu1: cpu@201 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x201>;
			enable-method = "amlogic,meson8-smp";
			resets = <&clkc CLKC_RESET_CPU1_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu2: cpu@202 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x202>;
			enable-method = "amlogic,meson8-smp";
			resets = <&clkc CLKC_RESET_CPU2_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu3: cpu@203 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x203>;
			enable-method = "amlogic,meson8-smp";
			resets = <&clkc CLKC_RESET_CPU3_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};
	};

	cpu_opp_table: opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		opp-96000000 {
			opp-hz = /bits/ 64 <96000000>;
			opp-microvolt = <825000>;
		};
		opp-192000000 {
			opp-hz = /bits/ 64 <192000000>;
			opp-microvolt = <825000>;
		};
		opp-312000000 {
			opp-hz = /bits/ 64 <312000000>;
			opp-microvolt = <825000>;
		};
		opp-408000000 {
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <825000>;
		};
		opp-504000000 {
			opp-hz = /bits/ 64 <504000000>;
			opp-microvolt = <825000>;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <850000>;
		};
		opp-720000000 {
			opp-hz = /bits/ 64 <720000000>;
			opp-microvolt = <850000>;
		};
		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <875000>;
		};
		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <925000>;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <975000>;
		};
		opp-1416000000 {
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <1025000>;
		};
		opp-1608000000 {
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <1100000>;
		};
		opp-1800000000 {
			status = "disabled";
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <1125000>;
		};
		opp-1992000000 {
			status = "disabled";
			opp-hz = /bits/ 64 <1992000000>;
			opp-microvolt = <1150000>;
		};
	};

	gpu_opp_table: opp-table-gpu {
		compatible = "operating-points-v2";

		opp-182142857 {
			opp-hz = /bits/ 64 <182142857>;
			opp-microvolt = <1150000>;
		};
		opp-318750000 {
			opp-hz = /bits/ 64 <318750000>;
			opp-microvolt = <1150000>;
		};
		opp-425000000 {
			opp-hz = /bits/ 64 <425000000>;
			opp-microvolt = <1150000>;
		};
		opp-510000000 {
			opp-hz = /bits/ 64 <510000000>;
			opp-microvolt = <1150000>;
		};
		opp-637500000 {
			opp-hz = /bits/ 64 <637500000>;
			opp-microvolt = <1150000>;
			turbo-mode;
		};
	};

	pmu {
		compatible = "arm,cortex-a9-pmu";
		interrupts = <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		/* 2 MiB reserved for Hardware ROM Firmware? */
		hwrom@0 {
			reg = <0x0 0x200000>;
			no-map;
		};

		/*
		 * 1 MiB reserved for the "ARM Power Firmware": this is ARM
		 * code which is responsible for system suspend. It loads a
		 * piece of ARC code ("arc_power" in the vendor u-boot tree)
		 * into SRAM, executes that and shuts down the (last) ARM core.
		 * The arc_power firmware then checks various wakeup sources
		 * (IR remote receiver, HDMI CEC, WIFI and Bluetooth wakeup or
		 * simply the power key) and re-starts the ARM core once it
		 * detects a wakeup request.
		 */
		power-firmware@4f00000 {
			reg = <0x4f00000 0x100000>;
			no-map;
		};
	};

	thermal-zones {
		soc-thermal {
			polling-delay-passive = <250>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&thermal_sensor>;

			cooling-maps {
				map0 {
					trip = <&soc_passive>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&mali THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};

				map1 {
					trip = <&soc_hot>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&mali THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};

			trips {
				soc_passive: soc-passive {
					temperature = <80000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "passive";
				};

				soc_hot: soc-hot {
					temperature = <90000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "hot";
				};

				soc_critical: soc-critical {
					temperature = <110000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "critical";
				};
			};
		};
	};

	mmcbus: bus@c8000000 {
		compatible = "simple-bus";
		reg = <0xc8000000 0x8000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0xc8000000 0x8000>;

		ddr_clkc: clock-controller@400 {
			compatible = "amlogic,meson8-ddr-clkc";
			reg = <0x400 0x20>;
			clocks = <&xtal>;
			clock-names = "xtal";
			#clock-cells = <1>;
		};

		dmcbus: bus@6000 {
			compatible = "simple-bus";
			reg = <0x6000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x6000 0x400>;

			canvas: video-lut@20 {
				compatible = "amlogic,meson8-canvas",
					     "amlogic,canvas";
				reg = <0x20 0x14>;
			};
		};
	};

	apb: bus@d0000000 {
		compatible = "simple-bus";
		reg = <0xd0000000 0x200000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0xd0000000 0x200000>;

		mali: gpu@c0000 {
			compatible = "amlogic,meson8-mali", "arm,mali-450";
			reg = <0xc0000 0x40000>;
			interrupts = <GIC_SPI 160 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 161 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 162 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 164 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 165 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 166 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 167 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 168 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 169 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 172 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 173 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 176 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gp", "gpmmu", "pp", "pmu",
					  "pp0", "ppmmu0", "pp1", "ppmmu1",
					  "pp2", "ppmmu2", "pp4", "ppmmu4",
					  "pp5", "ppmmu5", "pp6", "ppmmu6";
			resets = <&reset RESET_MALI>;

			clocks = <&clkc CLKID_CLK81>, <&clkc CLKID_MALI>;
			clock-names = "bus", "core";

			assigned-clocks = <&clkc CLKID_MALI>;
			assigned-clock-rates = <318750000>;

			operating-points-v2 = <&gpu_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};
	};
}; /* end of / */

&aiu {
	compatible = "amlogic,aiu-meson8", "amlogic,aiu";
	clocks = <&clkc CLKID_AIU_GLUE>,
		 <&clkc CLKID_I2S_OUT>,
		 <&clkc CLKID_AOCLK_GATE>,
		 <&clkc CLKID_CTS_AMCLK>,
		 <&clkc CLKID_MIXER_IFACE>,
		 <&clkc CLKID_IEC958>,
		 <&clkc CLKID_IEC958_GATE>,
		 <&clkc CLKID_CTS_MCLK_I958>,
		 <&clkc CLKID_CTS_I958>;
	clock-names = "pclk",
		      "i2s_pclk",
		      "i2s_aoclk",
		      "i2s_mclk",
		      "i2s_mixer",
		      "spdif_pclk",
		      "spdif_aoclk",
		      "spdif_mclk",
		      "spdif_mclk_sel";
	resets = <&reset RESET_AIU>;
};

&aobus {
	pmu: pmu@e0 {
		compatible = "amlogic,meson8-pmu", "syscon";
		reg = <0xe0 0x18>;
	};

	pinctrl_aobus: pinctrl@14 {
		compatible = "amlogic,meson8-aobus-pinctrl";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x14 0x1c>;

		gpio_ao: bank@0 {
			reg = <0x0 0x4>,
			      <0x18 0x4>,
			      <0x10 0x8>;
			reg-names = "mux", "pull", "gpio";
			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl_aobus 0 0 16>;
		};

		i2s_am_clk_pins: i2s-am-clk-out {
			mux {
				groups = "i2s_am_clk_out_ao";
				function = "i2s_ao";
				bias-disable;
			};
		};

		i2s_out_ao_clk_pins: i2s-ao-clk-out {
			mux {
				groups = "i2s_ao_clk_out_ao";
				function = "i2s_ao";
				bias-disable;
			};
		};

		i2s_out_lr_clk_pins: i2s-lr-clk-out {
			mux {
				groups = "i2s_lr_clk_out_ao";
				function = "i2s_ao";
				bias-disable;
			};
		};

		i2s_out_ch01_ao_pins: i2s-out-ch01 {
			mux {
				groups = "i2s_out_ch01_ao";
				function = "i2s_ao";
				bias-disable;
			};
		};

		uart_ao_a_pins: uart_ao_a {
			mux {
				groups = "uart_tx_ao_a", "uart_rx_ao_a";
				function = "uart_ao";
				bias-pull-up;
			};
		};

		i2c_ao_pins: i2c_mst_ao {
			mux {
				groups = "i2c_mst_sck_ao", "i2c_mst_sda_ao";
				function = "i2c_mst_ao";
				bias-disable;
			};
		};

		ir_recv_pins: remote {
			mux {
				groups = "remote_input";
				function = "remote";
				bias-disable;
			};
		};

		pwm_f_ao_pins: pwm-f-ao {
			mux {
				groups = "pwm_f_ao";
				function = "pwm_f_ao";
				bias-disable;
			};
		};
	};
};

&ao_arc_rproc {
	compatible = "amlogic,meson8-ao-arc", "amlogic,meson-mx-ao-arc";
	amlogic,secbus2 = <&secbus2>;
	sram = <&ao_arc_sram>;
	resets = <&reset RESET_MEDIA_CPU>;
	clocks = <&clkc CLKID_AO_MEDIA_CPU>;
};

&cbus {
	reset: reset-controller@4404 {
		compatible = "amlogic,meson8b-reset";
		reg = <0x4404 0x9c>;
		#reset-cells = <1>;
	};

	analog_top: analog-top@81a8 {
		compatible = "amlogic,meson8-analog-top", "syscon";
		reg = <0x81a8 0x14>;
	};

	pwm_ef: pwm@86c0 {
		compatible = "amlogic,meson8-pwm-v2";
		clocks = <&xtal>,
			 <0>, /* unknown/untested, the datasheet calls it "Video PLL" */
			 <&clkc CLKID_FCLK_DIV4>,
			 <&clkc CLKID_FCLK_DIV3>;
		reg = <0x86c0 0x10>;
		#pwm-cells = <3>;
		status = "disabled";
	};

	clock-measure@8758 {
		compatible = "amlogic,meson8-clk-measure";
		reg = <0x8758 0x1c>;
	};

	pinctrl_cbus: pinctrl@8030 {
		compatible = "amlogic,meson8-cbus-pinctrl";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x8030 0x108>;

		gpio: bank@80 {
			reg = <0x80 0x28>,
			      <0xb8 0x18>,
			      <0xf0 0x18>,
			      <0x00 0x30>;
			reg-names = "mux", "pull", "pull-enable", "gpio";
			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl_cbus 0 0 120>;
		};

		i2c_b_pins: i2c-b {
			mux {
				groups = "i2c_sda_b", "i2c_sck_b";
				function = "i2c_b";
				bias-disable;
			};
		};

		sd_a_pins: sd-a {
			mux {
				groups = "sd_d0_a", "sd_d1_a", "sd_d2_a",
					"sd_d3_a", "sd_clk_a", "sd_cmd_a";
				function = "sd_a";
				bias-disable;
			};
		};

		sd_b_pins: sd-b {
			mux {
				groups = "sd_d0_b", "sd_d1_b", "sd_d2_b",
					"sd_d3_b", "sd_clk_b", "sd_cmd_b";
				function = "sd_b";
				bias-disable;
			};
		};

		sd_c_pins: sd-c {
			mux {
				groups = "sd_d0_c", "sd_d1_c", "sd_d2_c",
					"sd_d3_c", "sd_clk_c", "sd_cmd_c";
				function = "sd_c";
				bias-disable;
			};
		};

		sdxc_a_pins: sdxc-a {
			mux {
				groups = "sdxc_d0_a", "sdxc_d13_a",
					 "sdxc_clk_a", "sdxc_cmd_a";
				function = "sdxc_a";
				bias-pull-up;
			};
		};

		sdxc_b_pins: sdxc-b {
			mux {
				groups = "sdxc_d0_b", "sdxc_d13_b",
					 "sdxc_clk_b", "sdxc_cmd_b";
				function = "sdxc_b";
				bias-pull-up;
			};
		};

		sdxc_c_pins: sdxc-c {
			mux {
				groups = "sdxc_d0_c", "sdxc_d13_c",
					"sdxc_clk_c", "sdxc_cmd_c",
					"sdxc_d47_c";
				function = "sdxc_c";
				bias-pull-up;
			};
		};

		spdif_out_pins: spdif-out {
			mux {
				groups = "spdif_out";
				function = "spdif";
				bias-disable;
			};
		};

		spi_nor_pins: nor {
			mux {
				groups = "nor_d", "nor_q", "nor_c", "nor_cs";
				function = "nor";
				bias-disable;
			};
		};

		eth_pins: ethernet {
			mux {
				groups = "eth_tx_clk_50m", "eth_tx_en",
					 "eth_txd1", "eth_txd0",
					 "eth_rx_clk_in", "eth_rx_dv",
					 "eth_rxd1", "eth_rxd0", "eth_mdio",
					 "eth_mdc";
				function = "ethernet";
				bias-disable;
			};
		};

		pwm_e_pins: pwm-e {
			mux {
				groups = "pwm_e";
				function = "pwm_e";
				bias-disable;
			};
		};

		uart_a1_pins: uart-a1 {
			mux {
				groups = "uart_tx_a1",
				       "uart_rx_a1";
				function = "uart_a";
				bias-pull-up;
			};
		};

		uart_a1_cts_rts_pins: uart-a1-cts-rts {
			mux {
				groups = "uart_cts_a1",
				       "uart_rts_a1";
				function = "uart_a";
				bias-disable;
			};
		};

		xtal_32k_out_pins: xtal-32k-out {
			mux {
				groups = "xtal_32k_out";
				function = "xtal";
				bias-disable;
			};
		};
	};
};

&ahb_sram {
	ao_arc_sram: aoarc-sram@0 {
		compatible = "amlogic,meson8-ao-arc-sram";
		reg = <0x0 0x8000>;
		pool;
	};

	smp-sram@1ff80 {
		compatible = "amlogic,meson8-smp-sram";
		reg = <0x1ff80 0x8>;
	};
};

&efuse {
	compatible = "amlogic,meson8-efuse";
	clocks = <&clkc CLKID_EFUSE>;
	clock-names = "core";

	temperature_calib: calib@1f4 {
		/* only the upper two bytes are relevant */
		reg = <0x1f4 0x4>;
	};
};

&ethmac {
	clocks = <&clkc CLKID_ETH>;
	clock-names = "stmmaceth";

	power-domains = <&pwrc PWRC_MESON8_ETHERNET_MEM_ID>;
};

&gpio_intc {
	compatible = "amlogic,meson8-gpio-intc", "amlogic,meson-gpio-intc";
	status = "okay";
};

&hhi {
	clkc: clock-controller {
		compatible = "amlogic,meson8-clkc";
		clocks = <&xtal>, <&ddr_clkc DDR_CLKID_DDR_PLL>;
		clock-names = "xtal", "ddr_pll";
		#clock-cells = <1>;
		#reset-cells = <1>;
	};

	pwrc: power-controller {
		compatible = "amlogic,meson8-pwrc";
		#power-domain-cells = <1>;
		amlogic,ao-sysctrl = <&pmu>;
		clocks = <&clkc CLKID_VPU>;
		clock-names = "vpu";
		assigned-clocks = <&clkc CLKID_VPU>;
		assigned-clock-rates = <364285714>;
	};
};

&hwrng {
	clocks = <&clkc CLKID_RNG0>;
	clock-names = "core";
};

&i2c_AO {
	clocks = <&clkc CLKID_CLK81>;
};

&i2c_A {
	clocks = <&clkc CLKID_CLK81>;
};

&i2c_B {
	clocks = <&clkc CLKID_CLK81>;
};

&L2 {
	arm,data-latency = <3 3 3>;
	arm,tag-latency = <2 2 2>;
	arm,filter-ranges = <0x100000 0xc0000000>;
	prefetch-data = <1>;
	prefetch-instr = <1>;
	arm,prefetch-offset = <7>;
	arm,double-linefill = <1>;
	arm,prefetch-drop = <1>;
	arm,shared-override;
};

&periph {
	scu@0 {
		compatible = "arm,cortex-a9-scu";
		reg = <0x0 0x100>;
	};

	timer@200 {
		compatible = "arm,cortex-a9-global-timer";
		reg = <0x200 0x20>;
		interrupts = <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
		clocks = <&clkc CLKID_PERIPH>;

		/*
		 * the arm_global_timer driver currently does not handle clock
		 * rate changes. Keep it disabled for now.
		 */
		status = "disabled";
	};

	timer@600 {
		compatible = "arm,cortex-a9-twd-timer";
		reg = <0x600 0x20>;
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
		clocks = <&clkc CLKID_PERIPH>;
	};
};

&pwm_ab {
	compatible = "amlogic,meson8-pwm-v2";
	clocks = <&xtal>,
		 <0>, /* unknown/untested, the datasheet calls it "Video PLL" */
		 <&clkc CLKID_FCLK_DIV4>,
		 <&clkc CLKID_FCLK_DIV3>;
};

&pwm_cd {
	compatible = "amlogic,meson8-pwm-v2";
	clocks = <&xtal>,
		 <0>, /* unknown/untested, the datasheet calls it "Video PLL" */
		 <&clkc CLKID_FCLK_DIV4>,
		 <&clkc CLKID_FCLK_DIV3>;
};

&rtc {
	compatible = "amlogic,meson8-rtc";
	resets = <&reset RESET_RTC>;
};

&saradc {
	compatible = "amlogic,meson8-saradc", "amlogic,meson-saradc";
	clocks = <&xtal>, <&clkc CLKID_SAR_ADC>;
	clock-names = "clkin", "core";
	amlogic,hhi-sysctrl = <&hhi>;
	nvmem-cells = <&temperature_calib>;
	nvmem-cell-names = "temperature_calib";
};

&sdhc {
	compatible = "amlogic,meson8-sdhc", "amlogic,meson-mx-sdhc";
	clocks = <&xtal>,
		 <&clkc CLKID_FCLK_DIV4>,
		 <&clkc CLKID_FCLK_DIV3>,
		 <&clkc CLKID_FCLK_DIV5>,
		 <&clkc CLKID_SDHC>;
	clock-names = "clkin0", "clkin1", "clkin2", "clkin3", "pclk";
};

&secbus {
	secbus2: system-controller@4000 {
		compatible = "amlogic,meson8-secbus2", "syscon";
		reg = <0x4000 0x2000>;
	};
};

&sdio {
	compatible = "amlogic,meson8-sdio", "amlogic,meson-mx-sdio";
	clocks = <&clkc CLKID_SDIO>, <&clkc CLKID_CLK81>;
	clock-names = "core", "clkin";
};

&spifc {
	clocks = <&clkc CLKID_CLK81>;
};

&timer_abcde {
	clocks = <&xtal>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk";
};

&uart_AO {
	compatible = "amlogic,meson8-uart", "amlogic,meson-ao-uart";
	clocks = <&xtal>, <&clkc CLKID_CLK81>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&uart_A {
	compatible = "amlogic,meson8-uart";
	clocks = <&xtal>, <&clkc CLKID_UART0>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&uart_B {
	compatible = "amlogic,meson8-uart";
	clocks = <&xtal>, <&clkc CLKID_UART1>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&uart_C {
	compatible = "amlogic,meson8-uart";
	clocks = <&xtal>, <&clkc CLKID_UART2>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&usb0 {
	compatible = "amlogic,meson8-usb", "snps,dwc2";
	clocks = <&clkc CLKID_USB0_DDR_BRIDGE>;
	clock-names = "otg";
};

&usb1 {
	compatible = "amlogic,meson8-usb", "snps,dwc2";
	clocks = <&clkc CLKID_USB1_DDR_BRIDGE>;
	clock-names = "otg";
};

&usb0_phy {
	compatible = "amlogic,meson8-usb2-phy", "amlogic,meson-mx-usb2-phy";
	clocks = <&clkc CLKID_USB>, <&clkc CLKID_USB0>;
	clock-names = "usb_general", "usb";
	resets = <&reset RESET_USB_OTG>;
};

&usb1_phy {
	compatible = "amlogic,meson8-usb2-phy", "amlogic,meson-mx-usb2-phy";
	clocks = <&clkc CLKID_USB>, <&clkc CLKID_USB1>;
	clock-names = "usb_general", "usb";
	resets = <&reset RESET_USB_OTG>;
};
