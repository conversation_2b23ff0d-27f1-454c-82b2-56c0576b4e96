// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Romulus BMC";
	compatible = "ibm,romulus-bmc", "aspeed,ast2500";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@9f000000 {
			no-map;
			reg = <0x9f000000 0x01000000>; /* 16M */
		};

		flash_memory: region@98000000 {
			no-map;
			reg = <0x98000000 0x04000000>; /* 64M */
		};

		coldfire_memory: codefire_memory@9ef00000 {
			reg = <0x9ef00000 0x00100000>;
			no-map;
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	leds {
		compatible = "gpio-leds";

		fault {
			gpios = <&gpio ASPEED_GPIO(N, 2) GPIO_ACTIVE_LOW>;
		};

		identify {
			gpios = <&gpio ASPEED_GPIO(N, 4) GPIO_ACTIVE_HIGH>;
		};

		power {
			gpios = <&gpio ASPEED_GPIO(R, 5) GPIO_ACTIVE_LOW>;
		};
	};

	fsi: gpio-fsi {
		compatible = "aspeed,ast2500-cf-fsi-master", "fsi-master";
		#address-cells = <2>;
		#size-cells = <0>;
		no-gpio-delays;

		memory-region = <&coldfire_memory>;
		aspeed,sram = <&sram>;
		aspeed,cvic = <&cvic>;

		clock-gpios = <&gpio ASPEED_GPIO(AA, 0) GPIO_ACTIVE_HIGH>;
		data-gpios = <&gpio ASPEED_GPIO(AA, 2) GPIO_ACTIVE_HIGH>;
		mux-gpios = <&gpio ASPEED_GPIO(A, 6) GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio ASPEED_GPIO(D, 0) GPIO_ACTIVE_HIGH>;
		trans-gpios = <&gpio ASPEED_GPIO(R, 2) GPIO_ACTIVE_HIGH>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-checkstop {
			label = "checkstop";
			gpios = <&gpio ASPEED_GPIO(J, 2) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(J, 2)>;
		};

		id-button {
			label = "id-button";
			gpios = <&gpio ASPEED_GPIO(Q, 7) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(Q, 7)>;
		};
	};

	iio-hwmon-battery {
		compatible = "iio-hwmon";
		io-channels = <&adc 12>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-max-frequency = <100000000>;
	};
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&uart1 {
	/* Rear RS-232 connector */
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default
			&pinctrl_nrts1_default
			&pinctrl_ndtr1_default
			&pinctrl_ndsr1_default
			&pinctrl_ncts1_default
			&pinctrl_ndcd1_default
			&pinctrl_nri1_default>;
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	use-ncsi;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	/* PCIe slot 1 (x8) */
	status = "okay";
};

&i2c7 {
	/* PCIe slot 2 (x16) */
	status = "okay";
};

&i2c8 {
	/* PCIe slot 3 (x16) */
	status = "okay";
};

&i2c9 {
	/* PCIe slot 4 (x16) */
	status = "okay";
};

&i2c10 {
	/* PCIe slot 5 (x8) */
	status = "okay";
};

&i2c11 {
	status = "okay";

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};
};

&i2c12 {
	status = "okay";

	w83773g@4c {
		compatible = "nuvoton,w83773g";
		reg = <0x4c>;
	};
};

&gpio {
	gpio-line-names =
	/*A0-A7*/	"","cfam-reset","","","","","fsi-mux","",
	/*B0-B7*/	"","","","","","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"fsi-enable","","","nic_func_mode0","nic_func_mode1","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","","","","","",
	/*I0-I7*/	"","","","power-button","","","","",
	/*J0-J7*/	"","","checkstop","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","led-fault","",
				"led-identify","","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"","","","","","","","",
	/*Q0-Q7*/	"","","","","","","","id-button",
	/*R0-R7*/	"","","fsi-trans","","","led-power","","",
	/*S0-S7*/	"","","","","","","","seq_cont",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","",
	/*AA0-AA7*/	"fsi-clock","","fsi-data","","","","","",
	/*AB0-AB7*/	"","","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";

	nic_func_mode0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(D, 3) GPIO_ACTIVE_HIGH>;
		output-low;
	};
	nic_func_mode1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(D, 4) GPIO_ACTIVE_HIGH>;
		output-low;
	};
	seq_cont {
		gpio-hog;
		gpios = <ASPEED_GPIO(S, 7) GPIO_ACTIVE_HIGH>;
		output-low;
	};
};

&vuart {
	status = "okay";
};

&gfx {
	status = "okay";
	memory-region = <&gfx_memory>;
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x08>;
	};

	fan@1 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x09>;
	};

	fan@2 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0a>;
	};

	fan@3 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0b>;
	};

	fan@4 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0c>;
	};

	fan@5 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0d>;
	};

	fan@6 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0e>;
	};
};

&ibt {
	status = "okay";
};

&vhub {
	status = "okay";
};

&adc {
	status = "okay";
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

#include "ibm-power9-dual.dtsi"
