// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/ {
	model = "ARM Versatile AB";
	compatible = "arm,versatile-ab";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&vic>;

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
		i2c0 = &i2c0;
	};

	chosen {
		stdout-path = &uart0;
	};

	memory {
		device_type = "memory";
		reg = <0x0 0x08000000>;
	};

	xtal24mhz: clock-24000000 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
	};

	bridge {
		compatible = "ti,ths8134b", "ti,ths8134";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				vga_bridge_in: endpoint {
					remote-endpoint = <&clcd_pads_vga_dac>;
				};
			};

			port@1 {
				reg = <1>;

				vga_bridge_out: endpoint {
					remote-endpoint = <&vga_con_in>;
				};
			};
		};
	};

	vga {
		compatible = "vga-connector";
		label = "J1";

		port {
			vga_con_in: endpoint {
				remote-endpoint = <&vga_bridge_out>;
			};
		};
	};

	core-module@10000000 {
		compatible = "arm,core-module-versatile", "syscon", "simple-mfd";
		reg = <0x10000000 0x200>;
		ranges = <0x0 0x10000000 0x200>;
		#address-cells = <1>;
		#size-cells = <1>;

		led@8,0 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x01>;
			label = "versatile:0";
			linux,default-trigger = "heartbeat";
			default-state = "on";
		};
		led@8,1 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x02>;
			label = "versatile:1";
			linux,default-trigger = "mmc0";
			default-state = "off";
		};
		led@8,2 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x04>;
			label = "versatile:2";
			linux,default-trigger = "cpu0";
			default-state = "off";
		};
		led@8,3 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x08>;
			label = "versatile:3";
			default-state = "off";
		};
		led@8,4 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x10>;
			label = "versatile:4";
			default-state = "off";
		};
		led@8,5 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x20>;
			label = "versatile:5";
			default-state = "off";
		};
		led@8,6 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x40>;
			label = "versatile:6";
			default-state = "off";
		};
		led@8,7 {
			compatible = "register-bit-led";
			reg = <0x08 0x04>;
			offset = <0x08>;
			mask = <0x80>;
			label = "versatile:7";
			default-state = "off";
		};

		/* OSC1 on AB, OSC4 on PB */
		osc1: clock-osc {
			#clock-cells = <0>;
			compatible = "arm,versatile-cm-auxosc";
			clocks = <&xtal24mhz>;
		};

		/* The timer clock is the 24 MHz oscillator divided to 1MHz */
		timclk: clock-1000000 {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clock-div = <24>;
			clock-mult = <1>;
			clocks = <&xtal24mhz>;
		};

		pclk: clock-pclk {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clock-div = <1>;
			clock-mult = <1>;
			clocks = <&xtal24mhz>;
		};
	};

	flash@******** {
		/* 64 MiB NOR flash in non-interleaved chips */
		compatible = "arm,versatile-flash", "cfi-flash";
		reg = <0x******** 0x04000000>;
		bank-width = <4>;
		partitions {
			compatible = "arm,arm-firmware-suite";
		};
	};

	i2c0: i2c@******** {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "arm,versatile-i2c";
		reg = <0x******** 0x1000>;

		rtc@68 {
			compatible = "dallas,ds1338";
			reg = <0x68>;
		};
	};

	net@******** {
		compatible = "smsc,lan91c111";
		reg = <0x******** 0x10000>;
		interrupts = <25>;
	};

	lcd@******** {
		compatible = "arm,versatile-lcd";
		reg = <0x******** 0x1000>;
	};

	amba {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vic: interrupt-controller@******** {
			compatible = "arm,versatile-vic";
			interrupt-controller;
			#interrupt-cells = <1>;
			reg = <0x******** 0x1000>;
			valid-mask = <0xffffffff>;
		};

		sic: interrupt-controller@******** {
			compatible = "arm,versatile-sic";
			interrupt-controller;
			#interrupt-cells = <1>;
			reg = <0x******** 0x1000>;
			interrupt-parent = <&vic>;
			interrupts = <31>; /* Cascaded to vic */
			clear-mask = <0xffffffff>;
			/*
			 * Valid interrupt lines mask according to
			 * table 4-36 page 4-50 of ARM DUI 0225D
			 */
			valid-mask = <0x0760031b>;
		};

		dma@10130000 {
			compatible = "arm,pl081", "arm,primecell";
			reg = <0x10130000 0x1000>;
			interrupts = <17>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		uart0: serial@101f1000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x101f1000 0x1000>;
			interrupts = <12>;
			clocks = <&xtal24mhz>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		uart1: serial@101f2000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x101f2000 0x1000>;
			interrupts = <13>;
			clocks = <&xtal24mhz>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		uart2: serial@101f3000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x101f3000 0x1000>;
			interrupts = <14>;
			clocks = <&xtal24mhz>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		smc@10100000 {
			compatible = "arm,primecell";
			reg = <0x10100000 0x1000>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		mpmc@10110000 {
			compatible = "arm,primecell";
			reg = <0x10110000 0x1000>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		display@10120000 {
			compatible = "arm,pl110", "arm,primecell";
			reg = <0x10120000 0x1000>;
			interrupts = <16>;
			clocks = <&osc1>, <&pclk>;
			clock-names = "clcdclk", "apb_pclk";
			/* 800x600 16bpp @ 36MHz works fine */
			max-memory-bandwidth = <54000000>;

			/*
			 * This port is routed through a PLD (Programmable
			 * Logic Device) that routes the output from the CLCD
			 * (after transformations) to the VGA DAC and also an
			 * external panel connector. The PLD is essential for
			 * supporting RGB565/BGR565.
			 *
			 * The signals from the port thus reaches two endpoints.
			 * The PLD is managed through a few special bits in the
			 * FPGA "sysreg".
			 *
			 * This arrangement can be clearly seen in
			 * ARM DUI 0225D, page 3-41, figure 3-19.
			 */
			port@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				clcd_pads_panel: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&panel_in>;
					arm,pl11x,tft-r0g0b0-pads = <0 8 16>;
				};
				clcd_pads_vga_dac: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vga_bridge_in>;
					arm,pl11x,tft-r0g0b0-pads = <0 8 16>;
				};
			};
		};

		sctl@101e0000 {
			compatible = "arm,primecell";
			reg = <0x101e0000 0x1000>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		watchdog@101e1000 {
			compatible = "arm,primecell";
			reg = <0x101e1000 0x1000>;
			interrupts = <0>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		timer@101e2000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x101e2000 0x1000>;
			interrupts = <4>;
			clocks = <&timclk>, <&timclk>, <&pclk>;
			clock-names = "timer0", "timer1", "apb_pclk";
		};

		timer@101e3000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x101e3000 0x1000>;
			interrupts = <5>;
			clocks = <&timclk>, <&timclk>, <&pclk>;
			clock-names = "timer0", "timer1", "apb_pclk";
		};

		gpio0: gpio@101e4000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x101e4000 0x1000>;
			gpio-controller;
			interrupts = <6>;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		gpio1: gpio@101e5000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x101e5000 0x1000>;
			interrupts = <7>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		rtc@101e8000 {
			compatible = "arm,pl030", "arm,primecell";
			reg = <0x101e8000 0x1000>;
			interrupts = <10>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		sci@101f0000 {
			compatible = "arm,primecell";
			reg = <0x101f0000 0x1000>;
			interrupts = <15>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		spi@101f4000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x101f4000 0x1000>;
			interrupts = <11>;
			clocks = <&xtal24mhz>, <&pclk>;
			clock-names = "sspclk", "apb_pclk";
		};

		fpga {
			compatible = "arm,versatile-fpga", "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x10000000 0x10000>;

			sysreg@0 {
				compatible = "arm,versatile-sysreg", "syscon", "simple-mfd";
				reg = <0x00000 0x1000>;

				panel: display@0 {
					compatible = "arm,versatile-tft-panel";

					port {
						panel_in: endpoint {
							remote-endpoint = <&clcd_pads_panel>;
						};
					};
				};
			};

			aaci@4000 {
				compatible = "arm,primecell";
				reg = <0x4000 0x1000>;
				interrupts = <24>;
				clocks = <&pclk>;
				clock-names = "apb_pclk";
			};
			mmc@5000 {
				compatible = "arm,pl180", "arm,primecell";
				reg = <0x5000 0x1000>;
				interrupts-extended = <&vic 22 &sic 1>;
				clocks = <&xtal24mhz>, <&pclk>;
				clock-names = "mclk", "apb_pclk";
			};
			kmi@6000 {
				compatible = "arm,pl050", "arm,primecell";
				reg = <0x6000 0x1000>;
				interrupt-parent = <&sic>;
				interrupts = <3>;
				clocks = <&xtal24mhz>, <&pclk>;
				clock-names = "KMIREFCLK", "apb_pclk";
			};
			kmi@7000 {
				compatible = "arm,pl050", "arm,primecell";
				reg = <0x7000 0x1000>;
				interrupt-parent = <&sic>;
				interrupts = <4>;
				clocks = <&xtal24mhz>, <&pclk>;
				clock-names = "KMIREFCLK", "apb_pclk";
			};
		};
	};
};
