// SPDX-License-Identifier: GPL-2.0+
/*
 * Copyright 2024 <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun5i-a13.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/leds/common.h>

/ {
	model = "PocketBook 614 Plus";
	compatible = "pocketbook,614-plus", "allwinner,sun5i-a13";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_POWER;
			linux,default-trigger = "default-on";
			gpios = <&pio 4 8 GPIO_ACTIVE_LOW>; /* PE8 */
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-0 {
			label = "Right";
			linux,code = <KEY_NEXT>;
			gpios = <&pio 6 9 GPIO_ACTIVE_LOW>; /* PG9 */
		};

		key-1 {
			label = "Left";
			linux,code = <KEY_PREVIOUS>;
			gpios = <&pio 6 10 GPIO_ACTIVE_LOW>; /* PG10 */
		};
	};

	reg_3v3_mmc0: regulator-mmc0 {
		compatible = "regulator-fixed";
		regulator-name = "vdd-mmc0";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pio 4 4 GPIO_ACTIVE_LOW>; /* PE4 */
		vin-supply = <&reg_vcc3v3>;
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		compatible = "x-powers,axp209";
		reg = <0x34>;
		interrupts = <0>;
	};
};

#include "axp209.dtsi"

&i2c1 {
	status = "okay";

	pcf8563: rtc@51 {
		compatible = "nxp,pcf8563";
		reg = <0x51>;
		#clock-cells = <0>;
	};
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-300 {
		label = "Down";
		linux,code = <KEY_DOWN>;
		channel = <0>;
		voltage = <300000>;
	};

	button-700 {
		label = "Up";
		linux,code = <KEY_UP>;
		channel = <0>;
		voltage = <700000>;
	};

	button-1000 {
		label = "Left";
		linux,code = <KEY_LEFT>;
		channel = <0>;
		voltage = <1000000>;
	};

	button-1200 {
		label = "Menu";
		linux,code = <KEY_MENU>;
		channel = <0>;
		voltage = <1200000>;
	};

	button-1500 {
		label = "Right";
		linux,code = <KEY_RIGHT>;
		channel = <0>;
		voltage = <1500000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_3v3_mmc0>;
	bus-width = <4>;
	cd-gpios = <&pio 6 0 GPIO_ACTIVE_LOW>; /* PG0 */
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_4bit_pc_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_usb0_vbus {
	status = "okay";
	gpio = <&pio 6 12 GPIO_ACTIVE_HIGH>; /* PG12 */
};

&reg_usb1_vbus {
	gpio = <&pio 6 11 GPIO_ACTIVE_HIGH>; /* PG11 */
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 2 GPIO_ACTIVE_HIGH>; /* PG2 */
	usb0_vbus_det-gpios = <&axp_gpio 1 GPIO_ACTIVE_HIGH>;
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};
