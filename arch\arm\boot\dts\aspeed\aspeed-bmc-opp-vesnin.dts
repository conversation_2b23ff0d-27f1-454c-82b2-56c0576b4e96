// SPDX-License-Identifier: GPL-2.0+
// Copyright 2019 YADRO
/dts-v1/;

#include "aspeed-g4.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Vesnin BMC";
	compatible = "yadro,vesnin-bmc", "aspeed,ast2400";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@40000000 {
		reg = <0x40000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@5f000000 {
			no-map;
			reg = <0x5f000000 0x01000000>; /* 16MB */
		};
		flash_memory: region@5c000000 {
			no-map;
			reg = <0x5c000000 0x02000000>; /* 32M */
		};
	};

	leds {
		compatible = "gpio-leds";

		heartbeat {
			gpios = <&gpio ASPEED_GPIO(R, 4) GPIO_ACTIVE_LOW>;
		};
		power_red {
			gpios = <&gpio ASPEED_GPIO(N, 1) GPIO_ACTIVE_LOW>;
		};

		power_green {
			gpios = <&gpio ASPEED_GPIO(F, 1) GPIO_ACTIVE_LOW>;
		};

		id_blue {
			gpios = <&gpio ASPEED_GPIO(O, 0) GPIO_ACTIVE_LOW>;
		};

		alarm_red {
			gpios = <&gpio ASPEED_GPIO(N, 6) GPIO_ACTIVE_LOW>;
		};

		alarm_yel {
			gpios = <&gpio ASPEED_GPIO(N, 7) GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-checkstop {
			label = "checkstop";
			linux,code = <74>;
			gpios = <&gpio ASPEED_GPIO(P, 5) GPIO_ACTIVE_LOW>;
		};

		event-identify {
			label = "identify";
			linux,code = <152>;
			gpios = <&gpio ASPEED_GPIO(O, 7) GPIO_ACTIVE_LOW>;
		};
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
        label = "bmc";
#include "openbmc-flash-layout.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt";
	};
};

&spi {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1debug_default>;

	flash@0 {
		status = "okay";
		label = "pnor";
		m25p,fast-read;
	};
};

&mac0 {
	status = "okay";
	use-ncsi;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
};


&uart5 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi>;
};

&ibt {
	status = "okay";
};

&uart3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default &pinctrl_rxd2_default>;
};

&i2c0 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c256";
		reg = <0x50>;
		pagesize = <64>;
	};
};

&i2c1 {
	status = "okay";

	tmp75@49 {
		compatible = "ti,tmp75";
		reg = <0x49>;
	};
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";

	occ-hwmon@50 {
		compatible = "ibm,p8-occ-hwmon";
		reg = <0x50>;
	};
};

&i2c5 {
	status = "okay";

	occ-hwmon@51 {
		compatible = "ibm,p8-occ-hwmon";
		reg = <0x51>;
	};
};

&i2c6 {
	status = "okay";

	w83795g@2f {
		compatible = "nuvoton,w83795g";
		reg = <0x2f>;
	};
};

&i2c7 {
	status = "okay";

	occ-hwmon@56 {
		compatible = "ibm,p8-occ-hwmon";
		reg = <0x56>;
	};
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";

	occ-hwmon@57 {
		compatible = "ibm,p8-occ-hwmon";
		reg = <0x57>;
	};
};

&i2c12 {
	status = "okay";

	rtc@68 {
		compatible = "maxim,ds3231";
		reg = <0x68>;
	};
};

&i2c13 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&wdt2 {
	aspeed,alt-boot;
};

&sdmmc {
	status = "okay";
};

&sdhci1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sd2_default>;
	cd-inverted;
	disable-wp;
};
