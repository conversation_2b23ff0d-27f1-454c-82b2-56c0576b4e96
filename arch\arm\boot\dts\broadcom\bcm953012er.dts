/*
 *  BSD LICENSE
 *
 *  Copyright(c) 2016 Broadcom. All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    * Redistributions of source code must retain the above copyright
 *      notice, this list of conditions and the following disclaimer.
 *    * Redistributions in binary form must reproduce the above copyright
 *      notice, this list of conditions and the following disclaimer in
 *      the documentation and/or other materials provided with the
 *      distribution.
 *    * Neither the name of Broadcom Corporation nor the names of its
 *      contributors may be used to endorse or promote products derived
 *      from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDE<PERSON>AL,
 *  <PERSON>ECIA<PERSON>, E<PERSON>EMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/dts-v1/;

#include "bcm4708.dtsi"
#include "bcm5301x-nand-cs0-bch8.dtsi"

/ {
	model = "NorthStar Enterprise Router (BCM953012ER)";
	compatible = "brcm,bcm953012er", "brcm,bcm53012", "brcm,bcm4708";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x8000000>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-wps {
			label = "WPS";
			linux,code = <KEY_WPS_BUTTON>;
			gpios = <&chipcommon 6 GPIO_ACTIVE_LOW>;
		};

		button-restart {
			label = "Reset";
			linux,code = <KEY_RESTART>;
			gpios = <&chipcommon 15 GPIO_ACTIVE_LOW>;
		};
	};
};

&spi_nor {
	status = "okay";
};

&srab {
	status = "okay";

	ports {
		port@0 {
			reg = <0>;
			label = "port0";
		};

		port@1 {
			reg = <1>;
			label = "port1";
		};

		port@5 {
			reg = <5>;
			label = "cpu";
			ethernet = <&gmac0>;
		};

		port@7 {
			status = "disabled";
		};

		port@8 {
			status = "disabled";
		};
	};
};

&usb3_phy {
	status = "okay";
};
