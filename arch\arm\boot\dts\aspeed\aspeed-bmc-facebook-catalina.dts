// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2021 Facebook Inc.
/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/usb/pd.h>
#include <dt-bindings/leds/leds-pca955x.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/i2c/i2c.h>

/ {
	model = "Facebook Catalina BMC";
	compatible = "facebook,catalina-bmc", "aspeed,ast2600";

	aliases {
		serial0 = &uart1;
		serial2 = &uart3;
		serial3 = &uart4;
		serial4 = &uart5;
		i2c16 = &i2c1mux0ch0;
		i2c17 = &i2c1mux0ch1;
		i2c18 = &i2c1mux0ch2;
		i2c19 = &i2c1mux0ch3;
		i2c20 = &i2c1mux0ch4;
		i2c21 = &i2c1mux0ch5;
		i2c22 = &i2c1mux0ch6;
		i2c23 = &i2c1mux0ch7;
		i2c24 = &i2c0mux0ch0;
		i2c25 = &i2c0mux0ch1;
		i2c26 = &i2c0mux0ch2;
		i2c27 = &i2c0mux0ch3;
		i2c28 = &i2c0mux1ch0;
		i2c29 = &i2c0mux1ch1;
		i2c30 = &i2c0mux1ch2;
		i2c31 = &i2c0mux1ch3;
		i2c32 = &i2c0mux2ch0;
		i2c33 = &i2c0mux2ch1;
		i2c34 = &i2c0mux2ch2;
		i2c35 = &i2c0mux2ch3;
		i2c36 = &i2c0mux3ch0;
		i2c37 = &i2c0mux3ch1;
		i2c38 = &i2c0mux3ch2;
		i2c39 = &i2c0mux3ch3;
		i2c40 = &i2c0mux4ch0;
		i2c41 = &i2c0mux4ch1;
		i2c42 = &i2c0mux4ch2;
		i2c43 = &i2c0mux4ch3;
		i2c44 = &i2c0mux5ch0;
		i2c45 = &i2c0mux5ch1;
		i2c46 = &i2c0mux5ch2;
		i2c47 = &i2c0mux5ch3;
		i2c48 = &i2c5mux0ch0;
		i2c49 = &i2c5mux0ch1;
		i2c50 = &i2c5mux0ch2;
		i2c51 = &i2c5mux0ch3;
		i2c52 = &i2c5mux0ch4;
		i2c53 = &i2c5mux0ch5;
		i2c54 = &i2c5mux0ch6;
		i2c55 = &i2c5mux0ch7;
	};

	chosen {
		stdout-path = "serial4:57600n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc0 0>, <&adc0 1>, <&adc0 2>, <&adc0 3>,
			      <&adc0 4>, <&adc0 5>, <&adc0 6>, <&adc0 7>,
			      <&adc1 2>;
	};

	spi1_gpio: spi {
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		sck-gpios = <&gpio0 ASPEED_GPIO(Z, 3) GPIO_ACTIVE_HIGH>;
		mosi-gpios = <&gpio0 ASPEED_GPIO(Z, 4) GPIO_ACTIVE_HIGH>;
		miso-gpios = <&gpio0 ASPEED_GPIO(Z, 5) GPIO_ACTIVE_HIGH>;
		cs-gpios = <&gpio0 ASPEED_GPIO(Z, 0) GPIO_ACTIVE_LOW>;
		num-chipselects = <1>;

		tpm@0 {
			compatible = "infineon,slb9670", "tcg,tpm_tis-spi";
			spi-max-frequency = <33000000>;
			reg = <0>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "bmc_heartbeat_amber";
			gpios = <&gpio0 ASPEED_GPIO(P, 7) GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};

		led-1 {
			label = "fp_id_amber";
			default-state = "off";
			gpios = <&gpio0 ASPEED_GPIO(B, 5) GPIO_ACTIVE_HIGH>;
		};

		led-2 {
			label = "bmc_ready_noled";
			gpios = <&gpio0 ASPEED_GPIO(B, 3) (GPIO_ACTIVE_HIGH|GPIO_TRANSITORY)>;
		};

		led-3 {
			label = "bmc_ready_cpld_noled";
			gpios = <&gpio0 ASPEED_GPIO(P, 5) (GPIO_ACTIVE_HIGH|GPIO_TRANSITORY)>;
		};
	};

	p1v8_bmc_aux: regulator-p1v8-bmc-aux {
		compatible = "regulator-fixed";
		regulator-name = "p1v8_bmc_aux";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	p2v5_bmc_aux: regulator-p2v5-bmc-aux {
		compatible = "regulator-fixed";
		regulator-name = "p2v5_bmc_aux";
		regulator-min-microvolt = <2500000>;
		regulator-max-microvolt = <2500000>;
		regulator-always-on;
	};
};

&uart1 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_ncsi3_default>;
	use-ncsi;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_ncsi4_default>;
	use-ncsi;
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-128.dtsi"
	};
	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
		spi-max-frequency = <50000000>;
	};
};

&i2c0 {
	status = "okay";

	i2c-mux@71 {
		compatible = "nxp,pca9546";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c0mux0ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2c0mux0ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
		i2c0mux0ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2c0mux0ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	i2c-mux@72 {
		compatible = "nxp,pca9546";
		reg = <0x72>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c0mux1ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2c0mux1ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			// IO Mezz 0 IOEXP
			io_expander7: gpio@20 {
				compatible = "nxp,pca9535";
				reg = <0x20>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			// IO Mezz 0 FRU EEPROM
			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};
		i2c0mux1ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2c0mux1ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	i2c-mux@73 {
		compatible = "nxp,pca9546";
		reg = <0x73>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c0mux2ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2c0mux2ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
		i2c0mux2ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2c0mux2ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	i2c-mux@75 {
		compatible = "nxp,pca9546";
		reg = <0x75>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c0mux3ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2c0mux3ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
		i2c0mux3ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2c0mux3ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	i2c-mux@76 {
		compatible = "nxp,pca9546";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c0mux4ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2c0mux4ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			// IO Mezz 1 IOEXP
			io_expander8: gpio@21 {
				compatible = "nxp,pca9535";
				reg = <0x21>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			// IO Mezz 1 FRU EEPROM
			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};
		i2c0mux4ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2c0mux4ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	i2c-mux@77 {
		compatible = "nxp,pca9546";
		reg = <0x77>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c0mux5ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2c0mux5ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
		i2c0mux5ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2c0mux5ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c1 {
	status = "okay";
	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		i2c1mux0ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;

			power-sensor@41 {
				compatible = "ti,ina238";
				reg = <0x41>;
				shunt-resistor = <500>;
			};
			power-sensor@42 {
				compatible = "ti,ina238";
				reg = <0x42>;
				shunt-resistor = <500>;
			};
			power-sensor@44 {
				compatible = "ti,ina238";
				reg = <0x44>;
				shunt-resistor = <500>;
			};
		};
		i2c1mux0ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x1>;

			power-sensor@41 {
				compatible = "ti,ina238";
				reg = <0x41>;
			};
			power-sensor@43 {
				compatible = "ti,ina238";
				reg = <0x43>;
			};
		};
		i2c1mux0ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;
		};
		i2c1mux0ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;
		};
		i2c1mux0ch4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x4>;

			power-monitor@42 {
				compatible = "lltc,ltc4287";
				reg = <0x42>;
				shunt-resistor-micro-ohms = <100>;
			};
			power-monitor@43 {
				compatible = "lltc,ltc4287";
				reg = <0x43>;
				shunt-resistor-micro-ohms = <100>;
			};
		};
		i2c1mux0ch5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x5>;

			// PDB FRU EEPROM
			eeprom@54 {
				compatible = "atmel,24c64";
				reg = <0x54>;
			};

			// PDB TEMP SENSOR
			temperature-sensor@4f {
				compatible = "ti,tmp75";
				reg = <0x4f>;
			};
		};
		i2c1mux0ch6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x6>;

			// PDB IOEXP
			io_expander5: gpio@27 {
				compatible = "nxp,pca9554";
				reg = <0x27>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			// OSFP IOEXP
			io_expander6: gpio@25 {
				compatible = "nxp,pca9555";
				reg = <0x25>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			// OSFP FRU EEPROM
			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};
		};
		i2c1mux0ch7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x7>;

			// FIO FRU EEPROM
			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};

			// FIO TEMP SENSOR
			temperature-sensor@4b {
				compatible = "ti,tmp75";
				reg = <0x4b>;
			};
		};
	};
};

&i2c2 {
	status = "okay";

	// Module 0 IOEXP
	io_expander0: gpio@20 {
		compatible = "nxp,pca9555";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// Module 1 IOEXP
	io_expander1: gpio@21 {
		compatible = "nxp,pca9555";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// HMC IOEXP
	io_expander2: gpio@27 {
		compatible = "nxp,pca9555";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// Module 0 EEPROM
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	// Module 1 EEPROM
	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c5mux0ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2c5mux0ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
		i2c5mux0ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2c5mux0ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
		i2c5mux0ch4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};
		i2c5mux0ch5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};
		i2c5mux0ch6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
			// HDD FRU EEPROM
			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};
		};
		i2c5mux0ch7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;

			power-sensor@40 {
				compatible = "ti,ina230";
				reg = <0x40>;
				shunt-resistor = <2000>;
			};
			power-sensor@41 {
				compatible = "ti,ina230";
				reg = <0x41>;
				shunt-resistor = <2000>;
			};
			power-sensor@44 {
				compatible = "ti,ina230";
				reg = <0x44>;
				shunt-resistor = <2000>;
			};
			power-sensor@45 {
				compatible = "ti,ina230";
				reg = <0x45>;
				shunt-resistor = <2000>;
			};
		};
	};
};

&i2c6 {
	status = "okay";

	// BMC IOEXP on Module 0
	io_expander3: gpio@21 {
		compatible = "nxp,pca9555";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	rtc@6f {
		compatible = "nuvoton,nct3018y";
		reg = <0x6f>;
	};
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";

	// SCM CPLD IOEXP
	io_expander4: gpio@4f {
		compatible = "nxp,pca9555";
		reg = <0x4f>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// SCM TEMP SENSOR
	temperature-sensor@4b {
		compatible = "ti,tmp75";
		reg = <0x4b>;
	};

	// SCM FRU EEPROM
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	// BSM FRU EEPROM
	eeprom@56 {
		compatible = "atmel,24c64";
		reg = <0x56>;
	};
};

&i2c10 {
	status = "okay";

	// OCP NIC0 TEMP
	temperature-sensor@1f {
		compatible = "ti,tmp421";
		reg = <0x1f>;
	};

	// OCP NIC0 FRU EEPROM
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};
};

&i2c11 {
	status = "okay";

	ssif-bmc@10 {
		compatible = "ssif-bmc";
		reg = <0x10>;
	};
};

&i2c12 {
	status = "okay";

	// Module 1 FRU EEPROM
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};
};

&i2c13 {
	status = "okay";

	// Module 0 FRU EEPROM
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	// Left CBC FRU EEPROM
	eeprom@54 {
		compatible = "atmel,24c02";
		reg = <0x54>;
	};

	// Right CBC FRU EEPROM
	eeprom@55 {
		compatible = "atmel,24c02";
		reg = <0x55>;
	};

	// HMC FRU EEPROM
	eeprom@57 {
		compatible = "atmel,24c02";
		reg = <0x57>;
	};
};

&i2c14 {
	status = "okay";

	// PDB CPLD IOEXP 0x10
	io_expander9: gpio@10 {
		compatible = "nxp,pca9555";
		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(I, 6) IRQ_TYPE_LEVEL_LOW>;
		reg = <0x10>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// PDB CPLD IOEXP 0x11
	io_expander10: gpio@11 {
		compatible = "nxp,pca9555";
		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(I, 6) IRQ_TYPE_LEVEL_LOW>;
		reg = <0x11>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// PDB CPLD IOEXP 0x12
	io_expander11: gpio@12 {
		compatible = "nxp,pca9555";
		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(I, 6) IRQ_TYPE_LEVEL_LOW>;
		reg = <0x12>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// PDB CPLD IOEXP 0x13
	io_expander12: gpio@13 {
		compatible = "nxp,pca9555";
		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(I, 6) IRQ_TYPE_LEVEL_LOW>;
		reg = <0x13>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// PDB CPLD IOEXP 0x14
	io_expander13: gpio@14 {
		compatible = "nxp,pca9555";
		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(I, 6) IRQ_TYPE_LEVEL_LOW>;
		reg = <0x14>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// PDB CPLD IOEXP 0x15
	io_expander14: gpio@15 {
		compatible = "nxp,pca9555";
		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(I, 6) IRQ_TYPE_LEVEL_LOW>;
		reg = <0x15>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

&i2c15 {
	status = "okay";

	// OCP NIC1 TEMP
	temperature-sensor@1f {
		compatible = "ti,tmp421";
		reg = <0x1f>;
	};

	// OCP NIC1 FRU EEPROM
	eeprom@52 {
		compatible = "atmel,24c64";
		reg = <0x52>;
	};
};

&adc0 {
	vref-supply = <&p1v8_bmc_aux>;
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default &pinctrl_adc1_default
		&pinctrl_adc2_default &pinctrl_adc3_default
		&pinctrl_adc4_default &pinctrl_adc5_default
		&pinctrl_adc6_default &pinctrl_adc7_default>;
};

&adc1 {
	vref-supply = <&p2v5_bmc_aux>;
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc10_default>;
};

&ehci0 {
	status = "okay";
};

&wdt1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
	aspeed,reset-type = "soc";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;
	aspeed,ext-pulse-duration = <256>;
};

&pinctrl {
	pinctrl_ncsi3_default: ncsi3_default {
		function = "RMII3";
		groups = "NCSI3";
	};

	pinctrl_ncsi4_default: ncsi4_default {
		function = "RMII4";
		groups = "NCSI4";
	};
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"BATTERY_DETECT","PRSNT1_HPM_SCM_N",
			"BMC_I2C1_FPGA_ALERT_L","BMC_READY",
			"IOEXP_INT_L","FM_ID_LED",
			"","",
	/*C0-C7*/	"","","","",
			"PMBUS_REQ_N","PSU_FW_UPDATE_REQ_N",
			"","BMC_I2C_SSIF_ALERT_L",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","",
			"FM_DEBUG_PORT_PRSNT_N","FM_BMC_DBP_PRESENT_N",
	/*H0-H7*/	"PWR_BRAKE_L","RUN_POWER_EN",
			"SHDN_FORCE_L","SHDN_REQ_L",
			"","","","",
	/*I0-I7*/	"","","","",
			"","FLASH_WP_STATUS",
			"FM_PDB_HEALTH_N","RUN_POWER_PG",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"PCIE_EP_RST_EN","BMC_FRU_WP",
			"SCM_HPM_STBY_RST_N","SCM_HPM_STBY_EN",
			"STBY_POWER_PG_3V3","TH500_SHDN_OK_L","","",
	/*N0-N7*/	"LED_POSTCODE_0","LED_POSTCODE_1",
			"LED_POSTCODE_2","LED_POSTCODE_3",
			"LED_POSTCODE_4","LED_POSTCODE_5",
			"LED_POSTCODE_6","LED_POSTCODE_7",
	/*O0-O7*/	"HMC_I2C3_FPGA_ALERT_L","FPGA_READY_HMC",
			"CHASSIS_AC_LOSS_L","BSM_PRSNT_R_N",
			"PSU_SMB_ALERT_L","FM_TPM_PRSNT_0_N",
			"","USBDBG_IPMI_EN_L",
	/*P0-P7*/	"PWR_BTN_BMC_N","IPEX_CABLE_PRSNT_L",
			"ID_RST_BTN_BMC_N","RST_BMC_RSTBTN_OUT_N",
			"host0-ready","BMC_READY_CPLD","","BMC_HEARTBEAT_N",
	/*Q0-Q7*/	"IRQ_PCH_TPM_SPI_N","USB_OC0_REAR_R_N",
			"UART_MUX_SEL","I2C_MUX_RESET_L",
			"RSVD_NV_PLT_DETECT","SPI_TPM_INT_L",
			"CPU_JTAG_MUX_SELECT","THERM_BB_OVERT_L",
	/*R0-R7*/	"THERM_BB_WARN_L","SPI_BMC_FPGA_INT_L",
			"CPU_BOOT_DONE","PMBUS_GNT_L",
			"CHASSIS_PWR_BRK_L","PCIE_WAKE_L",
			"PDB_THERM_OVERT_L","HMC_I2C2_FPGA_ALERT_L",
	/*S0-S7*/	"","","SYS_BMC_PWRBTN_R_N","FM_TPM_PRSNT_1_N",
			"FM_BMC_DEBUG_SW_N","UID_LED_N",
			"SYS_FAULT_LED_N","RUN_POWER_FAULT_L",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"L2_RST_REQ_OUT_L","L0L1_RST_REQ_OUT_L",
			"BMC_ID_BEEP_SEL","BMC_I2C0_FPGA_ALERT_L",
			"SMB_BMC_TMP_ALERT","PWR_LED_N",
			"SYS_RST_OUT_L","IRQ_TPM_SPI_N",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","RST_BMC_SELF_HW",
			"FM_FLASH_LATCH_N","BMC_EMMC_RST_N",
			"","","","",
	/*Z0-Z7*/	"","","","","","","","";
};

&io_expander0 {
	gpio-line-names =
		"FPGA_THERM_OVERT_L","FPGA_READY_BMC",
		"HMC_BMC_DETECT","HMC_PGOOD",
		"","BMC_SELF_PWR_CYCLE",
		"FPGA_EROT_FATAL_ERROR_L","WP_HW_EXT_CTRL_L",
		"EROT_FPGA_RST_L","FPGA_EROT_RECOVERY_L",
		"BMC_EROT_FPGA_SPI_MUX_SEL","USB2_HUB_RESET_L",
		"NCSI_CS1_SEL","SGPIO_EN_L",
		"B2B_IOEXP_INT_L","I2C_BUS_MUX_RESET_L";
};

&io_expander1 {
	gpio-line-names =
		"SEC_FPGA_THERM_OVERT_L","SEC_FPGA_READY_BMC",
		"","",
		"","",
		"SEC_FPGA_EROT_FATAL_ERROR_L","SEC_WP_HW_EXT_CTRL_L",
		"SEC_EROT_FPGA_RST_L","SEC_FPGA_EROT_RECOVERY_L",
		"SEC_BMC_EROT_FPGA_SPI_MUX_SEL","",
		"","",
		"","SEC_I2C_BUS_MUX_RESET_L";
};

&io_expander2 {
	gpio-line-names =
		"HMC_PRSNT_L","HMC_READY",
		"HMC_EROT_FATAL_ERROR_L","I2C_MUX_SEL",
		"HMC_EROT_SPI_MUX_SEL","HMC_EROT_RECOVERY_L",
		"HMC_EROT_RST_L","GLOBAL_WP_HMC",
		"FPGA_RST_L","USB2_HUB_RST",
		"CPU_UART_MUX_SEL","",
		"","","","";
};

&io_expander3 {
	gpio-line-names =
		"RTC_MUX_SEL","PCI_MUX_SEL","TPM_MUX_SEL","FAN_MUX-SEL",
		"SGMII_MUX_SEL","DP_MUX_SEL","UPHY3_USB_SEL","NCSI_MUX_SEL",
		"BMC_PHY_RST","RTC_CLR_L","BMC_12V_CTRL","PS_RUN_IO0_PG",
		"","","","";
};

&io_expander4 {
	gpio-line-names =
		"stby_power_en_cpld","stby_power_gd_cpld","","",
		"","","","",
		"","","","",
		"","","","";
};

&io_expander5 {
	gpio-line-names =
		"JTAG_MUX_SEL","IOX_BMC_RESET","","",
		"","","","";
};

&io_expander6 {
	gpio-line-names =
		"OSFP_PHASE_ID0","OSFP_PHASE_ID1",
		"OSFP_PHASE_ID2","OSFP_PHASE_ID3",
		"","","","",
		"OSFP_BOARD_ID0","OSFP_BOARD_ID1",
		"OSFP_BOARD_ID2","PWRGD_P3V3_N1",
		"PWRGD_P3V3_N2","","","";
};

&io_expander7 {
	gpio-line-names =
		"RST_CX7_0","RST_CX7_1",
		"CX0_SSD0_PRSNT_L","CX1_SSD1_PRSNT_L",
		"CX_BOOT_CMPLT_CX0","CX_BOOT_CMPLT_CX1",
		"CX_TWARN_CX0_L","CX_TWARN_CX1_L",
		"CX_OVT_SHDN_CX0","CX_OVT_SHDN_CX1",
		"FNP_L_CX0","FNP_L_CX1",
		"","MCU_GPIO","MCU_RST_N","MCU_RECOVERY_N";
};

&io_expander8 {
	gpio-line-names =
		"SEC_RST_CX7_0","SEC_RST_CX7_1",
		"SEC_CX0_SSD0_PRSNT_L","SEC_CX1_SSD1_PRSNT_L",
		"SEC_CX_BOOT_CMPLT_CX0","SEC_CX_BOOT_CMPLT_CX1",
		"SEC_CX_TWARN_CX0_L","SEC_CX_TWARN_CX1_L",
		"SEC_CX_OVT_SHDN_CX0","SEC_CX_OVT_SHDN_CX1",
		"SEC_FNP_L_CX0","SEC_FNP_L_CX1",
		"","SEC_MCU_GPIO","SEC_MCU_RST_N","SEC_MCU_RECOVERY_N";
};

&io_expander9 {
	gpio-line-names =
		"LEAK3_DETECT_R","LEAK1_DETECT_R",
		"LEAK2_DETECT_R","LEAK0_DETECT_R",
		"CHASSIS3_LEAK_Q_N_PLD","CHASSIS1_LEAK_Q_N_PLD",
		"CHASSIS2_LEAK_Q_N_PLD","CHASSIS0_LEAK_Q_N_PLD",
		"P12V_AUX_FAN_ALERT_PLD_N","P12V_AUX_FAN_OC_PLD_N",
		"P12V_AUX_FAN_FAULT_PLD_N","LEAK_DETECT_RMC_N_R",
		"RSVD_RMC_GPIO3_R","SMB_RJ45_FIO_TMP_ALERT",
		"","";
};

&io_expander10 {
	gpio-line-names =
		"FM_P12V_NIC1_FLTB_R_N","FM_P3V3_NIC1_FAULT_R_N",
		"OCP_V3_2_PWRBRK_FROM_HOST_ISO_PLD_N",
		"P12V_AUX_NIC1_SENSE_ALERT_R_N",
		"FM_P12V_NIC0_FLTB_R_N","FM_P3V3_NIC0_FAULT_R_N",
		"OCP_SFF_PWRBRK_FROM_HOST_ISO_PLD_N",
		"P12V_AUX_NIC0_SENSE_ALERT_R_N",
		"P12V_AUX_PSU_SMB_ALERT_R_L","P12V_SCM_SENSE_ALERT_R_N",
		"NODEB_PSU_SMB_ALERT_R_L","NODEA_PSU_SMB_ALERT_R_L",
		"P52V_SENSE_ALERT_PLD_N","P48V_HS2_FAULT_N_PLD",
		"P48V_HS1_FAULT_N_PLD","";
};

&io_expander11 {
	gpio-line-names =
		"FAN_7_PRESENT_N","FAN_6_PRESENT_N",
		"FAN_5_PRESENT_N","FAN_4_PRESENT_N",
		"FAN_3_PRESENT_N","FAN_2_PRESENT_N",
		"FAN_1_PRESENT_N","FAN_0_PRESENT_N",
		"PRSNT_CHASSIS3_LEAK_CABLE_R_N","PRSNT_CHASSIS1_LEAK_CABLE_R_N",
		"PRSNT_CHASSIS2_LEAK_CABLE_R_N","PRSNT_CHASSIS0_LEAK_CABLE_R_N",
		"PRSNT_RJ45_FIO_N_R","PRSNT_HDDBD_POWER_CABLE_N",
		"PRSNT_OSFP_POWER_CABLE_N","";
};

&io_expander12 {
	gpio-line-names =
		"RST_OCP_V3_1_R_N","NIC0_PERST_N",
		"OCP_SFF_PERST_FROM_HOST_ISO_PLD_N","OCP_SFF_MAIN_PWR_EN",
		"FM_OCP_SFF_PWR_GOOD_PLD","OCP_SFF_AUX_PWR_PLD_EN_R",
		"HP_LVC3_OCP_V3_1_PWRGD_PLD","HP_OCP_V3_1_HSC_PWRGD_PLD_R",
		"RST_OCP_V3_2_R_N","NIC1_PERST_N",
		"OCP_V3_2_PERST_FROM_HOST_ISO_PLD_N","OCP_V3_2_MAIN_PWR_EN",
		"FM_OCP_V3_2_PWR_GOOD_PLD","OCP_V3_2_AUX_PWR_PLD_EN_R",
		"HP_LVC3_OCP_V3_2_PWRGD_PLD","HP_OCP_V3_2_HSC_PWRGD_PLD_R";
};

&io_expander13 {
	gpio-line-names =
		"NODEA_NODEB_PWOK_PLD_ISO_R","PWR_EN_NICS",
		"PWRGD_P12V_AUX_FAN_PLD","P12V_AUX_FAN_EN_PLD",
		"PWRGD_P3V3_AUX_PLD","PWRGD_P12V_AUX_PLD_ISO_R",
		"FM_MAIN_PWREN_FROM_RMC_R","FM_MAIN_PWREN_RMC_EN_ISO_R",
		"PWRGD_RMC_R","PWRGD_P12V_AUX_FAN_PLD",
		"P12V_AUX_FAN_EN_PLD","FM_SYS_THROTTLE_N",
		"HP_LVC3_OCP_V3_2_PRSNT2_PLD_N","HP_LVC3_OCP_V3_1_PRSNT2_PLD_N",
		"","";
};

&io_expander14 {
	gpio-line-names =
		"","","","","","","","",
		"FM_BOARD_BMC_SKU_ID3","FM_BOARD_BMC_SKU_ID2",
		"FM_BOARD_BMC_SKU_ID1","FM_BOARD_BMC_SKU_ID0",
		"FAB_BMC_REV_ID2","FAB_BMC_REV_ID1",
		"FAB_BMC_REV_ID0","";
};
