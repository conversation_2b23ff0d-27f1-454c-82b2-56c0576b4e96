// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

/{
	model = "ASRock ROMED8HM3 BMC v1.00";
	compatible = "asrock,romed8hm3-bmc", "aspeed,ast2500";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=tty0 console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	leds {
		compatible = "gpio-leds";

		heartbeat {
			gpios = <&gpio ASPEED_GPIO(H, 6) GPIO_ACTIVE_LOW>;
			linux,default-trigger = "timer";
		};

		system-fault {
			gpios = <&gpio ASPEED_GPIO(Z, 2) GPIO_ACTIVE_HIGH>;
			panic-indicator;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 12>, <&adc 13>, <&adc 14>, <&adc 15>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>; /* 50 MHz */
#include "openbmc-flash-layout-64.dtsi"
	};
};

&uart5 {
	status = "okay";
};

&vuart {
	status = "okay";
	aspeed,lpc-io-reg = <0x2f8>;
	aspeed,lpc-interrupts = <3 IRQ_TYPE_LEVEL_HIGH>;
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;

	nvmem-cells = <&eth0_macaddress>;
	nvmem-cell-names = "mac-address";
};

&i2c0 {
	status = "okay";

	/* inlet temp sensor */
	w83773g@4c {
		compatible = "nuvoton,w83773g";
		reg = <0x4c>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	/* IPB temp sensor */
	w83773g@4c {
		compatible = "nuvoton,w83773g";
		reg = <0x4c>;
	};

	/* IPB PMIC */
	lm25066@40 {
		compatible = "ti,lm25066";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <1000>;
	};

	/* 12VSB PMIC */
	lm25066@41 {
		compatible = "ti,lm25066";
		reg = <0x41>;
		shunt-resistor-micro-ohms = <10000>;
	};
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";

	/* Baseboard FRU eeprom */
	eeprom@50 {
		compatible = "st,24c128", "atmel,24c128";
		reg = <0x50>;
		pagesize = <16>;
		#address-cells = <1>;
		#size-cells = <1>;

		eth0_macaddress: macaddress@3f80 {
			reg = <0x3f80 6>;
		};
	};
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&video {
	status = "okay";
};

&vhub {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm3_default
		&pinctrl_pwm4_default
		&pinctrl_pwm5_default
		&pinctrl_pwm6_default>;

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03 0x0b>;
	};

	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04 0x0c>;
	};

	fan@5 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05 0x0d>;
	};

	fan@6 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06 0x0e>;
	};
};

&gpio {
	status = "okay";
	gpio-line-names =
		/*  A */ "LOCATORLED_STATUS_N", "BMC_MAC2_INTB", "NMI_BTN_N", "BMC_NMI",
			"", "", "", "",
		/*  B */ "POST_COMPLETE_N", "", "", "", "", "", "", "",
		/*  C */ "", "", "", "", "PCIE_HP_SEL_N", "PCIE_SATA_SEL_N", "LOCATORBTN", "",
		/*  D */ "BMC_PSIN", "BMC_PSOUT", "BMC_RESETCON", "RESETCON",
			"", "", "", "PSU_FAN_FAIL_N",
		/*  E */ "", "", "", "", "", "", "", "",
		/*  F */ "NIC_PWR_GOOD", "PRSNTB0", "PRSNTB1", "PRSNTB2",
			"PRSNTB3", "", "3VSB_PCIE1_PG", "12V_PCIE1_PG",
		/*  G */ "HWM_BAT_EN", "CHASSIS_ID0", "CHASSIS_ID1", "CHASSIS_ID2",
			"BMC_ALERT1_N_R", "BMC_ALERT2_N_R", "BMC_ALERT3_N", "BMC_ALERT4_N",
		/*  H */ "X24_C1_PRSNT", "X24_C2_PRSNT", "X24_C3_PRSNT", "FM_MEM_THERM_EVENT_BMC_R_N",
			"FACMODE", "BMC_RTCRST", "BMC_HB_LED_N", "BMC_CASEOPEN",
		/*  I */ "", "", "", "", "", "", "", "",
		/*  J */ "BMC_READY", "BMC_PCH_BIOS_CS_N", "", "P0_MA_DDR_QS_CS_N",
			"", "", "", "",
		/*  K */ "", "", "", "", "", "", "", "",
		/*  L */ "", "", "", "", "", "", "", "",
		/*  M */ "", "", "MEZZ_PWRBRK_N", "OCP_HP_RST_EN",
			"MAIN_PWR_EN_G", "BMC_MAIN_EN", "AUX_PWR_EN_G", "BMC_AUX_EN",
		/*  N */ "", "", "", "", "", "", "", "",
		/*  O */ "", "", "", "", "", "", "", "",
		/*  P */ "", "", "", "", "", "", "", "",
		/*  Q */ "", "", "", "",
			"BMC_SMB_PRESENT_1_N", "BMC_SMB_PRESENT_2_N",
			"BMC_SMB_PRESENT_3_N", "BMC_PCIE_WAKE_N",
		/*  R */ "", "", "THERMALTRIP_CLEAR_N", "", "", "", "", "",
		/*  S */ "", "", "", "", "", "", "", "",
		/*  T */ "", "", "", "", "", "", "", "",
		/*  U */ "", "", "", "", "", "", "", "",
		/*  V */ "", "", "", "", "", "", "", "",
		/*  W */ "", "", "", "", "", "", "", "",
		/*  X */ "", "", "", "", "", "", "", "",
		/*  Y */ "SLP_S3", "SLP_S4_S5", "NODE_ID_1", "NODE_ID_2", "", "", "", "",
		/*  Z */ "", "", "SYSTEM_FAULT_LED_N", "FAST_THROTTLE_N",
			"", "", "", "",
		/* AA */ "FM_CPU0_IBMC_THERMTRIP_N", "", "PROCHOT_L_G", "",
			"", "", "", "",
		/* AB */ "BMC_FORCE_SELFREFRESH", "PWRGD_OUT", "", "IRQ_BMC_PCH_SMI_LPC_N",
			"", "", "", "",
		/* AC */ "", "", "", "", "", "", "", "";
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
		&pinctrl_adc1_default
		&pinctrl_adc2_default
		&pinctrl_adc3_default
		&pinctrl_adc4_default
		&pinctrl_adc5_default
		&pinctrl_adc6_default
		&pinctrl_adc7_default
		&pinctrl_adc8_default
		&pinctrl_adc9_default
		&pinctrl_adc10_default
		&pinctrl_adc11_default
		&pinctrl_adc12_default
		&pinctrl_adc13_default
		&pinctrl_adc14_default
		&pinctrl_adc15_default>;
};
