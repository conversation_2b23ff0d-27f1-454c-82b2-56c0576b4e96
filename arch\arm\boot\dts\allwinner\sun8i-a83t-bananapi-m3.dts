/*
 * Copyright 2017 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun8i-a83t.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Banana Pi BPI-M3";
	compatible = "sinovoip,bpi-m3", "allwinner,sun8i-a83t";

	aliases {
		ethernet0 = &emac;
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "bananapi-m3:blue:usr";
			gpios = <&axp_gpio 1 GPIO_ACTIVE_HIGH>;
		};

		led-1 {
			label = "bananapi-m3:green:usr";
			gpios = <&axp_gpio 0 GPIO_ACTIVE_HIGH>;
		};
	};

	reg_usb1_vbus: reg-usb1-vbus {
		compatible = "regulator-fixed";
		regulator-name = "usb1-vbus";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
		enable-active-high;
		gpio = <&pio 3 24 GPIO_ACTIVE_HIGH>; /* PD24 */
	};

	wifi_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&ac100_rtc 1>;
		clock-names = "ext_clock";
		/* The WiFi low power clock must be 32768 Hz */
		assigned-clocks = <&ac100_rtc 1>;
		assigned-clock-rates = <32768>;
		/* enables internal regulator and de-asserts reset */
		reset-gpios = <&r_pio 0 2 GPIO_ACTIVE_LOW>; /* PL2 WL-PMU-EN */
	};

	/*
	 * Power supply for the SATA disk, behind a USB-SATA bridge.
	 * Since it is a USB device, there is no consumer in the DT, so we
	 * have to keep this always on.
	 */
	regulator-sata-disk-pwr {
		compatible = "regulator-fixed";
		regulator-name = "sata-disk-pwr";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		enable-active-high;
		gpio = <&pio 3 25 GPIO_ACTIVE_HIGH>; /* PD25 */
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&cpu100 {
	cpu-supply = <&reg_dcdc3>;
};

&de {
	status = "okay";
};

&ehci0 {
	/* Terminus Tech FE 1.1s 4-port USB 2.0 hub here */
	status = "okay";

	/* TODO GL830 USB-to-SATA bridge downstream w/ GPIO power controls */
};

&emac {
	pinctrl-names = "default";
	pinctrl-0 = <&emac_rgmii_pins>;
	phy-supply = <&reg_sw>;
	phy-handle = <&rgmii_phy>;
	phy-mode = "rgmii-id";
	allwinner,rx-delay-ps = <700>;
	allwinner,tx-delay-ps = <700>;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&mdio {
	rgmii_phy: ethernet-phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <1>;
	};
};

&mmc0 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc0_pins>;
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	status = "okay";
};

&mmc1 {
	vmmc-supply = <&reg_dldo1>;
	vqmmc-supply = <&reg_dldo1>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	brcmf: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&r_pio>;
		interrupts = <0 3 IRQ_TYPE_LEVEL_LOW>;
		interrupt-names = "host-wake";
	};
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_emmc_pins>;
	vmmc-supply = <&reg_dcdc1>;
	vqmmc-supply = <&reg_dcdc1>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&r_cir {
	clock-frequency = <3000000>;
	status = "okay";
};

&r_rsb {
	status = "okay";

	axp81x: pmic@3a3 {
		compatible = "x-powers,axp813";
		reg = <0x3a3>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		eldoin-supply = <&reg_dcdc1>;
		fldoin-supply = <&reg_dcdc5>;
		swin-supply = <&reg_dcdc1>;
		x-powers,drive-vbus-en;
	};

	ac100: codec@e89 {
		compatible = "x-powers,ac100";
		reg = <0xe89>;

		ac100_codec: codec {
			compatible = "x-powers,ac100-codec";
			interrupt-parent = <&r_pio>;
			interrupts = <0 11 IRQ_TYPE_LEVEL_LOW>; /* PL11 */
			#clock-cells = <0>;
			clock-output-names = "4M_adda";
		};

		ac100_rtc: rtc {
			compatible = "x-powers,ac100-rtc";
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
			clocks = <&ac100_codec>;
			#clock-cells = <1>;
			clock-output-names = "cko1_rtc",
					     "cko2_rtc",
					     "cko3_rtc";
		};
	};
};

#include "axp81x.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&reg_aldo1 {
	regulator-always-on;
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "vcc-1v8";
};

&reg_aldo2 {
	regulator-always-on;
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "dram-pll";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_dcdc1 {
	/* schematics says 3.1V but FEX file says 3.3V */
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-3v3";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-cpua";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-cpub";
};

&reg_dcdc4 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-gpu";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1200000>;
	regulator-name = "vcc-dram";
};

&reg_dcdc6 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <900000>;
	regulator-name = "vdd-sys";
};

&reg_dldo1 {
	/*
	 * This powers both the WiFi/BT module's main power, I/O supply,
	 * and external pull-ups on all the data lines. It should be set
	 * to the same voltage as the I/O supply (DCDC1 in this case) to
	 * avoid any leakage or mismatch.
	 */
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi";
};

&reg_dldo3 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-pd";
};

&reg_drivevbus {
	regulator-name = "usb0-vbus";
	status = "okay";
};

&reg_fldo1 {
	regulator-min-microvolt = <1080000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd12-hsic";
};

&reg_fldo2 {
	/*
	 * Despite the embedded CPUs core not being used in any way,
	 * this must remain on or the system will hang.
	 */
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-cpus";
};

&reg_rtc_ldo {
	regulator-name = "vcc-rtc";
};

&reg_sw {
	/*
	 * The PHY requires 20ms after all voltages
	 * are applied until core logic is ready and
	 * 30ms after the reset pin is de-asserted.
	 * Set a 100ms delay to account for PMIC
	 * ramp time and board traces.
	 */
	regulator-enable-ramp-delay = <100000>;
	regulator-name = "vcc-ephy";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>, <&uart1_rts_cts_pins>;
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		clocks = <&ac100_rtc 1>;
		clock-names = "lpo";
		vbat-supply = <&reg_dldo1>;
		vddio-supply = <&reg_dldo1>;
		device-wakeup-gpios = <&pio 7 9 GPIO_ACTIVE_HIGH>; /* PH9 */
		host-wakeup-gpios = <&r_pio 0 5 GPIO_ACTIVE_HIGH>; /* PL5 */
		shutdown-gpios = <&r_pio 0 4 GPIO_ACTIVE_HIGH>; /* PL4 */
	};
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 11 GPIO_ACTIVE_HIGH>; /* PH11 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_drivevbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};
