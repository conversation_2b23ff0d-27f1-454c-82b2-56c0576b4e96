// SPDX-License-Identifier: GPL-2.0
// Copyright (c) 2020 AMD Inc.
// Author: <PERSON><PERSON><PERSON> <<EMAIL>>
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "AMD EthanolX BMC";
	compatible = "amd,ethanolx-bmc", "aspeed,ast2500";

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};


	aliases {
		serial0 = &uart1;
		serial4 = &uart5;
	};
	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};
	leds {
		compatible = "gpio-leds";

		fault {
			gpios = <&gpio ASPEED_GPIO(A, 2) GPIO_ACTIVE_LOW>;
		};

		identify {
			gpios = <&gpio ASPEED_GPIO(A, 3) GPIO_ACTIVE_LOW>;
		};
	};
	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>, <&adc 4>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bios";
		spi-max-frequency = <100000000>;
	};
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
};

&uart1 {
	//Host Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default
		     &pinctrl_nrts1_default
		     &pinctrl_ncts1_default>;
};

&uart5 {
	//BMC Console
	status = "okay";
};

&adc {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
		     &pinctrl_adc1_default
		     &pinctrl_adc2_default
		     &pinctrl_adc3_default
		     &pinctrl_adc4_default>;
};

&gpio {
	status = "okay";
	gpio-line-names =
	/*A0-A7*/	"","","FAULT_LED","CHASSIS_ID_LED","","","","",
	/*B0-B7*/	"","","","","","","","",
	/*C0-C7*/	"CHASSIS_ID_BTN","INTRUDER","AC_LOSS","","","","","",
	/*D0-D7*/	"HDT_DBREQ","LOCAL_SPI_ROM_SEL","FPGA_SPI_ROM_SEL","JTAG_MUX_S",
			"JTAG_MUX_OE","HDT_SEL","ASERT_WARM_RST_BTN","FPGA_RSVD",
	/*E0-E7*/	"","","MON_P0_PWR_BTN","MON_P0_RST_BTN","MON_P0_NMI_BTN",
			"MON_P0_PWR_GOOD","MON_PWROK","MON_RESET",
	/*F0-F7*/	"MON_P0_PROCHOT","MON_P1_PROCHOT","MON_P0_THERMTRIP",
			"MON_P1_THERMTRIP","P0_PRESENT","P1_PRESENT","MON_ATX_PWR_OK","",
	/*G0-G7*/	"BRD_REV_ID_3","BRD_REV_ID_2","BRD_REV_ID_1","BRD_REV_ID_0",
			"P0_APML_ALERT","P1_APML_ALERT","FPGA ALERT","",
	/*H0-H7*/	"BRD_ID_0","BRD_ID_1","BRD_ID_2","BRD_ID_3",
			"PCIE_DISCONNECTED","USB_DISCONNECTED","SPARE_0","SPARE_1",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"ASSERT_PWR_BTN","ASSERT_RST_BTN","ASSERT_NMI_BTN",
			"ASSERT_LOCAL_LOCK","ASSERT_P0_PROCHOT","ASSERT_P1_PROCHOT",
			"ASSERT_CLR_CMOS","ASSERT_BMC_READY",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"P0_VDD_CORE_RUN_VRHOT","P0_VDD_SOC_RUN_VRHOT",
			"P0_VDD_MEM_ABCD_SUS_VRHOT","P0_VDD_MEM_EFGH_SUS_VRHOT",
			"P1_VDD_CORE_RUN_VRHOT","P1_VDD_SOC_RUN_VRHOT",
			"P1_VDD_MEM_ABCD_SUS_VRHOT","P1_VDD_MEM_EFGH_SUS_VRHOT",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","","","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","",
	/*AA0-AA7*/	"","SENSOR THERM","","","","","","",
	/*AB0-AB7*/	"","","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";
};

//APML for P0
&i2c0 {
	status = "okay";
};

//APML for P1
&i2c1 {
	status = "okay";
};

//FPGA
&i2c2 {
	status = "okay";
};

//24LC128 EEPROM
&i2c3 {
	status = "okay";
	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
		pagesize = <64>;
	};
};

//P0 Power regulators
&i2c4 {
	status = "okay";
};

//P1 Power regulators
&i2c5 {
	status = "okay";
};

//P0/P1 Thermal diode
&i2c6 {
	status = "okay";
};

// Thermal Sensors
&i2c7 {
	status = "okay";

	lm75a@48 {
		compatible = "national,lm75a";
		reg = <0x48>;
	};

	lm75a@49 {
		compatible = "national,lm75a";
		reg = <0x49>;
	};

	lm75a@4a {
		compatible = "national,lm75a";
		reg = <0x4a>;
	};

	lm75a@4b {
		compatible = "national,lm75a";
		reg = <0x4b>;
	};

	lm75a@4c {
		compatible = "national,lm75a";
		reg = <0x4c>;
	};

	lm75a@4d {
		compatible = "national,lm75a";
		reg = <0x4d>;
	};

	lm75a@4e {
		compatible = "national,lm75a";
		reg = <0x4e>;
	};

	lm75a@4f {
		compatible = "national,lm75a";
		reg = <0x4f>;
	};
};

//BMC I2C
&i2c8 {
	status = "okay";
};

&kcs1 {
	status = "okay";
	aspeed,lpc-io-reg = <0x60>;
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0x62>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xCA2>;
};

&kcs4 {
	status = "okay";
	aspeed,lpc-io-reg = <0x97DE>;
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>, <0x81>;
};

&lpc_ctrl {
	//Enable lpc clock
	status = "okay";
};

&vuart {
	status = "okay";
	aspeed,lpc-io-reg = <0x3f8>;
	aspeed,lpc-interrupts = <4 IRQ_TYPE_LEVEL_HIGH>;
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default
	&pinctrl_pwm1_default
	&pinctrl_pwm2_default
	&pinctrl_pwm3_default
	&pinctrl_pwm4_default
	&pinctrl_pwm5_default
	&pinctrl_pwm6_default
	&pinctrl_pwm7_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};

	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};

	fan@5 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05>;
	};

	fan@6 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06>;
	};

	fan@7 {
		reg = <0x07>;
		aspeed,fan-tach-ch = /bits/ 8 <0x07>;
	};
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&vhub {
	status = "okay";
};

