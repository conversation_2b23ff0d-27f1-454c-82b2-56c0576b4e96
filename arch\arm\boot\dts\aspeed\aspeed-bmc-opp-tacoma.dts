// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2019 IBM Corp.
/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "Tacoma";
	compatible = "ibm,tacoma-bmc", "aspeed,ast2600";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200n8 earlycon";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		flash_memory: region@b8000000 {
			no-map;
			reg = <0xb8000000 0x4000000>; /* 64M */
		};

		ramoops@bc000000 {
			compatible = "ramoops";
			reg = <0xbc000000 0x180000>; /* 16 * (3 * 0x8000) */
			record-size = <0x8000>;
			console-size = <0x8000>;
			pmsg-size = <0x8000>;
			max-reason = <3>; /* KMSG_DUMP_EMERG */
		};

		vga_memory: region@bf000000 {
			no-map;
			compatible = "shared-dma-pool";
			reg = <0xbf000000 0x01000000>;	/* 16M */
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-ps0-presence {
			label = "ps0-presence";
			gpios = <&gpio0 ASPEED_GPIO(H, 3) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(H, 3)>;
		};

		event-ps1-presence {
			label = "ps1-presence";
			gpios = <&gpio0 ASPEED_GPIO(E, 5) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(E, 5)>;
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-fan0-presence {
			label = "fan0-presence";
			gpios = <&pca0 4 GPIO_ACTIVE_LOW>;
			linux,code = <4>;
		};

		event-fan1-presence {
			label = "fan1-presence";
			gpios = <&pca0 5 GPIO_ACTIVE_LOW>;
			linux,code = <5>;
		};

		event-fan2-presence {
			label = "fan2-presence";
			gpios = <&pca0 6 GPIO_ACTIVE_LOW>;
			linux,code = <6>;
		};

		event-fan3-presence {
			label = "fan3-presence";
			gpios = <&pca0 7 GPIO_ACTIVE_LOW>;
			linux,code = <7>;
		};
	};

	iio-hwmon-dps310 {
		compatible = "iio-hwmon";
		io-channels = <&dps 0>;
	};

	iio-hwmon-bmp280 {
		compatible = "iio-hwmon";
		io-channels = <&bmp 1>;
	};
};

&ehci1 {
	status = "okay";
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"fsi-mux","","","","","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"power-button","","","checkstop","","presence-ps1","","led-rear-fault",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","presence-ps0","","","","",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"led-rear-power","led-rear-id","","usb-power","","","","",
	/*P0-P7*/	"","","","","","bmc-tpm-reset","","",
	/*Q0-Q7*/	"cfam-reset","","","","","","","fsi-routing",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","","","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","";
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <********>;
#include "openbmc-flash-layout-128.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
		spi-max-frequency = <********>;
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-max-frequency = <*********>;
	};
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC3CLK>,
		 <&syscon ASPEED_CLK_MAC3RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&emmc_controller {
	status = "okay";
};

&emmc {
	status = "okay";
	clk-phase-mmc-hs200 = <36>, <270>;
};

&fsim0 {
	status = "okay";

	#address-cells = <2>;
	#size-cells = <0>;

	fsi-routing-gpios = <&gpio0 ASPEED_GPIO(Q, 7) GPIO_ACTIVE_HIGH>;
	fsi-mux-gpios = <&gpio0 ASPEED_GPIO(B, 0) GPIO_ACTIVE_HIGH>;

	cfam@0,0 {
		reg = <0 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <0>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam0_i2c0: i2c-bus@0 {
				reg = <0>;
			};

			cfam0_i2c1: i2c-bus@1 {
				reg = <1>;
			};

			cfam0_i2c2: i2c-bus@2 {
				reg = <2>;
			};

			cfam0_i2c3: i2c-bus@3 {
				reg = <3>;
			};

			cfam0_i2c4: i2c-bus@4 {
				reg = <4>;
			};

			cfam0_i2c5: i2c-bus@5 {
				reg = <5>;
			};

			cfam0_i2c6: i2c-bus@6 {
				reg = <6>;
			};

			cfam0_i2c7: i2c-bus@7 {
				reg = <7>;
			};

			cfam0_i2c8: i2c-bus@8 {
				reg = <8>;
			};

			cfam0_i2c9: i2c-bus@9 {
				reg = <9>;
			};

			cfam0_i2c10: i2c-bus@a {
				reg = <10>;
			};

			cfam0_i2c11: i2c-bus@b {
				reg = <11>;
			};

			cfam0_i2c12: i2c-bus@c {
				reg = <12>;
			};

			cfam0_i2c13: i2c-bus@d {
				reg = <13>;
			};

			cfam0_i2c14: i2c-bus@e {
				reg = <14>;
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ0: occ {
				compatible = "ibm,p9-occ";
			};
		};

		fsi_hub0: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};
};

&fsi_hub0 {
	cfam@1,0 {
		reg = <1 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <1>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam1_i2c0: i2c-bus@0 {
				reg = <0>;
			};

			cfam1_i2c1: i2c-bus@1 {
				reg = <1>;
			};

			cfam1_i2c2: i2c-bus@2 {
				reg = <2>;
			};

			cfam1_i2c3: i2c-bus@3 {
				reg = <3>;
			};

			cfam1_i2c4: i2c-bus@4 {
				reg = <4>;
			};

			cfam1_i2c5: i2c-bus@5 {
				reg = <5>;
			};

			cfam1_i2c6: i2c-bus@6 {
				reg = <6>;
			};

			cfam1_i2c7: i2c-bus@7 {
				reg = <7>;
			};

			cfam1_i2c8: i2c-bus@8 {
				reg = <8>;
			};

			cfam1_i2c9: i2c-bus@9 {
				reg = <9>;
			};

			cfam1_i2c10: i2c-bus@a {
				reg = <10>;
			};

			cfam1_i2c11: i2c-bus@b {
				reg = <11>;
			};

			cfam1_i2c12: i2c-bus@c {
				reg = <12>;
			};

			cfam1_i2c13: i2c-bus@d {
				reg = <13>;
			};

			cfam1_i2c14: i2c-bus@e {
				reg = <14>;
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ1: occ {
				compatible = "ibm,p9-occ";
			};
		};

		fsi_hub1: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};
};

/* Legacy OCC numbering (to get rid of when userspace is fixed) */
&fsi_occ0 {
	reg = <1>;
};

&fsi_occ1 {
	reg = <2>;
};

/ {
	aliases {
		i2c100 = &cfam0_i2c0;
		i2c101 = &cfam0_i2c1;
		i2c102 = &cfam0_i2c2;
		i2c103 = &cfam0_i2c3;
		i2c104 = &cfam0_i2c4;
		i2c105 = &cfam0_i2c5;
		i2c106 = &cfam0_i2c6;
		i2c107 = &cfam0_i2c7;
		i2c108 = &cfam0_i2c8;
		i2c109 = &cfam0_i2c9;
		i2c110 = &cfam0_i2c10;
		i2c111 = &cfam0_i2c11;
		i2c112 = &cfam0_i2c12;
		i2c113 = &cfam0_i2c13;
		i2c114 = &cfam0_i2c14;
		i2c200 = &cfam1_i2c0;
		i2c201 = &cfam1_i2c1;
		i2c202 = &cfam1_i2c2;
		i2c203 = &cfam1_i2c3;
		i2c204 = &cfam1_i2c4;
		i2c205 = &cfam1_i2c5;
		i2c206 = &cfam1_i2c6;
		i2c207 = &cfam1_i2c7;
		i2c208 = &cfam1_i2c8;
		i2c209 = &cfam1_i2c9;
		i2c210 = &cfam1_i2c10;
		i2c211 = &cfam1_i2c11;
		i2c212 = &cfam1_i2c12;
		i2c213 = &cfam1_i2c13;
		i2c214 = &cfam1_i2c14;
	};

};

&i2c0 {
	multi-master;
	status = "okay";

	ibm-panel@62 {
		compatible = "ibm,op-panel";
		reg = <(0x62 | I2C_OWN_SLAVE_ADDRESS)>;
	};
};

&i2c1 {
	status = "okay";

	tpm: tpm@2e {
		compatible = "nuvoton,npct75x", "tcg,tpm-tis-i2c";
		reg = <0x2e>;
	};
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";

	bmp: bmp280@77 {
		compatible = "bosch,bmp280";
		reg = <0x77>;
		#io-channel-cells = <1>;
	};

	max31785@52 {
		compatible = "maxim,max31785a";
		reg = <0x52>;
		#address-cells = <1>;
		#size-cells = <0>;

		fan@0 {
			compatible = "pmbus-fan";
			reg = <0>;
			tach-pulses = <2>;
			maxim,fan-rotor-input = "tach";
			maxim,fan-pwm-freq = <25000>;
			maxim,fan-dual-tach;
			maxim,fan-no-watchdog;
			maxim,fan-no-fault-ramp;
			maxim,fan-ramp = <2>;
			maxim,fan-fault-pin-mon;
		};

		fan@1 {
			compatible = "pmbus-fan";
			reg = <1>;
			tach-pulses = <2>;
			maxim,fan-rotor-input = "tach";
			maxim,fan-pwm-freq = <25000>;
			maxim,fan-dual-tach;
			maxim,fan-no-watchdog;
			maxim,fan-no-fault-ramp;
			maxim,fan-ramp = <2>;
			maxim,fan-fault-pin-mon;
		};

		fan@2 {
			compatible = "pmbus-fan";
			reg = <2>;
			tach-pulses = <2>;
			maxim,fan-rotor-input = "tach";
			maxim,fan-pwm-freq = <25000>;
			maxim,fan-dual-tach;
			maxim,fan-no-watchdog;
			maxim,fan-no-fault-ramp;
			maxim,fan-ramp = <2>;
			maxim,fan-fault-pin-mon;
		};

		fan@3 {
			compatible = "pmbus-fan";
			reg = <3>;
			tach-pulses = <2>;
			maxim,fan-rotor-input = "tach";
			maxim,fan-pwm-freq = <25000>;
			maxim,fan-dual-tach;
			maxim,fan-no-watchdog;
			maxim,fan-no-fault-ramp;
			maxim,fan-ramp = <2>;
			maxim,fan-fault-pin-mon;
		};
	};

	dps: dps310@76 {
		compatible = "infineon,dps310";
		reg = <0x76>;
		#io-channel-cells = <0>;
	};

	pca0: pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio@0 {
			reg = <0>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@1 {
			reg = <1>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@2 {
			reg = <2>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@3 {
			reg = <3>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@4 {
			reg = <4>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@5 {
			reg = <5>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@6 {
			reg = <6>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@7 {
			reg = <7>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@8 {
			reg = <8>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@9 {
			reg = <9>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@10 {
			reg = <10>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@11 {
			reg = <11>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@12 {
			reg = <12>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@13 {
			reg = <13>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@14 {
			reg = <14>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@15 {
			reg = <15>;
			type = <PCA955X_TYPE_GPIO>;
		};
	};

	power-supply@68 {
		compatible = "ibm,cffps1";
		reg = <0x68>;
	};

	power-supply@69 {
		compatible = "ibm,cffps1";
		reg = <0x69>;
	};
};

&i2c4 {
	status = "okay";

	tmp423a@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	ir35221@70 {
		compatible = "infineon,ir35221";
		reg = <0x70>;
	};

	ir35221@71 {
		compatible = "infineon,ir35221";
		reg = <0x71>;
	};
};

&i2c5 {
	status = "okay";

	tmp423a@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	ir35221@70 {
		compatible = "infineon,ir35221";
		reg = <0x70>;
	};

	ir35221@71 {
		compatible = "infineon,ir35221";
		reg = <0x71>;
	};
};

&i2c7 {
	status = "okay";
};

&i2c9 {
	status = "okay";

	tmp275@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";

	pca9552: pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names = "PS_SMBUS_RESET_N", "APSS_RESET_N",
			"GPU0_TH_OVERT_N_BUFF",	"GPU1_TH_OVERT_N_BUFF",
			"GPU2_TH_OVERT_N_BUFF", "GPU3_TH_OVERT_N_BUFF",
			"GPU4_TH_OVERT_N_BUFF",	"GPU5_TH_OVERT_N_BUFF",
			"GPU0_PWR_GOOD_BUFF", "GPU1_PWR_GOOD_BUFF",
			"GPU2_PWR_GOOD_BUFF", "GPU3_PWR_GOOD_BUFF",
			"GPU4_PWR_GOOD_BUFF", "GPU5_PWR_GOOD_BUFF",
			"12V_BREAKER_FLT_N", "THROTTLE_UNLATCHED_N";

		gpio@0 {
			reg = <0>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@1 {
			reg = <1>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@2 {
			reg = <2>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@3 {
			reg = <3>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@4 {
			reg = <4>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@5 {
			reg = <5>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@6 {
			reg = <6>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@7 {
			reg = <7>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@8 {
			reg = <8>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@9 {
			reg = <9>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@10 {
			reg = <10>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@11 {
			reg = <11>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@12 {
			reg = <12>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@13 {
			reg = <13>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@14 {
			reg = <14>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@15 {
			reg = <15>;
			type = <PCA955X_TYPE_GPIO>;
		};
	};

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	ucd90160@64 {
		compatible = "ti,ucd90160";
		reg = <0x64>;
	};
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&ibt {
	status = "okay";
};

&uart1 {
	status = "okay";
	// Workaround for A0
	compatible = "snps,dw-apb-uart";
};

&uart5 {
	// Workaround for A0
	compatible = "snps,dw-apb-uart";
};

&vuart1 {
	status = "okay";
};

&vuart2 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	status = "okay";
};

&pinctrl {
	/* Hog these as no driver is probed for the entire LPC block */
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lpc_default>,
		    <&pinctrl_lsirq_default>;
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8 0xcac>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
	aspeed,lpc-interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
};
