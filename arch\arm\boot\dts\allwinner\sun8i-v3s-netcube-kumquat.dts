// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2025 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun8i-v3s.dtsi"

#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/gpio/gpio.h>

/{
	model = "NetCube Systems Kumquat";
	compatible = "netcube,kumquat", "allwinner,sun8i-v3s";

	aliases {
		serial0 = &uart0;
		ethernet0 = &emac;
		rtc0 = &ds3232;
		rtc1 = &rtc; /* not battery backed */
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	/* 40 MHz Crystal Oscillator on PCB */
	clk_can0: clock-can0 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency  = <40000000>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		key-user {
			label = "GPIO Key User";
			linux,code = <KEY_PROG1>;
			gpios = <&pio 1 2 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PB2 */
		};
	};

	leds {
		compatible = "gpio-leds";

		led-heartbeat {
			gpios = <&pio 4 4 GPIO_ACTIVE_HIGH>; /* PE4 */
			linux,default-trigger = "heartbeat";
			color = <LED_COLOR_ID_GREEN>;
			function = LED_FUNCTION_HEARTBEAT;
		};

		led-mmc0-act {
			gpios = <&pio 5 6 GPIO_ACTIVE_HIGH>; /* PF6 */
			linux,default-trigger = "mmc0";
			color = <LED_COLOR_ID_GREEN>;
			function = LED_FUNCTION_DISK;
		};
	};

	/* EA3036C Switching 3 Channel Regulator - Channel 2 */
	reg_vcc3v3: regulator-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&reg_vcc5v0>;
	};

	/* K7805-1000R3 Switching Regulator supplied from main 12/24V terminal block */
	reg_vcc5v0: regulator-5v0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};
};

&codec {
	allwinner,audio-routing =
		"Headphone", "HP",
		"Headphone", "HPCOM",
		"MIC1", "Mic",
		"Mic", "HBIAS";
	status = "okay";
};

&ehci {
	status = "okay";
};

&emac {
	allwinner,leds-active-low;
	nvmem-cells = <&eth0_macaddress>;
	nvmem-cell-names = "mac-address";
	status = "okay";
};

&i2c0 {
	status = "okay";

	eeprom0: eeprom@50 {
		compatible = "atmel,24c02";		/* actually it's a 24AA02E48 */
		reg = <0x50>;
		pagesize = <16>;
		read-only;
		vcc-supply = <&reg_vcc3v3>;

		#address-cells = <1>;
		#size-cells = <1>;

		eth0_macaddress: macaddress@fa {
			reg = <0xfa 0x06>;
		};
	};

	tusb320: typec@60 {
		compatible = "ti,tusb320";
		reg = <0x60>;
		interrupts-extended = <&pio 1 5 IRQ_TYPE_LEVEL_LOW>;  /* PB5 */
	};

	ds3232: rtc@68 {
		compatible = "dallas,ds3232";
		reg = <0x68>;
	};
};

/* Exposed as the Flash/SD Header on the board */
&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	broken-cd;
	status = "okay";
};

/* Connected to the on-board ESP32 */
&mmc1 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	broken-cd;
	status = "okay";
};

&ohci {
	status = "okay";
};

/* Disable external 32k osc as it is broken on current revision */
&osc32k {
	status = "disabled";
};

&pio {
	vcc-pb-supply = <&reg_vcc3v3>;
	vcc-pc-supply = <&reg_vcc3v3>;
	vcc-pe-supply = <&reg_vcc3v3>;
	vcc-pf-supply = <&reg_vcc3v3>;
	vcc-pg-supply = <&reg_vcc3v3>;

	gpio-line-names = "", "", "", "", // PA
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "CAN_nCS", "CAN_nINT", "USER_SW", "PB3", // PB
			  "USB_ID", "USBC_nINT", "I2C0_SCL", "I2C0_SDA",
			  "UART0_TX", "UART0_RX", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "SPI_MISO", "SPI_SCK", "FLASH_nCS", "SPI_MOSI", // PC
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "", // PD
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "Q12", "Q11", "Q10", "Q9", // PE
			  "LED_SYS0", "I1", "Q1", "Q2",
			  "I2", "I3", "Q3", "Q4",
			  "I4", "I5", "Q5", "Q6",
			  "I6", "I7", "Q7", "Q8",
			  "I8", "UART1_TXD", "UART1_RXD", "ESP_nRST",
			  "ESP_nBOOT", "", "", "",
			  "", "", "", "",
			  "SD_D1", "SD_D0", "SD_CLK", "SD_CMD", // PF
			  "SD_D3", "SD_D2", "LED_SYS1", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "ESP_CLK", "ESP_CMD", "ESP_D0", "ESP_D1", // PG
			  "ESP_D2", "ESP_D3", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "";
};

/* Disable external 32k osc as it is broken on current revision */
&rtc {
	/delete-property/ clocks;
};

/* Exposed as a USB-C connector with USB-Serial converter */
&uart0 {
	pinctrl-0 = <&uart0_pb_pins>;
	pinctrl-names = "default";
	status = "okay";
};

/* Connected to the Bootloader/Console of the ESP32 */
&uart1 {
	pinctrl-0 = <&uart1_pe_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&usb_otg {
	extcon = <&tusb320 0>;
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 1 4 GPIO_ACTIVE_HIGH>; /* PB4 */
	status = "okay";
};

&spi0 {
	#address-cells = <1>;
	#size-cells = <0>;
	cs-gpios = <0>, <&pio 1 0 GPIO_ACTIVE_LOW>; /* PB0 */
	status = "okay";

	flash@0 {
		compatible = "jedec,spi-nor";
		reg = <0>;
		#address-cells = <1>;
		#size-cells = <1>;
		label = "firmware";
		spi-max-frequency = <40000000>;
	};

	can@1 {
		compatible = "microchip,mcp2518fd";
		reg = <1>;
		clocks = <&clk_can0>;
		interrupts-extended = <&pio 1 1 IRQ_TYPE_LEVEL_LOW>;  /* PB1 */
		spi-max-frequency = <20000000>;
		vdd-supply = <&reg_vcc3v3>;
		xceiver-supply = <&reg_vcc3v3>;
	};
};
