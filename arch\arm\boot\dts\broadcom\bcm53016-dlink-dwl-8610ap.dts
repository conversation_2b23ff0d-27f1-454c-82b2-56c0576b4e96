// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/dts-v1/;

#include "bcm4709.dtsi"
#include "bcm5301x-nand-cs0-bch8.dtsi"
#include <dt-bindings/leds/common.h>
#include <dt-bindings/input/input.h>

/ {
	model = "D-Link DWL-8610AP";
	compatible = "dlink,dwl-8610ap", "brcm,bcm53016", "brcm,bcm4708";

	memory@0 {
		device_type = "memory";
		/* 512 MB RAM in 2 x Macronix D9PSH chips */
		reg = <0x00000000 0x08000000>,
		      <0x88000000 0x08000000>;
	};

	leds {
		compatible = "gpio-leds";

		led-power {
			function = LED_FUNCTION_POWER;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&chipcommon 0 GPIO_ACTIVE_LOW>;
			default-state = "on";
		};

		led-diag {
			/* Actually "diag" unclear what this means */
			function = LED_FUNCTION_INDICATOR;
			color = <LED_COLOR_ID_RED>;
			gpios = <&chipcommon 1 GPIO_ACTIVE_LOW>;
			default-state = "on";
			linux,default-trigger = "heartbeat";
		};

		led-wlan-2g {
			function = LED_FUNCTION_WLAN;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&chipcommon 5 GPIO_ACTIVE_LOW>;
		};

		led-wlan-5g {
			function = LED_FUNCTION_WLAN;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&chipcommon 8 GPIO_ACTIVE_LOW>;
		};
	};

	gpio_keys {
		compatible = "gpio-keys";

		button-reset {
			debounce-interval = <100>;
			wakeup-source;
			linux,code = <KEY_RESTART>;
			label = "reset";
			/* This GPIO is actually stored in NVRAM, but it's not gonna change */
			gpios = <&chipcommon 4 GPIO_ACTIVE_LOW>;
		};
	};

	/*
	 * Flash memory at 0x1e000000-0x1fffffff
	 * Macronix 32 64KB blocks; total size 2MB, same that can be
	 * found attached to the spi_nor SPI controller.
	 */
	nvram@1e080000 {
		compatible = "brcm,nvram";
		reg = <0x1e080000 0x00020000>;

		et0macaddr: et0macaddr {
		};

		et1macaddr: et1macaddr {
		};
	};
};

&gmac0 {
	nvmem-cells = <&et0macaddr>;
	nvmem-cell-names = "mac-address";
};

&gmac1 {
	nvmem-cells = <&et1macaddr>;
	nvmem-cell-names = "mac-address";
};

&spi_nor {
	/* Serial SPI NOR Flash MX 25L1606E */
	status = "okay";
};

&nandcs {
	/*
	 * Spansion S34ML01G100TFI00 128 MB NAND Flash memory
	 *
	 * This ECC is a bit unorthodox but it is what the stock firmware
	 * is using, so to be able to mount the original partitions
	 * this is necessary.
	 */
	nand-ecc-strength = <5>;
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		/* This is named nflash1.trx in CFE */
		trx@0 {
			label = "firmware";
			reg = <0x00000000 0x02800000>;
			compatible = "brcm,trx";
		};

		/* This is named nflash1.trx2 in CFE */
		trx2@2800000 {
			label = "firmware2";
			reg = <0x02800000 0x02800000>;
			compatible = "brcm,trx";
		};

		/* This is named nflash1.rwfs in CFE */
		free@5000000 {
			label = "free";
			reg = <0x05000000 0x03000000>;
		};
	};
};
