// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Copyright (C) 2016 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

#include "bcm4708.dtsi"

/ {
};

&pinctrl {
	compatible = "brcm,bcm4709-pinmux";

	pinmux_mdio: mdio-pins {
		groups = "mdio_grp";
		function = "mdio";
	};
};

&usb3_phy {
	compatible = "brcm,ns-bx-usb3-phy";
};

&uart0 {
	clock-frequency = <125000000>;
	status = "okay";
};

&srab {
	compatible = "brcm,bcm53012-srab", "brcm,bcm5301x-srab";
};
