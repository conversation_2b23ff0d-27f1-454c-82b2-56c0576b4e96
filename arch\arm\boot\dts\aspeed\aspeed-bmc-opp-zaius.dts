// SPDX-License-Identifier: GPL-2.0
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "<PERSON><PERSON><PERSON>";
	compatible = "ingrasys,zaius-bmc", "aspeed,ast2500";

	aliases {
		i2c15 = &i2cpcie0;
		i2c16 = &i2cpcie1;
		i2c17 = &i2cpcie2;
		i2c19 = &i2cpcie3;
		i2c20 = &i2cpcie4;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		flash_memory: region@98000000 {
			no-map;
			reg = <0x98000000 0x04000000>; /* 64M */
		};
	};

	onewire0 {
		compatible = "w1-gpio";
		gpios = <&gpio ASPEED_GPIO(H, 0) GPIO_ACTIVE_HIGH>;
	};

	onewire1 {
		compatible = "w1-gpio";
		gpios = <&gpio ASPEED_GPIO(H, 1) GPIO_ACTIVE_HIGH>;
	};

	onewire2 {
		compatible = "w1-gpio";
		gpios = <&gpio ASPEED_GPIO(H, 2) GPIO_ACTIVE_HIGH>;
	};

	onewire3 {
		compatible = "w1-gpio";
		gpios = <&gpio ASPEED_GPIO(H, 3) GPIO_ACTIVE_HIGH>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-checkstop {
			label = "checkstop";
			gpios = <&gpio ASPEED_GPIO(F, 7) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(F, 7)>;
		};

		event-pcie-e2b-present{
			label = "pcie-e2b-present";
			gpios = <&gpio ASPEED_GPIO(E, 7) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(E, 7)>;
		};
	};

	leds {
		compatible = "gpio-leds";

		sys_boot_status {
			label = "System boot status";
			gpios = <&gpio ASPEED_GPIO(D, 5) GPIO_ACTIVE_LOW>;
		};

		attention {
			label = "Attention";
			gpios = <&gpio ASPEED_GPIO(D, 6) GPIO_ACTIVE_LOW>;
		};

		plt_fault {
			label = "Platform fault";
			gpios = <&gpio ASPEED_GPIO(D, 7) GPIO_ACTIVE_LOW>;
		};

		hdd_fault {
			label = "Onboard drive fault";
			gpios = <&gpio ASPEED_GPIO(AA, 2) GPIO_ACTIVE_LOW>;
		};
	};

	fsi: gpio-fsi {
		compatible = "fsi-master-gpio", "fsi-master";
		#address-cells = <2>;
		#size-cells = <0>;
		no-gpio-delays;

		trans-gpios = <&gpio ASPEED_GPIO(O, 6) GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio ASPEED_GPIO(D, 0) GPIO_ACTIVE_HIGH>;
		clock-gpios = <&gpio ASPEED_GPIO(G, 0) GPIO_ACTIVE_HIGH>;
		data-gpios = <&gpio ASPEED_GPIO(G, 1) GPIO_ACTIVE_HIGH>;
		mux-gpios = <&gpio ASPEED_GPIO(P, 6) GPIO_ACTIVE_HIGH>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 13>, <&adc 14>, <&adc 15>;
	};

	iio-hwmon-battery {
		compatible = "iio-hwmon";
		io-channels = <&adc 12>;
	};

};

&fmc {
	status = "okay";

	flash@0 {
		status = "okay";
		label = "bmc";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		label = "pnor";
		m25p,fast-read;
		spi-max-frequency = <100000000>;
	};
};

&spi2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi2ck_default
		     &pinctrl_spi2cs0_default
		     &pinctrl_spi2cs1_default
		     &pinctrl_spi2miso_default
		     &pinctrl_spi2mosi_default>;

	flash@0 {
		status = "okay";
	};
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default>;
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};


&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c0 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
		pagesize = <32>;
	};

	rtc@68 {
		compatible = "nxp,pcf8523";
		reg = <0x68>;
	};

	ucd90160@64 {
		compatible = "ti,ucd90160";
		reg = <0x64>;
	};

	/* Power sequencer UCD90160 PMBUS @64h
	 * FRU AT24C64D @50h
	 * RTC PCF8523 @68h
	 * Clock buffer 9DBL04 @6dh
	 */
};

&i2c1 {
	status = "okay";

	i2c-mux@71 {
		compatible = "nxp,pca9546";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;

		i2cpcie0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2cpcie1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
		i2cpcie2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		i2ctpm: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	/* MUX1 PCA9546A @71h
	 *   PCIe 0
	 *   PCIe 1
	 *   PCIe 2
	 *   TPM header
	 */
};

&i2c2 {
	status = "disabled";

	/* OCP Mezz Connector A (OOB SMBUS) */
};

&i2c3 {
	status = "disabled";

	/* OCP Mezz Connector A (PCIe slot SMBUS) */
};

&i2c4 {
	status = "okay";

	i2c-mux@71 {
		compatible = "nxp,pca9546";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;

		i2cpcie3: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		i2cpcie4: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
	};

	/* MUX1 PCA9546A @71h
	 *   PCIe 3
	 *   PCIe 4
	 */
};


&i2c5 {
	status = "disabled";

	/* CPU0 PRM 0.7V */
	/* CPU0 PRM 1.2V CH03 */
	/* CPU0 PRM 0.8V */
	/* CPU0 PRM 1.2V CH47 */
};

&i2c6 {
	status = "disabled";

	/* CPU1 PRM 0.7V */
	/* CPU1 PRM 1.2V CH03 */
	/* CPU1 PRM 0.8V */
	/* CPU1 PRM 1.2V CH47 */
};

&i2c7 {
	status = "okay";

	pca9541a@70 {
		compatible = "nxp,pca9541";
		reg = <0x70>;

		i2c-arb {
			#address-cells = <1>;
			#size-cells = <0>;

			hotswap@54 {
				compatible = "ti,lm5066i";
				reg = <0x54>;
			};
		};

	};

	vrm@64 {
		compatible = "isil,isl68137";
		reg = <0x64>;
	};

	vrm@40 {
		compatible = "isil,isl68137";
		reg = <0x40>;
	};

	vrm@60 {
		compatible = "isil,isl68137";
		reg = <0x60>;
	};

	vrm@43 {
		compatible = "infineon,ir38064";
		reg = <0x43>;
	};

	vrm@41 {
		compatible = "isil,isl68137";
		reg = <0x41>;
	};

	/* Master selector PCA9541A @70h (other master: CPU0)
	 *   LM5066I PMBUS @10h
	 */

	/*
	 * Brick will be one of these types/addresses.  Depending
	 * on the board SKU only one is actually present and will successfully
	 * instantiate while the others will fail the probe operation.
	 * These are the PVT (and presumably beyond) addresses:
	 *    12V Quarter Brick DC/DC Converter Q54SJ12050 @6Ah
	 *    12V Quarter Brick DC/DC Converter Q54SH12050 @30h
	 */
	power-brick@6a {
		compatible = "delta,dps800";
		reg = <0x6a>;
	};
	power-brick@30 {
		compatible = "delta,dps800";
		reg = <0x30>;
	};

	/* CPU0 VR ISL68137 0.7V, 0.96V PMBUS @64h */
	/* CPU0 VR ISL68137 1.2V CH03 PMBUS @40h */
	/* CPU0 VR ISL68137 0.8V PMBUS @60h */
	/* CPU0 VR 1.0V IR38064 I2C @11h, PMBUS @43h */
	/* CPU0 VR ISL68137 1.2V CH47 PMBUS @41h */
	/* Master selector PCA9541A @70h (other master: CPU0)
	 *   LM5066I PMBUS @10h
	 */
};

&i2c8 {
	status = "okay";

	vrm@64 {
		compatible = "isil,isl68137";
		reg = <0x64>;
	};

	vrm@40 {
		compatible = "isil,isl68137";
		reg = <0x40>;
	};

	vrm@41 {
		compatible = "isil,isl68137";
		reg = <0x41>;
	};

	vrm@42 {
		compatible = "infineon,ir38064";
		reg = <0x42>;
	};

	vrm@60 {
		compatible = "isil,isl68137";
		reg = <0x60>;
	};

	/* CPU1 VR ISL68137 0.7V, 0.96V PMBUS @64h */
	/* CPU1 VR ISL68137 1.2V CH03 PMBUS @40h */
	/* CPU1 VR ISL68137 1.2V CH47 PMBUS @41h */
	/* CPU1 VR 1.0V IR38064 I2C @12h, PMBUS @42h */
	/* CPU1 VR ISL68137 0.8V PMBUS @60h */
};


&i2c9 {
	status = "disabled";

	/* Fan board */
};

&i2c10 {
	status = "disabled";
};

&i2c11 {
	status = "disabled";

	/* GPU sideband */
};

&i2c12 {
	status = "disabled";
};

&i2c13 {
	status = "disabled";

	/* MUX PI3USB102
	 *   CPU0 debug
	 *   CPU1 debug
	 */
};

&pinctrl {
	pinctrl_gpioh_unbiased: gpioi_unbiased {
		pins = "A8", "C7", "B7", "A7", "D7", "B6", "A6", "E7";
		bias-disable;
	};
};

&gpio {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_gpioh_unbiased>;

	gpio-line-names =
	/*A0-A7*/	"","cfam-reset","","","","","","",
	/*B0-B7*/	"","","","","","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"fsi-enable","","","","","led-sys-boot-status","led-attention",
				"led-fault",
	/*E0-E7*/	"","","","","","","","presence-pcie-e2b",
	/*F0-F7*/	"","","","","","","","checkstop",
	/*G0-G7*/	"fsi-clock","fsi-data","","","","","","",
	/*H0-H7*/	"onewire0","onewire1","onewire2","onewire3","","","","",
	/*I0-I7*/	"","","","power-button","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","","iso_u164_en","","fsi-trans","",
	/*P0-P7*/	"ncsi_mux_en_n","bmc_i2c2_sw_rst_n","","bmc_i2c5_sw_rst_n","",
				"","fsi-mux","",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","","","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","",
	/*AA0-AA7*/	"","","led-hdd-fault","","","","","",
	/*AB0-AB7*/	"","","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";

	line_iso_u146_en {
		gpio-hog;
		gpios = <ASPEED_GPIO(O, 4) GPIO_ACTIVE_HIGH>;
		output-high;
	};

	ncsi_mux_en_n {
		gpio-hog;
		gpios = <ASPEED_GPIO(P, 0) GPIO_ACTIVE_HIGH>;
		output-low;
	};

	line_bmc_i2c2_sw_rst_n {
		gpio-hog;
		gpios = <ASPEED_GPIO(P, 1) GPIO_ACTIVE_HIGH>;
		output-high;
	};

	line_bmc_i2c5_sw_rst_n {
		gpio-hog;
		gpios = <ASPEED_GPIO(P, 3) GPIO_ACTIVE_HIGH>;
		output-high;
	};
};

&vuart {
	status = "okay";
};

&gfx {
	status = "okay";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default
		&pinctrl_pwm2_default &pinctrl_pwm3_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};
};

&ibt {
	status = "okay";
};

#include "ibm-power9-dual.dtsi"
