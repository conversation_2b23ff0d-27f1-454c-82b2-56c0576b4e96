// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2022 Broadcom Ltd.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	compatible = "brcm,bcm63148", "brcm,bcmbca";
	#address-cells = <1>;
	#size-cells = <1>;

	interrupt-parent = <&gic>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		B15_0: cpu@0 {
			device_type = "cpu";
			compatible = "brcm,brahma-b15";
			reg = <0x0>;
			next-level-cache = <&L2_0>;
			enable-method = "psci";
		};

		B15_1: cpu@1 {
			device_type = "cpu";
			compatible = "brcm,brahma-b15";
			reg = <0x1>;
			next-level-cache = <&L2_0>;
			enable-method = "psci";
		};

		L2_0: l2-cache0 {
			compatible = "cache";
			cache-level = <2>;
			cache-unified;
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>;
	};

	pmu: pmu {
		compatible = "arm,cortex-a15-pmu";
		interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&B15_0>, <&B15_1>;
	};

	clocks: clocks {
		periph_clk: periph-clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <50000000>;
		};

		hsspi_pll: hsspi-pll {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <400000000>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	axi@80030000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x80030000 0x8000>;

		gic: interrupt-controller@1000 {
			compatible = "arm,cortex-a15-gic";
			#interrupt-cells = <3>;
			interrupt-controller;
			reg = <0x1000 0x1000>,
				<0x2000 0x2000>,
				<0x4000 0x2000>,
				<0x6000 0x2000>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(2) |
					IRQ_TYPE_LEVEL_HIGH)>;
		};
	};

	bus@ff800000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xfffe8000 0x8000>;

		uart0: serial@600 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x600 0x20>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&periph_clk>;
			clock-names = "refclk";
			status = "disabled";
		};

		hsspi: spi@1000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,bcm63148-hsspi", "brcm,bcmbca-hsspi-v1.0";
			reg = <0x1000 0x600>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&hsspi_pll &hsspi_pll>;
			clock-names = "hsspi", "pll";
			num-cs = <8>;
			status = "disabled";
		};

		nand_controller: nand-controller@2000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,nand-bcm63138", "brcm,brcmnand-v7.1", "brcm,brcmnand";
			reg = <0x2000 0x600>, <0xf0 0x10>;
			reg-names = "nand", "nand-int-base";
			status = "disabled";

			nandcs: nand@0 {
				compatible = "brcm,nandcs";
				reg = <0>;
			};
		};
	};
};
