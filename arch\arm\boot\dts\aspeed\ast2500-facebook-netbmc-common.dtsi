// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2019 Facebook Inc.

#include "aspeed-g5.dtsi"

/ {
	aliases {
		spi0 = &fmc;
	};

	memory@80000000 {
		reg = <0x80000000 0x40000000>;
	};
};

/*
 * Update reset type to "system" (full chip) to fix warm reboot hang issue
 * when reset type is set to default ("soc", gated by reset mask registers).
 */
&wdt1 {
	status = "okay";
	aspeed,reset-type = "system";
};

&wdt2 {
	status = "disabled";
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default>;
};

&uart3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd3_default
		     &pinctrl_rxd3_default>;
};

&uart5 {
	status = "okay";
};

&fmc {
	status = "okay";

	fmc_flash0: flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "spi0.0";
	};

	fmc_flash1: flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "spi0.1";
	};
};

&mac1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&rtc {
	status = "okay";
};

&vhub {
	status = "okay";
};

&sdmmc {
	status = "okay";
};

&sdhci1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sd2_default>;
};
