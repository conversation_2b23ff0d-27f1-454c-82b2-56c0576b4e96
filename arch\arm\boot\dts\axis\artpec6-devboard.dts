// SPDX-License-Identifier: GPL-2.0-only
// Axis ARTPEC-6 development board.

/dts-v1/;
#include "artpec6.dtsi"

/ {
	model = "ARTPEC-6 development board";
	compatible = "axis,artpec6-dev-board", "axis,artpec6";

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;
	};

	chosen {
		stdout-path = "serial3:115200n8";
	};

	memory {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&pcie {
	status = "okay";
};

&ethernet {
	status = "okay";

	phy-handle = <&phy1>;
	phy-mode = "gmii";

	mdio {
		#address-cells = <0x1>;
		#size-cells = <0x0>;
		compatible = "snps,dwmac-mdio";
		phy1: phy@0 {
			compatible = "ethernet-phy-ieee802.3-c22";
			device_type = "ethernet-phy";
			reg = <0x0>;
		};
	};
};
