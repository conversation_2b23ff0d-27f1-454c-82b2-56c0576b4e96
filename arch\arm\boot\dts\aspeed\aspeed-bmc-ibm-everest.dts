// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2020 IBM Corp.
/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "Everest";
	compatible = "ibm,everest-bmc", "aspeed,ast2600";

	aliases {
		i2c500 = &cfam4_i2c0;
		i2c501 = &cfam4_i2c1;
		i2c510 = &cfam4_i2c10;
		i2c511 = &cfam4_i2c11;
		i2c512 = &cfam4_i2c12;
		i2c513 = &cfam4_i2c13;
		i2c514 = &cfam4_i2c14;
		i2c515 = &cfam4_i2c15;
		i2c602 = &cfam5_i2c2;
		i2c603 = &cfam5_i2c3;
		i2c610 = &cfam5_i2c10;
		i2c611 = &cfam5_i2c11;
		i2c614 = &cfam5_i2c14;
		i2c615 = &cfam5_i2c15;
		i2c616 = &cfam5_i2c16;
		i2c617 = &cfam5_i2c17;
		i2c700 = &cfam6_i2c0;
		i2c701 = &cfam6_i2c1;
		i2c710 = &cfam6_i2c10;
		i2c711 = &cfam6_i2c11;
		i2c712 = &cfam6_i2c12;
		i2c713 = &cfam6_i2c13;
		i2c714 = &cfam6_i2c14;
		i2c715 = &cfam6_i2c15;
		i2c802 = &cfam7_i2c2;
		i2c803 = &cfam7_i2c3;
		i2c810 = &cfam7_i2c10;
		i2c811 = &cfam7_i2c11;
		i2c814 = &cfam7_i2c14;
		i2c815 = &cfam7_i2c15;
		i2c816 = &cfam7_i2c16;
		i2c817 = &cfam7_i2c17;

		i2c16 = &i2c4mux0chn0;
		i2c17 = &i2c4mux0chn1;
		i2c18 = &i2c4mux0chn2;
		i2c19 = &i2c5mux0chn0;
		i2c20 = &i2c5mux0chn1;
		i2c21 = &i2c5mux0chn2;
		i2c22 = &i2c5mux0chn3;
		i2c23 = &i2c6mux0chn0;
		i2c24 = &i2c6mux0chn1;
		i2c25 = &i2c6mux0chn2;
		i2c26 = &i2c6mux0chn3;
		i2c27 = &i2c14mux0chn0;
		i2c28 = &i2c14mux0chn1;
		i2c29 = &i2c14mux0chn2;
		i2c30 = &i2c14mux0chn3;
		i2c31 = &i2c14mux1chn0;
		i2c32 = &i2c14mux1chn1;
		i2c33 = &i2c14mux1chn2;
		i2c34 = &i2c14mux1chn3;
		i2c35 = &i2c15mux0chn0;
		i2c36 = &i2c15mux0chn1;
		i2c37 = &i2c15mux0chn2;
		i2c38 = &i2c15mux0chn3;
		i2c39 = &i2c15mux1chn0;
		i2c40 = &i2c15mux1chn1;
		i2c41 = &i2c15mux1chn2;
		i2c42 = &i2c15mux1chn3;
		i2c43 = &i2c15mux2chn0;
		i2c44 = &i2c15mux2chn1;
		i2c45 = &i2c15mux2chn2;
		i2c46 = &i2c15mux2chn3;
		i2c47 = &i2c8mux0chn0;
		i2c48 = &i2c8mux0chn1;

		serial4 = &uart5;

		sbefifo500 = &sbefifo500;
		sbefifo501 = &sbefifo501;
		sbefifo510 = &sbefifo510;
		sbefifo511 = &sbefifo511;
		sbefifo512 = &sbefifo512;
		sbefifo513 = &sbefifo513;
		sbefifo514 = &sbefifo514;
		sbefifo515 = &sbefifo515;
		sbefifo602 = &sbefifo602;
		sbefifo603 = &sbefifo603;
		sbefifo610 = &sbefifo610;
		sbefifo611 = &sbefifo611;
		sbefifo614 = &sbefifo614;
		sbefifo615 = &sbefifo615;
		sbefifo616 = &sbefifo616;
		sbefifo617 = &sbefifo617;
		sbefifo700 = &sbefifo700;
		sbefifo701 = &sbefifo701;
		sbefifo710 = &sbefifo710;
		sbefifo711 = &sbefifo711;
		sbefifo712 = &sbefifo712;
		sbefifo713 = &sbefifo713;
		sbefifo714 = &sbefifo714;
		sbefifo715 = &sbefifo715;
		sbefifo802 = &sbefifo802;
		sbefifo803 = &sbefifo803;
		sbefifo810 = &sbefifo810;
		sbefifo811 = &sbefifo811;
		sbefifo814 = &sbefifo814;
		sbefifo815 = &sbefifo815;
		sbefifo816 = &sbefifo816;
		sbefifo817 = &sbefifo817;

		scom500 = &scom500;
		scom501 = &scom501;
		scom510 = &scom510;
		scom511 = &scom511;
		scom512 = &scom512;
		scom513 = &scom513;
		scom514 = &scom514;
		scom515 = &scom515;
		scom602 = &scom602;
		scom603 = &scom603;
		scom610 = &scom610;
		scom611 = &scom611;
		scom614 = &scom614;
		scom615 = &scom615;
		scom616 = &scom616;
		scom617 = &scom617;
		scom700 = &scom700;
		scom701 = &scom701;
		scom710 = &scom710;
		scom711 = &scom711;
		scom712 = &scom712;
		scom713 = &scom713;
		scom714 = &scom714;
		scom715 = &scom715;
		scom802 = &scom802;
		scom803 = &scom803;
		scom810 = &scom810;
		scom811 = &scom811;
		scom814 = &scom814;
		scom815 = &scom815;
		scom816 = &scom816;
		scom817 = &scom817;

		spi50 = &cfam4_spi0;
		spi51 = &cfam4_spi1;
		spi52 = &cfam4_spi2;
		spi53 = &cfam4_spi3;
		spi60 = &cfam5_spi0;
		spi61 = &cfam5_spi1;
		spi62 = &cfam5_spi2;
		spi63 = &cfam5_spi3;
		spi70 = &cfam6_spi0;
		spi71 = &cfam6_spi1;
		spi72 = &cfam6_spi2;
		spi73 = &cfam6_spi3;
		spi80 = &cfam7_spi0;
		spi81 = &cfam7_spi1;
		spi82 = &cfam7_spi2;
		spi83 = &cfam7_spi3;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		event_log: tcg_event_log@b3d00000 {
			no-map;
			reg = <0xb3d00000 0x100000>;
		};

		ramoops@b3e00000 {
			compatible = "ramoops";
			reg = <0xb3e00000 0x200000>; /* 16 * (4 * 0x8000) */
			record-size = <0x8000>;
			console-size = <0x8000>;
			ftrace-size = <0x8000>;
			pmsg-size = <0x8000>;
			max-reason = <3>; /* KMSG_DUMP_EMERG */
		};

		/* LPC FW cycle bridge region requires natural alignment */
		flash_memory: region@b4000000 {
			no-map;
			reg = <0xb4000000 0x04000000>; /* 64M */
		};

		/* VGA region is dictated by hardware strapping */
		vga_memory: region@bf000000 {
			no-map;
			compatible = "shared-dma-pool";
			reg = <0xbf000000 0x01000000>; /* 16M */
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-fan0-presence {
			label = "fan0-presence";
			gpios = <&pca0 15 GPIO_ACTIVE_LOW>;
			linux,code = <15>;
		};

		event-fan1-presence {
			label = "fan1-presence";
			gpios = <&pca0 14 GPIO_ACTIVE_LOW>;
			linux,code = <14>;
		};

		event-fan2-presence {
			label = "fan2-presence";
			gpios = <&pca0 13 GPIO_ACTIVE_LOW>;
			linux,code = <13>;
		};

		event-fan3-presence {
			label = "fan3-presence";
			gpios = <&pca0 12 GPIO_ACTIVE_LOW>;
			linux,code = <12>;
		};
	};

	leds {
		compatible = "gpio-leds";

		/* RTC battery fault LED at the back */
		led-rtc-battery {
			gpios = <&gpio0 ASPEED_GPIO(H, 0) GPIO_ACTIVE_LOW>;
		};

		/* BMC Card fault LED at the back */
		led-bmc {
			gpios = <&gpio0 ASPEED_GPIO(H, 1) GPIO_ACTIVE_LOW>;
		};

		/* Enclosure Identify LED at the back */
		led-rear-enc-id0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 2) GPIO_ACTIVE_LOW>;
		};

		/* Enclosure fault LED at the back */
		led-rear-enc-fault0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 3) GPIO_ACTIVE_LOW>;
		};

		/* PCIE slot power LED */
		led-pcieslot-power {
			gpios = <&gpio0 ASPEED_GPIO(P, 4) GPIO_ACTIVE_LOW>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc1 7>;
	};
};

&adc1 {
	status = "okay";
	aspeed,int-vref-microvolt = <2500000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc9_default
				 &pinctrl_adc10_default &pinctrl_adc11_default
				 &pinctrl_adc12_default &pinctrl_adc13_default
				 &pinctrl_adc14_default &pinctrl_adc15_default>;
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"USERSPACE_RSTIND_BUFF","","","","","","checkstop","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","rtc-battery-voltage-read-enable","reset-cause-pinhole","","","factory-reset-toggle","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"led-rtc-battery","led-bmc","led-rear-enc-id0","led-rear-enc-fault0","","","","",
	/*I0-I7*/	"","","","","","","bmc-secure-boot","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","usb-power","","","","",
	/*P0-P7*/	"","","","","led-pcieslot-power","","","",
	/*Q0-Q7*/	"","","regulator-standby-faulted","","","","","",
	/*R0-R7*/	"bmc-tpm-reset","power-chassis-control","power-chassis-good","","","I2C_FLASH_MICRO_N","","",
	/*S0-S7*/	"","","","","power-ffs-sync-history","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","BMC_3RESTART_ATTEMPT_P","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/   "","","","","","","","";

	usb_power {
		gpio-hog;
		gpios = <ASPEED_GPIO(O, 3) GPIO_ACTIVE_LOW>;
		output-high;
	};
};

&i2c0 {
	status = "okay";

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	pca1: pca9552@62 {
		compatible = "nxp,pca9552";
		reg = <0x62>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"presence-ps0",
			"presence-ps1",
			"presence-ps2",
			"presence-ps3",
			"presence-pdb",
			"presence-tpm",
			"", "",
			"presence-cp0",
			"presence-cp1",
			"presence-cp2",
			"presence-cp3",
			"presence-dasd",
			"presence-lcd-op",
			"presence-base-op",
			"";
	};

	led-controller@63 {
		compatible = "nxp,pca9552";
		reg = <0x63>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"presence-vrm-c12",
			"presence-vrm-c13",
			"presence-vrm-c15",
			"presence-vrm-c16",
			"presence-vrm-c17",
			"presence-vrm-c18",
			"presence-vrm-c20",
			"presence-vrm-c21",
			"presence-vrm-c54",
			"presence-vrm-c55",
			"presence-vrm-c57",
			"presence-vrm-c58",
			"presence-vrm-c59",
			"presence-vrm-c60",
			"presence-vrm-c62",
			"presence-vrm-c63";
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";

	eeprom@54 {
		compatible = "atmel,24c128";
		reg = <0x54>;
	};

	power-supply@68 {
		compatible = "ibm,cffps";
		reg = <0x68>;
	};

	power-supply@69 {
		compatible = "ibm,cffps";
		reg = <0x69>;
	};

	power-supply@6b {
		compatible = "ibm,cffps";
		reg = <0x6b>;
	};

	power-supply@6d {
		compatible = "ibm,cffps";
		reg = <0x6d>;
	};
};

&i2c4 {
	status = "okay";

	pca2: pca9552@65 {
		compatible = "nxp,pca9552";
		reg = <0x65>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"presence-cable-card1",
			"presence-cable-card2",
			"presence-cable-card3",
			"presence-cable-card4",
			"presence-cable-card5",
			"expander-cable-card1",
			"expander-cable-card2",
			"expander-cable-card3",
			"expander-cable-card4",
			"expander-cable-card5";
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c4mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};

			pca_cable_card_c01: pca9551@62 {
				compatible = "nxp,pca9551";
				reg = <0x62>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c01-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c01-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c4mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			pca_cable_card_c02: pca9551@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c02-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c02-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c4mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};

			pca_cable_card_c03: pca9551@61 {
				compatible = "nxp,pca9551";
				reg = <0x61>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c03-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c03-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};
	};
};

&i2c5 {
	status = "okay";

	pca3: pca9552@66 {
		compatible = "nxp,pca9552";
		reg = <0x66>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"presence-cable-card6",
			"presence-cable-card7",
			"presence-cable-card8",
			"presence-cable-card9",
			"presence-cable-card10",
			"presence-cable-card11",
			"expander-cable-card6",
			"expander-cable-card7",
			"expander-cable-card8",
			"expander-cable-card9",
			"expander-cable-card10",
			"expander-cable-card11";
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c5mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			pca_cable_card_c04: pca9551@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c04-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c04-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c5mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};

			pca_cable_card_c05: pca9551@61 {
				compatible = "nxp,pca9551";
				reg = <0x61>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c05-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c05-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c5mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};

			pca_cable_card_c06: pca9551@62 {
				compatible = "nxp,pca9551";
				reg = <0x62>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c06-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c06-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c5mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};

			pca_cable_card_c07: pca9551@63 {
				compatible = "nxp,pca9551";
				reg = <0x63>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c07-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c07-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};
	};
};

&i2c6 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c6mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			eeprom@50 {
			compatible = "atmel,24c64";
			reg = <0x50>;
			};

			pca_cable_card_c08: pca9551@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c08-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c08-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c6mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};

			pca_cable_card_c09: pca9551@62 {
				compatible = "nxp,pca9551";
				reg = <0x62>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c09-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c09-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c6mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};

			pca_cable_card_c10: pca9551@63 {
				compatible = "nxp,pca9551";
				reg = <0x63>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c10-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c10-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c6mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};

			pca_cable_card_c11: pca9551@61 {
				compatible = "nxp,pca9551";
				reg = <0x61>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard-c11-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard-c11-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};
	};

	pca_pcie_slot: pca9552@65 {
		compatible = "nxp,pca9552";
		reg = <0x65>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@1 {
			label = "pcieslot-c01";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "pcieslot-c02";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "pcieslot-c03";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "pcieslot-c04";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "pcieslot-c05";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "pcieslot-c06";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "pcieslot-c07";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "pcieslot-c08";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "pcieslot-c09";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "pcieslot-c10";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "pcieslot-c11";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&i2c7 {
	status = "okay";

	pic0_dimm: pca9552@31 {
		compatible = "ibm,pca9552";
		reg = <0x31>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "ddimm0";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "ddimm1";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "ddimm2";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "ddimm3";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "ddimm4";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "ddimm5";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "ddimm6";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "ddimm7";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "ddimm8";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "ddimm9";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "ddimm10";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "ddimm11";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "ddimm12";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "ddimm13";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "ddimm14";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "ddimm15";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pic1_dimm: pca9552@32 {
		compatible = "ibm,pca9552";
		reg = <0x32>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "ddimm16";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "ddimm17";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "ddimm18";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "ddimm19";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "ddimm20";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "ddimm21";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "ddimm22";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "ddimm23";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "ddimm24";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "ddimm25";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "ddimm26";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "ddimm27";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "ddimm28";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "ddimm29";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "ddimm30";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "ddimm31";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pic2_dimm: pca9552@33 {
		compatible = "ibm,pca9552";
		reg = <0x33>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "ddimm32";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "ddimm33";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "ddimm34";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "ddimm35";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "ddimm36";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "ddimm37";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "ddimm38";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "ddimm39";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "ddimm40";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "ddimm41";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "ddimm42";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "ddimm43";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "ddimm44";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "ddimm45";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "ddimm46";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "ddimm47";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pic3_dimm: pca9552@30 {
		compatible = "ibm,pca9552";
		reg = <0x30>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "ddimm48";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "ddimm49";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "ddimm50";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "ddimm51";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "ddimm52";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "ddimm53";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "ddimm54";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "ddimm55";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "ddimm56";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "ddimm57";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "ddimm58";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "ddimm59";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "ddimm60";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "ddimm61";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "ddimm62";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "ddimm63";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pic0_vrm_misc: pca9552@34 {
		compatible = "ibm,pca9552";
		reg = <0x34>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "planar";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "tpm";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "cpu3-c61";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "cpu0-c14";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "opencapi-connector3";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "opencapi-connector4";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "opencapi-connector5";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "vrm4";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "vrm5";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "vrm6";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "vrm7";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "vrm12";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "vrm13";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "vrm14";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "vrm15";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pic1_vrm_misc: pca9552@35 {
		compatible = "ibm,pca9552";
		reg = <0x35>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "dasd-backplane";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "power-distribution";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "cpu1-c19";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "cpu2-c56";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "opencapi-connector0";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "opencapi-connector1";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "opencapi-connector2";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "vrm0";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "vrm1";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "vrm2";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "vrm3";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "vrm8";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "vrm9";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "vrm10";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "vrm11";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&i2c8 {
	status = "okay";

	ucd90320@11 {
		compatible = "ti,ucd90320";
		reg = <0x11>;
	};

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c8mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c8mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
	};
};

&i2c9 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};

	eeprom@51 {
		compatible = "atmel,24c128";
		reg = <0x51>;
	};

	eeprom@53 {
		compatible = "atmel,24c128";
		reg = <0x53>;
	};

	eeprom@52 {
		compatible = "atmel,24c128";
		reg = <0x52>;
	};
};

&i2c10 {
	status = "okay";

	eeprom@51 {
		compatible = "atmel,24c128";
		reg = <0x51>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};

	eeprom@53 {
		compatible = "atmel,24c128";
		reg = <0x53>;
	};

	eeprom@52 {
		compatible = "atmel,24c128";
		reg = <0x52>;
	};
};

&i2c11 {
	status = "okay";

	eeprom@51 {
		compatible = "atmel,24c128";
		reg = <0x51>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};

	eeprom@53 {
		compatible = "atmel,24c128";
		reg = <0x53>;
	};

	eeprom@52 {
		compatible = "atmel,24c128";
		reg = <0x52>;
	};
};

&i2c12 {
	status = "okay";

	tpm@2e {
		compatible = "nuvoton,npct75x", "tcg,tpm-tis-i2c";
		reg = <0x2e>;
		memory-region = <&event_log>;
	};
};

&i2c13 {
	status = "okay";

	eeprom@51 {
		compatible = "atmel,24c128";
		reg = <0x51>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};

	eeprom@53 {
		compatible = "atmel,24c128";
		reg = <0x53>;
	};

	eeprom@52 {
		compatible = "atmel,24c128";
		reg = <0x52>;
	};
};

&i2c14 {
	multi-master;
	status = "okay";

	ibm-panel@62 {
		compatible = "ibm,op-panel";
		reg = <(0x62 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		idle-state = <1>;

		i2c14mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};

		i2c14mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			eeprom@51 {
				compatible = "atmel,24c32";
				reg = <0x51>;
			};
		};

		i2c14mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
			eeprom@50 {
				compatible = "atmel,24c32";
				reg = <0x50>;
			};

			pca_oppanel: pca9551@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "front-sys-id0";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "front-check-log0";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@2 {
					label = "front-enc-fault1";
					reg = <2>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@3 {
					label = "front-sys-pwron0";
					reg = <3>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c14mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			max31785@52 {
				compatible = "maxim,max31785a";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x52>;

				fan@0 {
					compatible = "pmbus-fan";
					reg = <0>;
					tach-pulses = <2>;
				};

				fan@1 {
					compatible = "pmbus-fan";
					reg = <1>;
					tach-pulses = <2>;
				};

				fan@2 {
					compatible = "pmbus-fan";
					reg = <2>;
					tach-pulses = <2>;
				};

				fan@3 {
					compatible = "pmbus-fan";
					reg = <3>;
					tach-pulses = <2>;
				};
			};

			pca_fan_nvme: pca9552@60 {
				compatible = "nxp,pca9552";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "nvme0";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "nvme1";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@2 {
					label = "nvme2";
					reg = <2>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@3 {
					label = "nvme3";
					reg = <3>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@4 {
					label = "nvme4";
					reg = <4>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@5 {
					label = "nvme5";
					reg = <5>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@6 {
					label = "nvme6";
					reg = <6>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@7 {
					label = "nvme7";
					reg = <7>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@8 {
					label = "nvme8";
					reg = <8>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@9 {
					label = "nvme9";
					reg = <9>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@10 {
					label = "fan0";
					reg = <10>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@11 {
					label = "fan1";
					reg = <11>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@12 {
					label = "fan2";
					reg = <12>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@13 {
					label = "fan3";
					reg = <13>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};

			pca0: pca9552@61 {
				compatible = "nxp,pca9552";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x61>;

				gpio-controller;
				#gpio-cells = <2>;

				gpio-line-names =
					"","","","",
					"","","","",
					"","","","",
					"presence-fan3",
					"presence-fan2",
					"presence-fan1",
					"presence-fan0";
			};
		};
	};

	i2c-mux@71 {
		compatible = "nxp,pca9546";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c14mux1chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			eeprom@50 {
				compatible = "atmel,24c32";
				reg = <0x50>;
			};
		};

		i2c14mux1chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			eeprom@50 {
				compatible = "atmel,24c32";
				reg = <0x50>;
			};
		};

		i2c14mux1chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
			eeprom@50 {
				compatible = "atmel,24c32";
				reg = <0x50>;
			};
		};

		i2c14mux1chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
			eeprom@50 {
				compatible = "atmel,24c32";
				reg = <0x50>;
			};
		};
	};
};

&i2c15 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c15mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};
	};

	i2c-mux@71 {
		compatible = "nxp,pca9546";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c15mux1chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux1chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux1chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux1chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};
	};

	i2c-mux@72 {
		compatible = "nxp,pca9546";
		reg = <0x72>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c15mux2chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux2chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c15mux2chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c15mux2chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&emmc_controller {
	status = "okay";
};

&pinctrl_emmc_default {
	bias-disable;
};

&emmc {
	status = "okay";
	clk-phase-mmc-hs200 = <210>, <228>;
};

&ibt {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&vuart1 {
	status = "okay";
};

&vuart2 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC3CLK>,
		 <&syscon ASPEED_CLK_MAC3RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC4CLK>,
		 <&syscon ASPEED_CLK_MAC4RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	status = "okay";
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8 0xcac>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
	aspeed,lpc-interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
};

#include "ibm-power10-quad.dtsi"

&fsi_hub0 {
	cfam@4,0 { /* DCM2_C0 */
		reg = <4 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <4>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam4_i2c0: i2c-bus@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;	/* OM01 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom500: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo500: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam4_i2c1: i2c-bus@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;	/* OM23 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom501: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo501: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam4_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom510: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo510: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam4_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom511: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo511: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam4_i2c12: i2c-bus@c {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <12>;	/* OP4A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom512: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo512: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam4_i2c13: i2c-bus@d {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <13>;	/* OP4B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom513: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo513: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam4_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom514: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo514: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam4_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom515: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo515: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam4_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam4_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam4_spi2: spi@40 {
				reg = <0x40>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam4_spi3: spi@60 {
				reg = <0x60>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ4: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub4: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};

	cfam@5,0 { /* DCM2_C1 */
		reg = <5 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <5>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam5_i2c2: i2c-bus@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <2>;	/* OM45 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom602: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo602: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam5_i2c3: i2c-bus@3 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <3>;	/* OM67 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom603: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo603: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam5_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom610: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo610: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam5_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom611: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo611: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam5_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom614: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo614: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam5_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom615: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo615: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam5_i2c16: i2c-bus@10 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <16>;	/* OP6A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom616: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo616: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam5_i2c17: i2c-bus@11 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <17>;	/* OP6B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom617: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo617: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam5_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam5_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam5_spi2: spi@40 {
				reg = <0x40>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam5_spi3: spi@60 {
				reg = <0x60>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ5: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub5: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};

	cfam@6,0 { /* DCM3_C0 */
		reg = <6 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <6>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam6_i2c0: i2c-bus@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;	/* OM01 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom700: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo700: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam6_i2c1: i2c-bus@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;	/* OM23 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom701: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo701: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam6_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom710: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo710: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam6_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom711: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo711: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam6_i2c12: i2c-bus@c {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <12>;	/* OP4A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom712: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo712: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam6_i2c13: i2c-bus@d {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <13>;	/* OP4B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom713: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo713: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam6_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom714: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo714: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam6_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom715: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo715: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam6_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam6_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam6_spi2: spi@40 {
				reg = <0x40>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam6_spi3: spi@60 {
				reg = <0x60>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ6: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub6: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};

	cfam@7,0 { /* DCM3_C1 */
		reg = <7 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <7>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam7_i2c2: i2c-bus@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <2>;	/* OM45 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom802: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo802: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam7_i2c3: i2c-bus@3 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <3>;	/* OM67 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom803: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo803: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam7_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom810: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo810: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam7_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom811: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo811: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam7_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom814: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo814: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam7_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom815: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo815: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam7_i2c16: i2c-bus@10 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <16>;	/* OP6A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom816: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo816: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam7_i2c17: i2c-bus@11 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <17>;	/* OP6B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom817: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo817: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam7_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam7_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam7_spi2: spi@40 {
				reg = <0x40>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam7_spi3: spi@60 {
				reg = <0x60>;
				compatible = "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ7: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub7: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};
};

/* Legacy OCC numbering (to get rid of when userspace is fixed) */
&fsi_occ4 {
	reg = <5>;
};

&fsi_occ5 {
	reg = <6>;
};

&fsi_occ6 {
	reg = <7>;
};

&fsi_occ7 {
	reg = <8>;
};
