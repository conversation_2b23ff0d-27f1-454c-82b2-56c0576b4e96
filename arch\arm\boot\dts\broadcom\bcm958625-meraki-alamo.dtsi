// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Device Tree Bindings for Cisco Meraki MX65 series (Alamo).
 *
 * Copyright (C) 2020-2021 <PERSON> <<EMAIL>>
 */

#include "bcm958625-meraki-mx6x-common.dtsi"

/ {
	keys {
		compatible = "gpio-keys-polled";
		autorepeat;
		poll-interval = <20>;

		button-reset {
			label = "reset";
			linux,code = <KEY_RESTART>;
			gpios = <&gpioa 8 GPIO_ACTIVE_LOW>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			/* green:wan1-left */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <0>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 25 GPIO_ACTIVE_LOW>;
		};

		led-1 {
			/* green:wan1-right */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <1>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 24 GPIO_ACTIVE_LOW>;
		};

		led-2 {
			/* green:wan2-left */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <2>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 27 GPIO_ACTIVE_LOW>;
		};

		led-3 {
			/* green:wan2-right */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <3>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 26 GPIO_ACTIVE_LOW>;
		};

		led-4 {
			/* amber:power */
			function = LED_FUNCTION_FAULT;
			color = <LED_COLOR_ID_AMBER>;
			gpios = <&gpioa 3 GPIO_ACTIVE_HIGH>;
		};

		led-5 {
			/* white:status */
			function = LED_FUNCTION_STATUS;
			color = <LED_COLOR_ID_WHITE>;
			gpios = <&gpioa 31 GPIO_ACTIVE_HIGH>;
		};
	};
};

&axi {
	mdio-mux@3f1c0 {
		compatible = "mdio-mux-mmioreg", "mdio-mux";
		reg = <0x3f1c0 0x4>;
		mux-mask = <0x2000>;
		mdio-parent-bus = <&mdio_ext>;
		#address-cells = <1>;
		#size-cells = <0>;

		mdio@0 {
			reg = <0x0>;
			#address-cells = <1>;
			#size-cells = <0>;

			phy_port6: phy@0 {
				reg = <0>;
			};

			phy_port7: phy@1 {
				reg = <1>;
			};

			phy_port8: phy@2 {
				reg = <2>;
			};

			phy_port9: phy@3 {
				reg = <3>;
			};

			phy_port10: phy@4 {
				reg = <4>;
			};

			switch@10 {
				compatible = "qca,qca8337";
				reg = <0x10>;
				dsa,member = <1 0>;

				ports {
					#address-cells = <1>;
					#size-cells = <0>;
					port@0 {
						reg = <0>;
						ethernet = <&sgmii1>;
						phy-mode = "sgmii";
						qca,sgmii-enable-pll;
						qca,sgmii-txclk-falling-edge;
						fixed-link {
							speed = <1000>;
							full-duplex;
						};
					};

					port@1 {
						reg = <1>;
						label = "lan8";
						phy-handle = <&phy_port6>;
					};

					port@2 {
						reg = <2>;
						label = "lan9";
						phy-handle = <&phy_port7>;
					};

					port@3 {
						reg = <3>;
						label = "lan10";
						phy-handle = <&phy_port8>;
					};

					port@4 {
						reg = <4>;
						label = "lan11";
						phy-handle = <&phy_port9>;
					};

					port@5 {
						reg = <5>;
						label = "lan12";
						phy-handle = <&phy_port10>;
					};
				};
			};
		};

		mdio-mii@2000 {
			reg = <0x2000>;
			#address-cells = <1>;
			#size-cells = <0>;

			phy_port1: phy@0 {
				reg = <0>;
			};

			phy_port2: phy@1 {
				reg = <1>;
			};

			phy_port3: phy@2 {
				reg = <2>;
			};

			phy_port4: phy@3 {
				reg = <3>;
			};

			phy_port5: phy@4 {
				reg = <4>;
			};

			switch@10 {
				compatible = "qca,qca8337";
				reg = <0x10>;
				dsa,member = <2 0>;

				ports {
					#address-cells = <1>;
					#size-cells = <0>;
					port@0 {
						reg = <0>;
						ethernet = <&sgmii0>;
						phy-mode = "sgmii";
						qca,sgmii-enable-pll;
						qca,sgmii-txclk-falling-edge;
						fixed-link {
							speed = <1000>;
							full-duplex;
						};
					};

					port@1 {
						reg = <1>;
						label = "lan3";
						phy-handle = <&phy_port1>;
					};

					port@2 {
						reg = <2>;
						label = "lan4";
						phy-handle = <&phy_port2>;
					};

					port@3 {
						reg = <3>;
						label = "lan5";
						phy-handle = <&phy_port3>;
					};

					port@4 {
						reg = <4>;
						label = "lan6";
						phy-handle = <&phy_port4>;
					};

					port@5 {
						reg = <5>;
						label = "lan7";
						phy-handle = <&phy_port5>;
					};
				};
			};
		};
	};
};

&srab {
	compatible = "brcm,bcm58625-srab", "brcm,nsp-srab";
	status = "okay";
	dsa,member = <0 0>;

	ports {
		port@0 {
			label = "wan1";
			reg = <0>;
		};

		port@1 {
			label = "wan2";
			reg = <1>;
		};

		sgmii0: port@4 {
			label = "sw0";
			reg = <4>;
			fixed-link {
				speed = <1000>;
				full-duplex;
			};
		};

		sgmii1: port@5 {
			label = "sw1";
			reg = <5>;
			fixed-link {
				speed = <1000>;
				full-duplex;
			};
		};

		port@8 {
			ethernet = <&amac2>;
			reg = <8>;
			fixed-link {
				speed = <1000>;
				full-duplex;
			};
		};
	};
};
