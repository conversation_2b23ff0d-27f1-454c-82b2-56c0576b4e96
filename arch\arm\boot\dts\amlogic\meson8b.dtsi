// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright 2015 Endless Mobile, Inc.
 * Author: <PERSON> <<EMAIL>>
 */

#include <dt-bindings/clock/meson8-ddr-clkc.h>
#include <dt-bindings/clock/meson8b-clkc.h>
#include <dt-bindings/gpio/meson8b-gpio.h>
#include <dt-bindings/power/meson8-power.h>
#include <dt-bindings/reset/amlogic,meson8b-reset.h>
#include <dt-bindings/reset/amlogic,meson8b-clkc-reset.h>
#include <dt-bindings/thermal/thermal.h>
#include "meson.dtsi"

/ {
	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@200 {
			device_type = "cpu";
			compatible = "arm,cortex-a5";
			next-level-cache = <&L2>;
			reg = <0x200>;
			enable-method = "amlogic,meson8b-smp";
			resets = <&clkc CLKC_RESET_CPU0_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu1: cpu@201 {
			device_type = "cpu";
			compatible = "arm,cortex-a5";
			next-level-cache = <&L2>;
			reg = <0x201>;
			enable-method = "amlogic,meson8b-smp";
			resets = <&clkc CLKC_RESET_CPU1_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu2: cpu@202 {
			device_type = "cpu";
			compatible = "arm,cortex-a5";
			next-level-cache = <&L2>;
			reg = <0x202>;
			enable-method = "amlogic,meson8b-smp";
			resets = <&clkc CLKC_RESET_CPU2_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu3: cpu@203 {
			device_type = "cpu";
			compatible = "arm,cortex-a5";
			next-level-cache = <&L2>;
			reg = <0x203>;
			enable-method = "amlogic,meson8b-smp";
			resets = <&clkc CLKC_RESET_CPU3_SOFT_RESET>;
			operating-points-v2 = <&cpu_opp_table>;
			clocks = <&clkc CLKID_CPUCLK>;
			#cooling-cells = <2>; /* min followed by max */
		};
	};

	cpu_opp_table: opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		opp-96000000 {
			opp-hz = /bits/ 64 <96000000>;
			opp-microvolt = <860000>;
		};
		opp-192000000 {
			opp-hz = /bits/ 64 <192000000>;
			opp-microvolt = <860000>;
		};
		opp-312000000 {
			opp-hz = /bits/ 64 <312000000>;
			opp-microvolt = <860000>;
		};
		opp-408000000 {
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <860000>;
		};
		opp-504000000 {
			opp-hz = /bits/ 64 <504000000>;
			opp-microvolt = <860000>;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <860000>;
		};
		opp-720000000 {
			opp-hz = /bits/ 64 <720000000>;
			opp-microvolt = <860000>;
		};
		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <900000>;
		};
		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <1140000>;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <1140000>;
		};
		opp-1320000000 {
			opp-hz = /bits/ 64 <1320000000>;
			opp-microvolt = <1140000>;
		};
		opp-1488000000 {
			opp-hz = /bits/ 64 <1488000000>;
			opp-microvolt = <1140000>;
		};
		opp-1536000000 {
			opp-hz = /bits/ 64 <1536000000>;
			opp-microvolt = <1140000>;
		};
	};

	gpu_opp_table: opp-table-gpu {
		compatible = "operating-points-v2";

		opp-255000000 {
			opp-hz = /bits/ 64 <255000000>;
			opp-microvolt = <1100000>;
		};
		opp-364285714 {
			opp-hz = /bits/ 64 <364285714>;
			opp-microvolt = <1100000>;
		};
		opp-425000000 {
			opp-hz = /bits/ 64 <425000000>;
			opp-microvolt = <1100000>;
		};
		opp-510000000 {
			opp-hz = /bits/ 64 <510000000>;
			opp-microvolt = <1100000>;
		};
		opp-637500000 {
			opp-hz = /bits/ 64 <637500000>;
			opp-microvolt = <1100000>;
			turbo-mode;
		};
	};

	pmu {
		compatible = "arm,cortex-a5-pmu";
		interrupts = <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		/* 2 MiB reserved for Hardware ROM Firmware? */
		hwrom@0 {
			reg = <0x0 0x200000>;
			no-map;
		};
	};

	thermal-zones {
		soc-thermal {
			polling-delay-passive = <250>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&thermal_sensor>;

			cooling-maps {
				map0 {
					trip = <&soc_passive>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&mali THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};

				map1 {
					trip = <&soc_hot>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&mali THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};

			trips {
				soc_passive: soc-passive {
					temperature = <80000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "passive";
				};

				soc_hot: soc-hot {
					temperature = <90000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "hot";
				};

				soc_critical: soc-critical {
					temperature = <110000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "critical";
				};
			};
		};
	};

	mmcbus: bus@c8000000 {
		compatible = "simple-bus";
		reg = <0xc8000000 0x8000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0xc8000000 0x8000>;

		ddr_clkc: clock-controller@400 {
			compatible = "amlogic,meson8b-ddr-clkc";
			reg = <0x400 0x20>;
			clocks = <&xtal>;
			clock-names = "xtal";
			#clock-cells = <1>;
		};

		dmcbus: bus@6000 {
			compatible = "simple-bus";
			reg = <0x6000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x6000 0x400>;

			canvas: video-lut@48 {
				compatible = "amlogic,meson8b-canvas",
					     "amlogic,canvas";
				reg = <0x48 0x14>;
			};
		};
	};

	apb: bus@d0000000 {
		compatible = "simple-bus";
		reg = <0xd0000000 0x200000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0xd0000000 0x200000>;

		mali: gpu@c0000 {
			compatible = "amlogic,meson8b-mali", "arm,mali-450";
			reg = <0xc0000 0x40000>;
			interrupts = <GIC_SPI 160 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 161 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 162 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 164 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 165 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 166 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 167 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gp", "gpmmu", "pp", "pmu",
					  "pp0", "ppmmu0", "pp1", "ppmmu1";
			resets = <&reset RESET_MALI>;
			clocks = <&clkc CLKID_CLK81>, <&clkc CLKID_MALI>;
			clock-names = "bus", "core";
			operating-points-v2 = <&gpu_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};
	};
}; /* end of / */

&aiu {
	compatible = "amlogic,aiu-meson8b", "amlogic,aiu";
	clocks = <&clkc CLKID_AIU_GLUE>,
		 <&clkc CLKID_I2S_OUT>,
		 <&clkc CLKID_AOCLK_GATE>,
		 <&clkc CLKID_CTS_AMCLK>,
		 <&clkc CLKID_MIXER_IFACE>,
		 <&clkc CLKID_IEC958>,
		 <&clkc CLKID_IEC958_GATE>,
		 <&clkc CLKID_CTS_MCLK_I958>,
		 <&clkc CLKID_CTS_I958>;
	clock-names = "pclk",
		      "i2s_pclk",
		      "i2s_aoclk",
		      "i2s_mclk",
		      "i2s_mixer",
		      "spdif_pclk",
		      "spdif_aoclk",
		      "spdif_mclk",
		      "spdif_mclk_sel";
	resets = <&reset RESET_AIU>;
};

&aobus {
	pmu: pmu@e0 {
		compatible = "amlogic,meson8b-pmu", "syscon";
		reg = <0xe0 0x18>;
	};

	pinctrl_aobus: pinctrl@14 {
		compatible = "amlogic,meson8b-aobus-pinctrl";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x14 0x1c>;

		gpio_ao: bank@0 {
			reg = <0x0 0x4>,
			      <0x18 0x4>,
			      <0x10 0x8>;
			reg-names = "mux", "pull", "gpio";
			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl_aobus 0 0 16>;
		};

		i2s_am_clk_pins: i2s-am-clk-out {
			mux {
				groups = "i2s_am_clk_out";
				function = "i2s";
				bias-disable;
			};
		};

		i2s_out_ao_clk_pins: i2s-ao-clk-out {
			mux {
				groups = "i2s_ao_clk_out";
				function = "i2s";
				bias-disable;
			};
		};

		i2s_out_lr_clk_pins: i2s-lr-clk-out {
			mux {
				groups = "i2s_lr_clk_out";
				function = "i2s";
				bias-disable;
			};
		};

		i2s_out_ch01_ao_pins: i2s-out-ch01 {
			mux {
				groups = "i2s_out_01";
				function = "i2s";
				bias-disable;
			};
		};

		spdif_out_1_pins: spdif-out-1 {
			mux {
				groups = "spdif_out_1";
				function = "spdif_1";
				bias-disable;
			};
		};

		uart_ao_a_pins: uart_ao_a {
			mux {
				groups = "uart_tx_ao_a", "uart_rx_ao_a";
				function = "uart_ao";
				bias-pull-up;
			};
		};

		ir_recv_pins: remote {
			mux {
				groups = "remote_input";
				function = "remote";
				bias-disable;
			};
		};
	};
};

&ao_arc_rproc {
	compatible = "amlogic,meson8b-ao-arc", "amlogic,meson-mx-ao-arc";
	amlogic,secbus2 = <&secbus2>;
	sram = <&ao_arc_sram>;
	resets = <&reset RESET_MEDIA_CPU>;
	clocks = <&clkc CLKID_AO_MEDIA_CPU>;
};

&cbus {
	reset: reset-controller@4404 {
		compatible = "amlogic,meson8b-reset";
		reg = <0x4404 0x9c>;
		#reset-cells = <1>;
	};

	analog_top: analog-top@81a8 {
		compatible = "amlogic,meson8b-analog-top", "syscon";
		reg = <0x81a8 0x14>;
	};

	pwm_ef: pwm@86c0 {
		compatible = "amlogic,meson8b-pwm-v2", "amlogic,meson8-pwm-v2";
		reg = <0x86c0 0x10>;
		clocks = <&xtal>,
			 <0>, /* unknown/untested, the datasheet calls it "Video PLL" */
			 <&clkc CLKID_FCLK_DIV4>,
			 <&clkc CLKID_FCLK_DIV3>;
		#pwm-cells = <3>;
		status = "disabled";
	};

	clock-measure@8758 {
		compatible = "amlogic,meson8b-clk-measure";
		reg = <0x8758 0x1c>;
	};

	pinctrl_cbus: pinctrl@8030 {
		compatible = "amlogic,meson8b-cbus-pinctrl";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x8030 0x108>;

		gpio: bank@80 {
			reg = <0x80 0x28>,
			      <0xb8 0x18>,
			      <0xf0 0x18>,
			      <0x00 0x38>;
			reg-names = "mux", "pull", "pull-enable", "gpio";
			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl_cbus 0 0 83>;
		};

		eth_rgmii_pins: eth-rgmii {
			mux {
				groups = "eth_tx_clk",
					 "eth_tx_en",
					 "eth_txd1_0",
					 "eth_txd0_0",
					 "eth_rx_clk",
					 "eth_rx_dv",
					 "eth_rxd1",
					 "eth_rxd0",
					 "eth_mdio_en",
					 "eth_mdc",
					 "eth_ref_clk",
					 "eth_txd2",
					 "eth_txd3",
					 "eth_rxd3",
					 "eth_rxd2";
				function = "ethernet";
				bias-disable;
			};
		};

		eth_rmii_pins: eth-rmii {
			mux {
				groups = "eth_tx_en",
					 "eth_txd1_0",
					 "eth_txd0_0",
					 "eth_rx_clk",
					 "eth_rx_dv",
					 "eth_rxd1",
					 "eth_rxd0",
					 "eth_mdio_en",
					 "eth_mdc";
				function = "ethernet";
				bias-disable;
			};
		};

		i2c_a_pins: i2c-a {
			mux {
				groups = "i2c_sda_a", "i2c_sck_a";
				function = "i2c_a";
				bias-disable;
			};
		};

		sd_b_pins: sd-b {
			mux {
				groups = "sd_d0_b", "sd_d1_b", "sd_d2_b",
					"sd_d3_b", "sd_clk_b", "sd_cmd_b";
				function = "sd_b";
				bias-disable;
			};
		};

		sdxc_c_pins: sdxc-c {
			mux {
				groups = "sdxc_d0_c", "sdxc_d13_c",
					 "sdxc_d47_c", "sdxc_clk_c",
					 "sdxc_cmd_c";
				function = "sdxc_c";
				bias-pull-up;
			};
		};

		pwm_c1_pins: pwm-c1 {
			mux {
				groups = "pwm_c1";
				function = "pwm_c";
				bias-disable;
			};
		};

		pwm_d_pins: pwm-d {
			mux {
				groups = "pwm_d";
				function = "pwm_d";
				bias-disable;
			};
		};

		uart_b0_pins: uart-b0 {
			mux {
				groups = "uart_tx_b0",
				       "uart_rx_b0";
				function = "uart_b";
				bias-pull-up;
			};
		};

		uart_b0_cts_rts_pins: uart-b0-cts-rts {
			mux {
				groups = "uart_cts_b0",
				       "uart_rts_b0";
				function = "uart_b";
				bias-disable;
			};
		};
	};
};

&ahb_sram {
	ao_arc_sram: aoarc-sram@0 {
		compatible = "amlogic,meson8b-ao-arc-sram";
		reg = <0x0 0x8000>;
		pool;
	};

	smp-sram@1ff80 {
		compatible = "amlogic,meson8b-smp-sram";
		reg = <0x1ff80 0x8>;
	};
};


&efuse {
	compatible = "amlogic,meson8b-efuse";
	clocks = <&clkc CLKID_EFUSE>;
	clock-names = "core";

	temperature_calib: calib@1f4 {
		/* only the upper two bytes are relevant */
		reg = <0x1f4 0x4>;
	};
};

&ethmac {
	compatible = "amlogic,meson8b-dwmac", "snps,dwmac-3.70a", "snps,dwmac";

	reg = <0xc9410000 0x10000
	       0xc1108140 0x4>;

	clocks = <&clkc CLKID_ETH>,
		 <&clkc CLKID_MPLL2>,
		 <&clkc CLKID_MPLL2>,
		 <&clkc CLKID_FCLK_DIV2>;
	clock-names = "stmmaceth", "clkin0", "clkin1", "timing-adjustment";
	rx-fifo-depth = <4096>;
	tx-fifo-depth = <2048>;

	resets = <&reset RESET_ETHERNET>;
	reset-names = "stmmaceth";

	power-domains = <&pwrc PWRC_MESON8_ETHERNET_MEM_ID>;
};

&gpio_intc {
	compatible = "amlogic,meson8b-gpio-intc",
		     "amlogic,meson-gpio-intc";
	status = "okay";
};

&hhi {
	clkc: clock-controller {
		compatible = "amlogic,meson8b-clkc";
		clocks = <&xtal>, <&ddr_clkc DDR_CLKID_DDR_PLL>;
		clock-names = "xtal", "ddr_pll";
		#clock-cells = <1>;
		#reset-cells = <1>;
	};

	pwrc: power-controller {
		compatible = "amlogic,meson8b-pwrc";
		#power-domain-cells = <1>;
		amlogic,ao-sysctrl = <&pmu>;
		resets = <&reset RESET_DBLK>,
			 <&reset RESET_PIC_DC>,
			 <&reset RESET_HDMI_APB>,
			 <&reset RESET_HDMI_SYSTEM_RESET>,
			 <&reset RESET_VENCI>,
			 <&reset RESET_VENCP>,
			 <&reset RESET_VDAC_4>,
			 <&reset RESET_VENCL>,
			 <&reset RESET_VIU>,
			 <&reset RESET_VENC>,
			 <&reset RESET_RDMA>;
		reset-names = "dblk", "pic_dc", "hdmi_apb", "hdmi_system",
			      "venci", "vencp", "vdac", "vencl", "viu",
			      "venc", "rdma";
		clocks = <&clkc CLKID_VPU>;
		clock-names = "vpu";
		assigned-clocks = <&clkc CLKID_VPU>;
		assigned-clock-rates = <182142857>;
	};
};

&hwrng {
	clocks = <&clkc CLKID_RNG0>;
	clock-names = "core";
};

&i2c_AO {
	clocks = <&clkc CLKID_CLK81>;
};

&i2c_A {
	clocks = <&clkc CLKID_I2C>;
};

&i2c_B {
	clocks = <&clkc CLKID_I2C>;
};

&L2 {
	arm,data-latency = <3 3 3>;
	arm,tag-latency = <2 2 2>;
	arm,filter-ranges = <0x100000 0xc0000000>;
	prefetch-data = <1>;
	prefetch-instr = <1>;
	arm,prefetch-offset = <7>;
	arm,double-linefill = <1>;
	arm,prefetch-drop = <1>;
	arm,shared-override;
};

&periph {
	scu@0 {
		compatible = "arm,cortex-a5-scu";
		reg = <0x0 0x100>;
	};

	timer@200 {
		compatible = "arm,cortex-a5-global-timer";
		reg = <0x200 0x20>;
		interrupts = <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
		clocks = <&clkc CLKID_PERIPH>;

		/*
		 * the arm_global_timer driver currently does not handle clock
		 * rate changes. Keep it disabled for now.
		 */
		status = "disabled";
	};

	timer@600 {
		compatible = "arm,cortex-a5-twd-timer";
		reg = <0x600 0x20>;
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
		clocks = <&clkc CLKID_PERIPH>;
	};
};

&pwm_ab {
	compatible = "amlogic,meson8b-pwm-v2", "amlogic,meson8-pwm-v2";
	clocks = <&xtal>,
		 <0>, /* unknown/untested, the datasheet calls it "Video PLL" */
		 <&clkc CLKID_FCLK_DIV4>,
		 <&clkc CLKID_FCLK_DIV3>;
};

&pwm_cd {
	compatible = "amlogic,meson8b-pwm-v2", "amlogic,meson8-pwm-v2";
	clocks = <&xtal>,
		 <0>, /* unknown/untested, the datasheet calls it "Video PLL" */
		 <&clkc CLKID_FCLK_DIV4>,
		 <&clkc CLKID_FCLK_DIV3>;
};

&rtc {
	compatible = "amlogic,meson8b-rtc";
	resets = <&reset RESET_RTC>;
};

&saradc {
	compatible = "amlogic,meson8b-saradc", "amlogic,meson-saradc";
	clocks = <&xtal>, <&clkc CLKID_SAR_ADC>;
	clock-names = "clkin", "core";
	amlogic,hhi-sysctrl = <&hhi>;
	nvmem-cells = <&temperature_calib>;
	nvmem-cell-names = "temperature_calib";
};

&sdhc {
	compatible = "amlogic,meson8-sdhc", "amlogic,meson-mx-sdhc";
	clocks = <&xtal>,
		 <&clkc CLKID_FCLK_DIV4>,
		 <&clkc CLKID_FCLK_DIV3>,
		 <&clkc CLKID_FCLK_DIV5>,
		 <&clkc CLKID_SDHC>;
	clock-names = "clkin0", "clkin1", "clkin2", "clkin3", "pclk";
};

&secbus {
	secbus2: system-controller@4000 {
		compatible = "amlogic,meson8b-secbus2", "syscon";
		reg = <0x4000 0x2000>;
	};
};

&sdio {
	compatible = "amlogic,meson8b-sdio", "amlogic,meson-mx-sdio";
	clocks = <&clkc CLKID_SDIO>, <&clkc CLKID_CLK81>;
	clock-names = "core", "clkin";
};

&timer_abcde {
	clocks = <&xtal>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk";
};

&uart_AO {
	compatible = "amlogic,meson8b-uart", "amlogic,meson-ao-uart";
	clocks = <&xtal>, <&clkc CLKID_CLK81>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&uart_A {
	compatible = "amlogic,meson8b-uart";
	clocks = <&xtal>, <&clkc CLKID_UART0>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&uart_B {
	compatible = "amlogic,meson8b-uart";
	clocks = <&xtal>, <&clkc CLKID_UART1>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&uart_C {
	compatible = "amlogic,meson8b-uart";
	clocks = <&xtal>, <&clkc CLKID_UART2>, <&clkc CLKID_CLK81>;
	clock-names = "xtal", "pclk", "baud";
};

&usb0 {
	compatible = "amlogic,meson8b-usb", "snps,dwc2";
	clocks = <&clkc CLKID_USB0_DDR_BRIDGE>;
	clock-names = "otg";
};

&usb1 {
	compatible = "amlogic,meson8b-usb", "snps,dwc2";
	clocks = <&clkc CLKID_USB1_DDR_BRIDGE>;
	clock-names = "otg";
};

&usb0_phy {
	compatible = "amlogic,meson8b-usb2-phy", "amlogic,meson-mx-usb2-phy";
	clocks = <&clkc CLKID_USB>, <&clkc CLKID_USB0>;
	clock-names = "usb_general", "usb";
	resets = <&reset RESET_USB_OTG>;
};

&usb1_phy {
	compatible = "amlogic,meson8b-usb2-phy", "amlogic,meson-mx-usb2-phy";
	clocks = <&clkc CLKID_USB>, <&clkc CLKID_USB1>;
	clock-names = "usb_general", "usb";
	resets = <&reset RESET_USB_OTG>;
};

&wdt {
	compatible = "amlogic,meson8b-wdt";
};
