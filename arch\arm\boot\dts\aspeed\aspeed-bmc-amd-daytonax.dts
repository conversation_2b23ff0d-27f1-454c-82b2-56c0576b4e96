// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "AMD DaytonaX BMC";
	compatible = "amd,daytonax-bmc", "aspeed,ast2500";

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	aliases {
		serial0 = &uart1;
		serial4 = &uart5;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200";
	};

	leds {
		compatible = "gpio-leds";

		led-fault {
			gpios = <&gpio ASPEED_GPIO(A, 2) GPIO_ACTIVE_LOW>;
		};

		led-identify {
			gpios = <&gpio ASPEED_GPIO(A, 3) GPIO_ACTIVE_LOW>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>, <&adc 4>,
			<&adc 5>, <&adc 6>, <&adc 7>, <&adc 8>, <&adc 9>,
			<&adc 10>, <&adc 11>, <&adc 12>, <&adc 13>, <&adc 14>,
			<&adc 15>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		#include "openbmc-flash-layout.dtsi"
	};
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;
};

&uart1 {
	//Host Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		&pinctrl_rxd1_default
		&pinctrl_nrts1_default
		&pinctrl_ndtr1_default
		&pinctrl_ndsr1_default
		&pinctrl_ncts1_default
		&pinctrl_ndcd1_default
		&pinctrl_nri1_default>;
};

&uart5 {
	//BMC Console
	status = "okay";
};

&vuart {
	status = "okay";
	aspeed,lpc-io-reg = <0x3f8>;
	aspeed,lpc-interrupts = <4 IRQ_TYPE_LEVEL_HIGH>;
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
		&pinctrl_adc1_default
		&pinctrl_adc2_default
		&pinctrl_adc3_default
		&pinctrl_adc4_default
		&pinctrl_adc5_default
		&pinctrl_adc6_default
		&pinctrl_adc7_default
		&pinctrl_adc8_default
		&pinctrl_adc9_default
		&pinctrl_adc10_default
		&pinctrl_adc11_default
		&pinctrl_adc12_default
		&pinctrl_adc13_default
		&pinctrl_adc14_default
		&pinctrl_adc15_default>;
};

&gpio {
	status = "okay";
	gpio-line-names =
	/*A0-A7*/	"","","led-fault","led-identify","","","","",
	/*B0-B7*/	"","","","","","","","",
	/*C0-C7*/	"id-button","","","","","","","",
	/*D0-D7*/	"","","ASSERT_BMC_READY","","","","","",
	/*E0-E7*/	"reset-button","reset-control","power-button","power-control","",
			"power-good","power-ok","",
	/*F0-F7*/	"","","","","","","BATTERY_DETECT","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","","","","","",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"","","","","","","","",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","","","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","",
	/*AA0-AA7*/	"","","","","","","","",
	/*AB0-AB7*/	"FM_BMC_READ_SPD_TEMP","","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>, <0x81>;
};

&lpc_ctrl {
	status = "okay";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default
		&pinctrl_pwm1_default
		&pinctrl_pwm2_default
		&pinctrl_pwm3_default
		&pinctrl_pwm4_default
		&pinctrl_pwm5_default
		&pinctrl_pwm6_default
		&pinctrl_pwm7_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};

	fan@4 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};

	fan@5 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05>;
	};

	fan@6 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06>;
	};

	fan@7 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x07>;
	};

	fan@8 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x08>;
	};

	fan@9 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x09>;
	};

	fan@10 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0a>;
	};

	fan@11 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0b>;
	};

	fan@12 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0c>;
	};

	fan@13 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0d>;
	};

	fan@14 {
		reg = <0x07>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0e>;
	};

	fan@15 {
		reg = <0x07>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0f>;
	};
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&vhub {
	status = "okay";
};
