// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2020 Facebook Inc.

/dts-v1/;

#include "ast2600-facebook-netbmc-common.dtsi"

/ {
	model = "Facebook Elbert BMC";
	compatible = "facebook,elbert-bmc", "aspeed,ast2600";

	aliases {
		serial0 = &uart5;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;

		/*
		 * 8 child channels of PCA9548 2-0075.
		 */
		i2c16 = &imux16;
		i2c17 = &imux17;
		i2c18 = &imux18;
		i2c19 = &imux19;
		i2c20 = &imux20;
		i2c21 = &imux21;
		i2c22 = &imux22;
		i2c23 = &imux23;

		/*
		 * 8 child channels of PCA9548 5-0075.
		 */
		i2c24 = &imux24;
		i2c25 = &imux25;
		i2c26 = &imux26;
		i2c27 = &imux27;
		i2c28 = &imux28;
		i2c29 = &imux29;
		i2c30 = &imux30;
		i2c31 = &imux31;
	};

	chosen {
		stdout-path = &uart5;
	};

	spi_gpio: spi {
		num-chipselects = <1>;
		cs-gpios = <&gpio0 ASPEED_GPIO(X, 0) GPIO_ACTIVE_LOW>;
	};
};

&lpc_ctrl {
	status = "okay";
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&i2c2 {
	i2c-mux@75 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x75>;
		i2c-mux-idle-disconnect;

		imux16: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		imux17: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		imux18: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		imux19: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		imux20: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux21: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux22: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux23: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c5 {
	i2c-mux@75 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x75>;
		i2c-mux-idle-disconnect;

		imux24: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		imux25: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		imux26: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		imux27: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		imux28: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux29: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux30: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux31: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c11 {
	status = "okay";
};

/*
 * BMC's "mac3" controller is connected to BCM53134P's IMP_RGMII port
 * directly (fixed link, no PHY in between).
 * Note: BMC's "mdio0" controller is connected to BCM53134P's MDIO
 * interface, and the MDIO channel will be enabled in dts later, when
 * BCM53134 is added to "bcm53xx" DSA driver.
 */
&mac3 {
	status = "okay";
	phy-mode = "rgmii";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii4_default>;
	fixed-link {
		speed = <1000>;
		full-duplex;
	};
};
