// SPDX-License-Identifier: GPL-2.0
/*
 * ARM Ltd. Versatile Express
 *
 * Motherboard Express uATX
 * V2M-P1
 *
 * HBI-0190D
 *
 * Original memory map ("Legacy memory map" in the board's
 * Technical Reference Manual)
 *
 * WARNING! The hardware described in this file is independent from the
 * RS1 variant (vexpress-v2m-rs1.dtsi), but there is a strong
 * correspondence between the two configurations.
 *
 * TAKE CARE WHEN MAINTAINING THIS FILE TO PROPAGATE ANY RELEVANT
 * CHANGES TO vexpress-v2m-rs1.dtsi!
 */
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	bus@40000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x40000000 0x40000000 0x10000000>,
			 <0x10000000 0x10000000 0x00020000>;

		#interrupt-cells = <1>;
		interrupt-map-mask = <0 63>;
		interrupt-map = <0  0 &gic GIC_SPI  0 IRQ_TYPE_LEVEL_HIGH>,
				<0  1 &gic GIC_SPI  1 IRQ_TYPE_LEVEL_HIGH>,
				<0  2 &gic GIC_SPI  2 IRQ_TYPE_LEVEL_HIGH>,
				<0  3 &gic GIC_SPI  3 IRQ_TYPE_LEVEL_HIGH>,
				<0  4 &gic GIC_SPI  4 IRQ_TYPE_LEVEL_HIGH>,
				<0  5 &gic GIC_SPI  5 IRQ_TYPE_LEVEL_HIGH>,
				<0  6 &gic GIC_SPI  6 IRQ_TYPE_LEVEL_HIGH>,
				<0  7 &gic GIC_SPI  7 IRQ_TYPE_LEVEL_HIGH>,
				<0  8 &gic GIC_SPI  8 IRQ_TYPE_LEVEL_HIGH>,
				<0  9 &gic GIC_SPI  9 IRQ_TYPE_LEVEL_HIGH>,
				<0 10 &gic GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
				<0 11 &gic GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
				<0 12 &gic GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
				<0 13 &gic GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
				<0 14 &gic GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
				<0 15 &gic GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				<0 16 &gic GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
				<0 17 &gic GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>,
				<0 18 &gic GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				<0 19 &gic GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
				<0 20 &gic GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
				<0 21 &gic GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>,
				<0 22 &gic GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>,
				<0 23 &gic GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>,
				<0 24 &gic GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>,
				<0 25 &gic GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
				<0 26 &gic GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>,
				<0 27 &gic GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>,
				<0 28 &gic GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>,
				<0 29 &gic GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>,
				<0 30 &gic GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>,
				<0 31 &gic GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>,
				<0 32 &gic GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
				<0 33 &gic GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
				<0 34 &gic GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
				<0 35 &gic GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>,
				<0 36 &gic GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
				<0 37 &gic GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
				<0 38 &gic GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>,
				<0 39 &gic GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>,
				<0 40 &gic GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
				<0 41 &gic GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>,
				<0 42 &gic GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;

		motherboard-bus@40000000 {
			arm,hbi = <0x190>;
			arm,vexpress,site = <0>;
			compatible = "arm,vexpress,v2m-p1", "simple-bus";
			#address-cells = <2>; /* SMB chipselect number and offset */
			#size-cells = <1>;
			ranges = <0 0 0x40000000 0x04000000>,
				 <1 0 0x44000000 0x04000000>,
				 <2 0 0x48000000 0x04000000>,
				 <3 0 0x4c000000 0x04000000>,
				 <7 0 0x10000000 0x00020000>;

			flash@0,******** {
				compatible = "arm,vexpress-flash", "cfi-flash";
				reg = <0 0x******** 0x04000000>,
				      <1 0x******** 0x04000000>;
				bank-width = <4>;
				partitions {
					compatible = "arm,arm-firmware-suite";
				};
			};

			psram@2,******** {
				compatible = "arm,vexpress-psram", "mtd-ram";
				reg = <2 0x******** 0x********>;
				bank-width = <4>;
			};

			ethernet@3,******** {
				compatible = "smsc,lan9118", "smsc,lan9115";
				reg = <3 0x******** 0x10000>;
				interrupts = <15>;
				phy-mode = "mii";
				reg-io-width = <4>;
				smsc,irq-active-high;
				smsc,irq-push-pull;
				vdd33a-supply = <&v2m_fixed_3v3>;
				vddvario-supply = <&v2m_fixed_3v3>;
			};

			usb@3,******** {
				compatible = "nxp,usb-isp1761";
				reg = <3 0x******** 0x20000>;
				interrupts = <16>;
				dr_mode = "peripheral";
			};

			iofpga@7,******** {
				compatible = "simple-bus";
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 7 0 0x20000>;

				v2m_sysreg: sysreg@0 {
					compatible = "arm,vexpress-sysreg";
					reg = <0x00000 0x1000>;
					#address-cells = <1>;
					#size-cells = <1>;
					ranges = <0 0 0x1000>;

					v2m_led_gpios: gpio@8 {
						compatible = "arm,vexpress-sysreg,sys_led";
						reg = <0x008 4>;
						gpio-controller;
						#gpio-cells = <2>;
					};

					v2m_mmc_gpios: gpio@48 {
						compatible = "arm,vexpress-sysreg,sys_mci";
						reg = <0x048 4>;
						gpio-controller;
						#gpio-cells = <2>;
					};

					v2m_flash_gpios: gpio@4c {
						compatible = "arm,vexpress-sysreg,sys_flash";
						reg = <0x04c 4>;
						gpio-controller;
						#gpio-cells = <2>;
					};
				};

				v2m_sysctl: sysctl@1000 {
					compatible = "arm,sp810", "arm,primecell";
					reg = <0x01000 0x1000>;
					clocks = <&v2m_refclk32khz>, <&v2m_refclk1mhz>, <&smbclk>;
					clock-names = "refclk", "timclk", "apb_pclk";
					#clock-cells = <1>;
					clock-output-names = "timerclken0", "timerclken1", "timerclken2", "timerclken3";
					assigned-clocks = <&v2m_sysctl 0>, <&v2m_sysctl 1>, <&v2m_sysctl 3>, <&v2m_sysctl 3>;
					assigned-clock-parents = <&v2m_refclk1mhz>, <&v2m_refclk1mhz>, <&v2m_refclk1mhz>, <&v2m_refclk1mhz>;
				};

				/* PCI-E I2C bus */
				v2m_i2c_pcie: i2c@2000 {
					compatible = "arm,versatile-i2c";
					reg = <0x02000 0x1000>;

					#address-cells = <1>;
					#size-cells = <0>;

					pcie-switch@60 {
						compatible = "idt,89hpes32h8";
						reg = <0x60>;
					};
				};

				aaci@4000 {
					compatible = "arm,pl041", "arm,primecell";
					reg = <0x04000 0x1000>;
					interrupts = <11>;
					clocks = <&smbclk>;
					clock-names = "apb_pclk";
				};

				mmci@5000 {
					compatible = "arm,pl180", "arm,primecell";
					reg = <0x05000 0x1000>;
					interrupts = <9>, <10>;
					cd-gpios = <&v2m_mmc_gpios 0 0>;
					wp-gpios = <&v2m_mmc_gpios 1 0>;
					max-frequency = <12000000>;
					vmmc-supply = <&v2m_fixed_3v3>;
					clocks = <&v2m_clk24mhz>, <&smbclk>;
					clock-names = "mclk", "apb_pclk";
				};

				kmi@6000 {
					compatible = "arm,pl050", "arm,primecell";
					reg = <0x06000 0x1000>;
					interrupts = <12>;
					clocks = <&v2m_clk24mhz>, <&smbclk>;
					clock-names = "KMIREFCLK", "apb_pclk";
				};

				kmi@7000 {
					compatible = "arm,pl050", "arm,primecell";
					reg = <0x07000 0x1000>;
					interrupts = <13>;
					clocks = <&v2m_clk24mhz>, <&smbclk>;
					clock-names = "KMIREFCLK", "apb_pclk";
				};

				v2m_serial0: serial@9000 {
					compatible = "arm,pl011", "arm,primecell";
					reg = <0x09000 0x1000>;
					interrupts = <5>;
					clocks = <&v2m_oscclk2>, <&smbclk>;
					clock-names = "uartclk", "apb_pclk";
				};

				v2m_serial1: serial@a000 {
					compatible = "arm,pl011", "arm,primecell";
					reg = <0x0a000 0x1000>;
					interrupts = <6>;
					clocks = <&v2m_oscclk2>, <&smbclk>;
					clock-names = "uartclk", "apb_pclk";
				};

				v2m_serial2: serial@b000 {
					compatible = "arm,pl011", "arm,primecell";
					reg = <0x0b000 0x1000>;
					interrupts = <7>;
					clocks = <&v2m_oscclk2>, <&smbclk>;
					clock-names = "uartclk", "apb_pclk";
				};

				v2m_serial3: serial@c000 {
					compatible = "arm,pl011", "arm,primecell";
					reg = <0x0c000 0x1000>;
					interrupts = <8>;
					clocks = <&v2m_oscclk2>, <&smbclk>;
					clock-names = "uartclk", "apb_pclk";
				};

				wdt@f000 {
					compatible = "arm,sp805", "arm,primecell";
					reg = <0x0f000 0x1000>;
					interrupts = <0>;
					clocks = <&v2m_refclk32khz>, <&smbclk>;
					clock-names = "wdog_clk", "apb_pclk";
				};

				v2m_timer01: timer@11000 {
					compatible = "arm,sp804", "arm,primecell";
					reg = <0x11000 0x1000>;
					interrupts = <2>;
					clocks = <&v2m_sysctl 0>, <&v2m_sysctl 1>, <&smbclk>;
					clock-names = "timclken1", "timclken2", "apb_pclk";
				};

				v2m_timer23: timer@12000 {
					compatible = "arm,sp804", "arm,primecell";
					reg = <0x12000 0x1000>;
					interrupts = <3>;
					clocks = <&v2m_sysctl 2>, <&v2m_sysctl 3>, <&smbclk>;
					clock-names = "timclken1", "timclken2", "apb_pclk";
				};

				/* DVI I2C bus */
				v2m_i2c_dvi: i2c@16000 {
					compatible = "arm,versatile-i2c";
					reg = <0x16000 0x1000>;
					#address-cells = <1>;
					#size-cells = <0>;

					dvi-transmitter@39 {
						compatible = "sil,sii9022-tpi", "sil,sii9022";
						reg = <0x39>;

						ports {
							#address-cells = <1>;
							#size-cells = <0>;

							/*
							 * Both the core tile and the motherboard routes their output
							 * pads to this transmitter. The motherboard system controller
							 * can select one of them as input using a mux register in
							 * "arm,vexpress-muxfpga". The Vexpress with the CA9 core tile is
							 * the only platform with this specific set-up.
							 */
							port@0 {
								reg = <0>;
								dvi_bridge_in_ct: endpoint {
									remote-endpoint = <&clcd_pads_ct>;
								};
							};
							port@1 {
								reg = <1>;
								dvi_bridge_in_mb: endpoint {
									remote-endpoint = <&clcd_pads_mb>;
								};
							};
						};
					};

					dvi-transmitter@60 {
						compatible = "sil,sii9022-cpi", "sil,sii9022";
						reg = <0x60>;
					};
				};

				rtc@17000 {
					compatible = "arm,pl031", "arm,primecell";
					reg = <0x17000 0x1000>;
					interrupts = <4>;
					clocks = <&smbclk>;
					clock-names = "apb_pclk";
				};

				compact-flash@1a000 {
					compatible = "arm,vexpress-cf", "ata-generic";
					reg = <0x1a000 0x100
					       0x1a100 0xf00>;
					reg-shift = <2>;
				};


				clcd@1f000 {
					compatible = "arm,pl111", "arm,primecell";
					reg = <0x1f000 0x1000>;
					interrupt-names = "combined";
					interrupts = <14>;
					clocks = <&v2m_oscclk1>, <&smbclk>;
					clock-names = "clcdclk", "apb_pclk";
					/* 800x600 16bpp @36MHz works fine */
					max-memory-bandwidth = <54000000>;
					memory-region = <&vram>;

					port {
						clcd_pads_mb: endpoint {
							remote-endpoint = <&dvi_bridge_in_mb>;
							arm,pl11x,tft-r0g0b0-pads = <0 8 16>;
						};
					};
				};
			};

			v2m_fixed_3v3: regulator-3v3 {
				compatible = "regulator-fixed";
				regulator-name = "3V3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			v2m_clk24mhz: clock-24000000 {
				compatible = "fixed-clock";
				#clock-cells = <0>;
				clock-frequency = <24000000>;
				clock-output-names = "v2m:clk24mhz";
			};

			v2m_refclk1mhz: clock-1000000 {
				compatible = "fixed-clock";
				#clock-cells = <0>;
				clock-frequency = <1000000>;
				clock-output-names = "v2m:refclk1mhz";
			};

			v2m_refclk32khz: clock-32768 {
				compatible = "fixed-clock";
				#clock-cells = <0>;
				clock-frequency = <32768>;
				clock-output-names = "v2m:refclk32khz";
			};

			leds {
				compatible = "gpio-leds";

				led-user1 {
					label = "v2m:green:user1";
					gpios = <&v2m_led_gpios 0 0>;
					linux,default-trigger = "heartbeat";
				};

				led-user2 {
					label = "v2m:green:user2";
					gpios = <&v2m_led_gpios 1 0>;
					linux,default-trigger = "mmc0";
				};

				led-user3 {
					label = "v2m:green:user3";
					gpios = <&v2m_led_gpios 2 0>;
					linux,default-trigger = "cpu0";
				};

				led-user4 {
					label = "v2m:green:user4";
					gpios = <&v2m_led_gpios 3 0>;
					linux,default-trigger = "cpu1";
				};

				led-user5 {
					label = "v2m:green:user5";
					gpios = <&v2m_led_gpios 4 0>;
					linux,default-trigger = "cpu2";
				};

				led-user6 {
					label = "v2m:green:user6";
					gpios = <&v2m_led_gpios 5 0>;
					linux,default-trigger = "cpu3";
				};

				led-user7 {
					label = "v2m:green:user7";
					gpios = <&v2m_led_gpios 6 0>;
					linux,default-trigger = "cpu4";
				};

				led-user8 {
					label = "v2m:green:user8";
					gpios = <&v2m_led_gpios 7 0>;
					linux,default-trigger = "cpu5";
				};
			};

			mcc {
				compatible = "arm,vexpress,config-bus";
				arm,vexpress,config-bridge = <&v2m_sysreg>;

				clock-controller-0 {
					/* MCC static memory clock */
					compatible = "arm,vexpress-osc";
					arm,vexpress-sysreg,func = <1 0>;
					freq-range = <25000000 60000000>;
					#clock-cells = <0>;
					clock-output-names = "v2m:oscclk0";
				};

				v2m_oscclk1: clock-controller-1 {
					/* CLCD clock */
					compatible = "arm,vexpress-osc";
					arm,vexpress-sysreg,func = <1 1>;
					freq-range = <23750000 65000000>;
					#clock-cells = <0>;
					clock-output-names = "v2m:oscclk1";
				};

				v2m_oscclk2: clock-controller-2 {
					/* IO FPGA peripheral clock */
					compatible = "arm,vexpress-osc";
					arm,vexpress-sysreg,func = <1 2>;
					freq-range = <24000000 24000000>;
					#clock-cells = <0>;
					clock-output-names = "v2m:oscclk2";
				};

				regulator-vio {
					/* Logic level voltage */
					compatible = "arm,vexpress-volt";
					arm,vexpress-sysreg,func = <2 0>;
					regulator-name = "VIO";
					regulator-always-on;
					label = "VIO";
				};

				temp-mcc {
					/* MCC internal operating temperature */
					compatible = "arm,vexpress-temp";
					arm,vexpress-sysreg,func = <4 0>;
					label = "MCC";
				};

				reset {
					compatible = "arm,vexpress-reset";
					arm,vexpress-sysreg,func = <5 0>;
				};

				muxfpga {
					compatible = "arm,vexpress-muxfpga";
					arm,vexpress-sysreg,func = <7 0>;
				};

				shutdown {
					compatible = "arm,vexpress-shutdown";
					arm,vexpress-sysreg,func = <8 0>;
				};

				reboot {
					compatible = "arm,vexpress-reboot";
					arm,vexpress-sysreg,func = <9 0>;
				};

				dvimode {
					compatible = "arm,vexpress-dvimode";
					arm,vexpress-sysreg,func = <11 0>;
				};
			};
		};
	};
};
