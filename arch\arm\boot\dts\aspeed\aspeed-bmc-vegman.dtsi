// SPDX-License-Identifier: GPL-2.0+
// Copyright (C) 2021 YADRO

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	aliases {
		serial4 = &uart5;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlyprintk";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		ramoops@9eff0000{
			compatible = "ramoops";
			reg = <0x9eff0000 0x10000>;
			record-size = <0x2000>;
			console-size = <0x2000>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 12>, <&adc 13>, <&adc 14>, <&adc 15>;
	};

	leds {
		compatible = "gpio-leds";

		identify {
			label = "platform:blue:indicator";
			linux,default-trigger = "heartbeat";
			gpios = <&gpio ASPEED_GPIO(S, 6) GPIO_ACTIVE_LOW>;
		};

		status_amber {
			label = "platform:red:status";
			default-state = "off";
			gpios = <&gpio ASPEED_GPIO(S, 5) GPIO_ACTIVE_LOW>;
		};

		status_green {
			label = "platform:green:status";
			default-state = "off";
			gpios = <&gpio ASPEED_GPIO(S, 4) GPIO_ACTIVE_LOW>;
		};

		power_fault {
			label = "platform:red:power";
			default-state = "off";
			gpios = <&gpio ASPEED_GPIO(AA, 4) GPIO_ACTIVE_LOW>;
		};

		power_ok {
			label = "platform:green:power";
			default-state = "off";
			gpios = <&gpio ASPEED_GPIO(AA, 5) GPIO_ACTIVE_LOW>;
		};
	};

	beeper {
		compatible = "pwm-beeper";
		pwms = <&timer 5 1000000 0>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		label = "bmc";
		m25p,fast-read;
#include "openbmc-flash-layout-64.dtsi"
	};
};

&spi2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi2ck_default
			&pinctrl_spi2miso_default
			&pinctrl_spi2mosi_default
			&pinctrl_spi2cs0_default>;
	flash@0 {
		status = "okay";
		label = "bios";
		m25p,fast-read;
	};
};

&mac0 {
	status = "okay";
	use-ncsi;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;

	phy-mode = "rgmii";
	phy-handle = <&phy>;
	mdio {
		#address-cells = <1>;
		#size-cells = <0>;

		phy: ethernet-phy@1 {
			/* KSZ9131 */
			compatible = "ethernet-phy-id0022.1640";
			reg = <1>;

			micrel,led-mode = <0>;
		};
	};
};

&vhub {
	status = "okay";
};

&adc {
	status = "okay";
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&sdmmc {
	status = "okay";
};

&sdhci1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sd2_default>;
	disable-wp;
};

&timer {
	fttmr010,pwm-outputs = <5>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_timer5_default>;
	#pwm-cells = <3>;
	status = "okay";
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default
			&pinctrl_nrts1_default
			&pinctrl_ndtr1_default
			&pinctrl_ndsr1_default
			&pinctrl_ncts1_default
			&pinctrl_ndcd1_default
			&pinctrl_nri1_default>;
};

&uart5 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&kcs3 {
	aspeed,lpc-io-reg = <0xCA2>;
	status = "okay";
};

&kcs4 {
	aspeed,lpc-io-reg = <0xCA4>;
	status = "okay";
};

&lpc_snoop {
	snoop-ports = <0x80>;
	status = "okay";
};

&uart_routing {
	status = "okay";
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <>;
};

&uart3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <>;
};

&uart4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <>;
};

&i2c0 {
	/* SMB_IPMB_STBY_LVC3 */
	multi-master;
	status = "okay";
};

&i2c1 {
	/* SMB_CHASSENSOR_STBY_LVC3 */
	status = "okay";
};

&i2c2 {
	/* SMB_PCIE_STBY_LVC3 */
	status = "okay";
};

&i2c3 {
	/* SMB_HOST_STBY_LVC3 */
	multi-master;
	status = "okay";
};

&i2c4 {
	/* BMC_PMBUS2_STBY */
	status = "okay";
};

&i2c5 {
	/* SMB_SMLINK0_STBY_LVC3 */
	bus-frequency = <1000000>;
	multi-master;
	status = "okay";
};

&i2c6 {
	/* SMB_TEMPSENSOR_STBY_LVC3 */
	multi-master;
	status = "okay";
};

&i2c7 {
	/* SMB_SM_PMB1_SML1_STBY_LVC3 */
	multi-master;
	status = "okay";
};

&i2c9 {
	/* SMB_BMC_ETH3_LVC3 */
	status = "okay";
};

&i2c10 {
	/* SMB_BMC_ETH2_LVC3 */
	status = "okay";
};

&i2c11 {
	/* SMB_BMC_MGMT_LVC3 */
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
		pagesize = <32>;
		size = <8192>;
		address-width = <16>;
	};
};

&i2c12 {
	/* SMB_BMC_FAULT_EXP_LVC3 */
	status = "okay";
};

&i2c13 {
	/* SMB_PCIE2_STBY_LVC3 */
	status = "okay";
};
