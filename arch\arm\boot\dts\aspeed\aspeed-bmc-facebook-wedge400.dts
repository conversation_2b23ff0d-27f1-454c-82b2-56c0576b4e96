// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2019 Facebook Inc.
/dts-v1/;

#include <dt-bindings/gpio/aspeed-gpio.h>
#include "ast2500-facebook-netbmc-common.dtsi"

/ {
	model = "Facebook Wedge 400 BMC";
	compatible = "facebook,wedge400-bmc", "aspeed,ast2500";

	aliases {
		/*
		 * PCA9548 (2-0070) provides 8 channels connecting to
		 * SCM (System Controller Module).
		 */
		i2c16 = &imux16;
		i2c17 = &imux17;
		i2c18 = &imux18;
		i2c19 = &imux19;
		i2c20 = &imux20;
		i2c21 = &imux21;
		i2c22 = &imux22;
		i2c23 = &imux23;

		/*
		 * PCA9548 (8-0070) provides 8 channels connecting to
		 * SMB (Switch Main Board).
		 */
		i2c24 = &imux24;
		i2c25 = &imux25;
		i2c26 = &imux26;
		i2c27 = &imux27;
		i2c28 = &imux28;
		i2c29 = &imux29;
		i2c30 = &imux30;
		i2c31 = &imux31;

		/*
		 * PCA9548 (11-0076) provides 8 channels connecting to
		 * FCM (Fan Controller Module).
		 */
		i2c32 = &imux32;
		i2c33 = &imux33;
		i2c34 = &imux34;
		i2c35 = &imux35;
		i2c36 = &imux36;
		i2c37 = &imux37;
		i2c38 = &imux38;
		i2c39 = &imux39;

		spi2 = &spi_gpio;
	};

	chosen {
		stdout-path = &uart1;
		bootargs = "console=ttyS0,9600n8 root=/dev/ram rw";
	};

	ast-adc-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>, <&adc 4>,
			      <&adc 5>, <&adc 6>, <&adc 7>, <&adc 8>;
	};

	/*
	 * GPIO-based SPI Master is required to access SPI TPM, because
	 * full-duplex SPI transactions are not supported by ASPEED SPI
	 * Controllers.
	 */
	spi_gpio: spi {
		status = "okay";
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		cs-gpios = <&gpio ASPEED_GPIO(R, 2) GPIO_ACTIVE_LOW>;
		gpio-sck = <&gpio ASPEED_GPIO(R, 3) GPIO_ACTIVE_HIGH>;
		gpio-mosi = <&gpio ASPEED_GPIO(R, 4) GPIO_ACTIVE_HIGH>;
		gpio-miso = <&gpio ASPEED_GPIO(R, 5) GPIO_ACTIVE_HIGH>;
		num-chipselects = <1>;

		tpm@0 {
			compatible = "infineon,slb9670", "tcg,tpm_tis-spi";
			spi-max-frequency = <33000000>;
			reg = <0>;
		};
	};
};

/*
 * Both firmware flashes are 128MB on Wedge400 BMC.
 */
&fmc_flash0 {
#include "facebook-bmc-flash-layout-128.dtsi"
};

&fmc_flash1 {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		flash1@0 {
			reg = <0x0 0x8000000>;
			label = "flash1";
		};
	};
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default
		     &pinctrl_rxd2_default>;
};

&uart4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd4_default
		     &pinctrl_rxd4_default>;
};

/*
 * I2C bus #0 is multi-master environment dedicated for BMC and Bridge IC
 * communication.
 */
&i2c0 {
	status = "okay";
	multi-master;
	bus-frequency = <1000000>;
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		imux16: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		imux17: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		imux18: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		imux19: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		imux20: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux21: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux22: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux23: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		imux24: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		imux25: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		imux26: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		imux27: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		imux28: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux29: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux30: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux31: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};

	};
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";

	i2c-mux@76 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x76>;
		i2c-mux-idle-disconnect;

		imux32: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		imux33: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		imux34: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		imux35: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		imux36: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux37: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux38: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux39: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};

	};
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&adc {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&sdhci1 {
	max-frequency = <25000000>;
	/*
	 * DMA mode needs to be disabled to avoid conflicts with UHCI
	 * Controller in AST2500 SoC.
	 */
	sdhci-caps-mask = <0x0 0x580000>;
};
