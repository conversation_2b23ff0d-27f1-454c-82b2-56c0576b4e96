// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Copyright (C) 2016 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "bcm53573.dtsi"

/ {
	compatible = "tenda,ac9", "brcm,bcm47189", "brcm,bcm53573";
	model = "Tenda AC9";

	chosen {
		bootargs = "console=ttyS0,115200 earlycon";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x08000000>;
	};

	leds-0 {
		compatible = "gpio-leds";

		led-usb {
			label = "bcm53xx:blue:usb";
			gpios = <&chipcommon 1 GPIO_ACTIVE_HIGH>;
			trigger-sources = <&ohci_port1>, <&ehci_port1>;
			linux,default-trigger = "usbport";
		};

		led-wps {
			label = "bcm53xx:blue:wps";
			gpios = <&chipcommon 10 GPIO_ACTIVE_HIGH>;
		};

		led-5ghz {
			label = "bcm53xx:blue:5ghz";
			gpios = <&chipcommon 11 GPIO_ACTIVE_HIGH>;
		};

		led-system {
			label = "bcm53xx:blue:system";
			gpios = <&chipcommon 15 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "timer";
		};
	};

	leds-1 {
		compatible = "gpio-leds";

		led-2ghz {
			label = "bcm53xx:blue:2ghz";
			gpios = <&pcie0_chipcommon 3 GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-rfkill {
			label = "WiFi";
			linux,code = <KEY_RFKILL>;
			gpios = <&chipcommon 3 GPIO_ACTIVE_LOW>;
		};

		button-restart {
			label = "Reset";
			linux,code = <KEY_RESTART>;
			gpios = <&chipcommon 7 GPIO_ACTIVE_LOW>;
		};

		button-wps {
			label = "WPS";
			linux,code = <KEY_WPS_BUTTON>;
			gpios = <&chipcommon 9 GPIO_ACTIVE_LOW>;
		};
	};
};

&pcie0 {
	ranges = <0x00000000 0 0 0 0 0x00100000>;
	#address-cells = <3>;
	#size-cells = <2>;

	bridge@0,0,0 {
		reg = <0x0000 0 0 0 0>;
		ranges = <0x00000000 0 0 0 0 0 0 0x00100000>;
		#address-cells = <3>;
		#size-cells = <2>;

		wifi@0,1,0 {
			reg = <0x0000 0 0 0 0>;
			ranges = <0x00000000 0 0 0 0x00100000>;
			#address-cells = <1>;
			#size-cells = <1>;

			pcie0_chipcommon: chipcommon@0 {
				reg = <0 0x1000>;

				gpio-controller;
				#gpio-cells = <2>;
			};
		};
	};
};

&switch {
	status = "okay";

	ports {
		port@0 {
			label = "wan";
		};

		port@1 {
			label = "lan1";
		};

		port@2 {
			label = "lan2";
		};

		port@3 {
			label = "lan3";
		};

		port@4 {
			label = "lan4";
		};

		port@8 {
			label = "cpu";
		};
	};
};
