// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2021 Inventec Corp.

/dts-v1/;

#include "aspeed-g6.dtsi"
#include "aspeed-g6-pinctrl.dtsi"
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
       model = "TRANSFORMERS BMC";
       compatible = "inventec,transformer-bmc", "aspeed,ast2600";

       aliases {
               serial4 = &uart5;
       };

       chosen {
               stdout-path = &uart5;
               bootargs = "console=ttyS4,115200n8";
       };

       memory@80000000 {
               device_type = "memory";
               reg = <0x80000000 0x80000000>;
       };

       leds {
               compatible = "gpio-leds";

               // UID led
               uid {
                       label = "UID_LED";
                       gpios = <&gpio0 ASPEED_GPIO(X, 0) GPIO_ACTIVE_LOW>;
               };

               // Heart beat led
               heartbeat {
                       label = "HB_LED";
                       gpios = <&gpio0 ASPEED_GPIO(P, 7) GPIO_ACTIVE_LOW>;
               };
       };
};

&mdio0 {
       status = "okay";

       ethphy0: ethernet-phy@0 {
               compatible = "ethernet-phy-ieee802.3-c22";
               reg = <1>;
       };
};

&mac3 {
       status = "okay";
       phy-mode = "rgmii";
       phy-handle = <&ethphy0>;
       pinctrl-names = "default";
       pinctrl-0 = <&pinctrl_rgmii4_default>;
};

&fmc {
       status = "okay";

       flash@0 {
               status = "okay";
               m25p,fast-read;
               label = "bmc";
               spi-max-frequency = <33000000>;
               spi-tx-bus-width = <2>;
               spi-rx-bus-width = <2>;
#include "openbmc-flash-layout.dtsi"
       };

       flash@1 {
               status = "okay";
               m25p,fast-read;
               label = "bmc2";
               spi-max-frequency = <33000000>;
               spi-tx-bus-width = <2>;
               spi-rx-bus-width = <2>;
       };
};

&spi1 {
       status = "okay";
       pinctrl-names = "default";
       pinctrl-0 = <&pinctrl_spi1_default>;

       flash@0 {
               status = "okay";
               m25p,fast-read;
               label = "bios";
               spi-max-frequency = <33000000>;
               spi-tx-bus-width = <1>;
               spi-rx-bus-width = <1>;
       };
};

&wdt1 {
       status = "okay";
};

&uart1 {
       status = "okay";
};

&uart5 {
       status = "okay";
};

&i2c0 {
       status = "okay";

       //Set bmc' slave address;
       bmc_slave@10 {
               compatible = "ipmb-dev";
               reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
               i2c-protocol;
       };
};

&i2c2 {
       status = "okay";
};

&i2c3 {
       // FRU AT24C512C-SSHM-T
       status = "okay";
       eeprom@50 {
               compatible = "atmel,24c512";
               reg = <0x50>;
               pagesize = <128>;
       };
};

&i2c5 {
       status = "okay";
};

&i2c6 {
       status = "okay";

       tmp75@49 {
               compatible = "ti,tmp75";
               reg = <0x49>;
       };

       tmp75@4f {
               compatible = "ti,tmp75";
               reg = <0x4f>;
       };

       tmp468@48 {
               compatible = "ti,tmp468";
               reg = <0x48>;
       };
};

&i2c7 {
       status = "okay";
       adm1278@40 {
               compatible = "adi,adm1278";
               reg = <0x40>;
       };
};


&i2c8 {
       // FRU AT24C512C-SSHM-T
       status = "okay";

       eeprom@51 {
               compatible = "atmel,24c512";
               reg = <0x51>;
               pagesize = <128>;
       };

       eeprom@53 {
               compatible = "atmel,24c512";
               reg = <0x53>;
               pagesize = <128>;
       };
};

&i2c9 {
       // M.2
       status = "okay";
};

&i2c10 {
       // I2C EXPANDER
       status = "okay";

       i2c-mux@71 {
               compatible = "nxp,pca9544";
               #address-cells = <1>;
               #size-cells = <0>;
               reg = <0x71>;
       };

       i2c-mux@73 {
               compatible = "nxp,pca9544";
               #address-cells = <1>;
               #size-cells = <0>;
               reg = <0x73>;
       };
};

&i2c11 {
       // I2C EXPANDER
       status = "okay";

       i2c-mux@70 {
               compatible = "nxp,pca9544";
               #address-cells = <1>;
               #size-cells = <0>;
               reg = <0x70>;

               pcie_eeprom_riser1: i2c@0 {
                       #address-cells = <1>;
                       #size-cells = <0>;
                       reg = <0>;

                       eeprom@55 {
                               compatible = "atmel,24c512";
                               reg = <0x55>;
                               pagesize = <128>;
                       };
               };

               pcie_eeprom_riser2: i2c@1 {
                       #address-cells = <1>;
                       #size-cells = <0>;
                       reg = <1>;

                       eeprom@55 {
                               compatible = "atmel,24c512";
                               reg = <0x55>;
                               pagesize = <128>;
                       };
               };

               pcie_eeprom_riser3: i2c@2 {
                       #address-cells = <1>;
                       #size-cells = <0>;
                       reg = <2>;

                       eeprom@55 {
                               compatible = "atmel,24c512";
                               reg = <0x55>;
                               pagesize = <128>;
                       };
               };
       };
};

&i2c12 {
       status = "okay";

       psu0:psu0@58 {
               compatible = "pmbus";
               reg = <0x58>;
       };
};

&gpio0 {
       status = "okay";
       gpio-line-names =
       /*A0-A7*/   "","","","","","","","",
       /*B0-B7*/   "presence-ps0","power-chassis-good","","","","","presence-ps1","",
       /*C0-C7*/   "","","","","","","","",
       /*D0-D7*/   "","","","","","","","",
       /*E0-E7*/   "","","","","","","","",
       /*F0-F7*/   "","","","","power-chassis-control","","","",
       /*G0-G7*/   "","","jtag-mux","","","","","",
       /*H0-H7*/   "","","","","reset-button","power-button","","",
       /*I0-I7*/   "","","","","","","","",
       /*J0-J7*/   "","","","","","","","",
       /*K0-K7*/   "","","","","","","","",
       /*L0-L7*/   "","","","","","","","",
       /*M0-M7*/   "","","","","","","","",
       /*N0-N7*/   "","","","","","","","",
       /*O0-O7*/   "","","","","","","","",
       /*P0-P7*/   "","","","tck-mux","","","","",
       /*Q0-Q7*/   "","","","","","","","",
       /*R0-R7*/   "","","","","","","","",
       /*S0-S7*/   "","","","","","","","",
       /*T0-T7*/   "","","","","","","","",
       /*U0-U7*/   "","nmi-button","","","","","","",
       /*V0-V7*/   "","","","","power-config-full-load","","","",
       /*W0-W7*/   "","","","","","","","",
       /*X0-X7*/   "","","","","","","","",
       /*Y0-Y7*/   "","","","","","","","",
       /*Z0-Z7*/   "","","","","","","","",
       /*AA0-AA7*/ "","","","","","","","",
       /*AB0-AB7*/ "","","","","","","","",
       /*AC0-AC7*/ "","","","","","","","";
};

&lpc_snoop {
       status = "okay";
       snoop-ports = <0x80>;
};

&emmc_controller {
       status = "okay";
};

&emmc {
       status = "okay";
       non-removable;
       max-frequency = <52000000>;
       bus-width = <8>;
};

&vhub {
       status = "okay";
       aspeed,vhub-downstream-ports = <7>;
       aspeed,vhub-generic-endpoints = <21>;
       pinctrl-names = "default";
       pinctrl-0 = <&pinctrl_usb2ad_default>;
};

&rtc {
       status = "okay";
};
