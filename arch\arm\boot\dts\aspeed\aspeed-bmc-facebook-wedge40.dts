// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2018 Facebook Inc.
/dts-v1/;

#include "ast2400-facebook-netbmc-common.dtsi"

/ {
	model = "Facebook Wedge 40 BMC";
	compatible = "facebook,wedge40-bmc", "aspeed,ast2400";

	chosen {
		stdout-path = &uart3;
		bootargs = "console=ttyS2,9600n8 root=/dev/ram rw";
	};

	ast-adc-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 5>, <&adc 6>, <&adc 7>, <&adc 8>, <&adc 9>;
	};
};

&wdt2 {
	status = "disabled";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default
		     &pinctrl_pwm1_default
		     &pinctrl_pwm6_default
		     &pinctrl_pwm7_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00 0x01>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02 0x03>;
	};

	fan@6 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04 0x05>;
	};

	fan@7 {
		reg = <0x07>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06 0x07>;
	};
};
