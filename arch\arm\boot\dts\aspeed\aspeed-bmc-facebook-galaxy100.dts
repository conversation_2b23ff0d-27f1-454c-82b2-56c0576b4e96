// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2020 Facebook Inc.
/dts-v1/;

#include "ast2400-facebook-netbmc-common.dtsi"

/ {
	model = "Facebook Galaxy 100 BMC";
	compatible = "facebook,galaxy100-bmc", "aspeed,ast2400";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS0,9600n8 root=/dev/ram rw";
	};

	ast-adc-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 3>, <&adc 4>, <&adc 8>, <&adc 9>;
	};
};

&wdt2 {
	status = "okay";
	aspeed,reset-type = "system";
};

&fmc {
	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "spi0.1";

		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			flash1@0 {
				reg = <0x0 0x2000000>;
				label = "flash1";
			};
		};
	};
};


&i2c9 {
	status = "okay";
};

&vhub {
	status = "okay";
};
