/*
 *  BSD LICENSE
 *
 *  Copyright(c) 2016 Broadcom.  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    * Redistributions of source code must retain the above copyright
 *      notice, this list of conditions and the following disclaimer.
 *    * Redistributions in binary form must reproduce the above copyright
 *      notice, this list of conditions and the following disclaimer in
 *      the documentation and/or other materials provided with the
 *      distribution.
 *    * Neither the name of Broadcom Corporation nor the names of its
 *      contributors may be used to endorse or promote products derived
 *      from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDE<PERSON>AL,
 *  <PERSON>ECIA<PERSON>, E<PERSON>EMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/dts-v1/;

#include "bcm-nsp.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "NorthStar Plus SVK (BCM958625HR)";
	compatible = "brcm,bcm958625hr", "brcm,bcm58625", "brcm,nsp";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x20000000>;
	};

	gpio-restart {
		compatible = "gpio-restart";
		gpios = <&gpioa 15 GPIO_ACTIVE_LOW>;
		open-source;
		priority = <200>;
	};

	sfp: sfp {
		compatible = "sff,sfp";
		i2c-bus = <&i2c0>;
		mod-def0-gpios = <&gpioa 28 GPIO_ACTIVE_LOW>;
		los-gpios = <&gpioa 24 GPIO_ACTIVE_HIGH>;
		tx-fault-gpios = <&gpioa 30 GPIO_ACTIVE_HIGH>;
		tx-disable-gpios = <&gpioa 26 GPIO_ACTIVE_HIGH>;
	};
};

&i2c0 {
	status = "okay";
};

&dma {
	status = "okay";
};

&amac0 {
	status = "okay";
};

&amac1 {
	status = "okay";
};

&amac2 {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&nand_controller {
	nand@0 {
		compatible = "brcm,nandcs";
		reg = <0>;
		nand-on-flash-bbt;

		#address-cells = <1>;
		#size-cells = <1>;

		nand-ecc-strength = <24>;
		nand-ecc-step-size = <1024>;

		brcm,nand-oob-sector-size = <27>;

		partition@0 {
			label = "nboot";
			reg = <0x00000000 0x00200000>;
			read-only;
		};
		partition@200000 {
			label = "nenv";
			reg = <0x00200000 0x00400000>;
		};
		partition@600000 {
			label = "nsystem";
			reg = <0x00600000 0x00a00000>;
		};
		partition@1000000 {
			label = "nrootfs";
			reg = <0x01000000 0x03000000>;
		};
		partition@4000000 {
			label = "ncustfs";
			reg = <0x04000000 0x3c000000>;
		};
	};
};

&ohci0 {
	status = "okay";
};

&pcie0 {
	status = "okay";
};

&pcie1 {
	status = "okay";
};

&pinctrl {
	pinctrl-names = "default";
	pinctrl-0 = <&nand_sel>;
	nand_sel: nand_sel {
		function = "nand";
		groups = "nand_grp";
	};
};

&qspi {
	status = "okay";
	flash: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "m25p80";
		reg = <0x0>;
		spi-max-frequency = <12500000>;
		m25p,fast-read;
		spi-cpol;
		spi-cpha;

		partition@0 {
			label = "boot";
			reg = <0x00000000 0x000a0000>;
		};

		partition@a0000 {
			label = "env";
			reg = <0x000a0000 0x00060000>;
		};

		partition@100000 {
			label = "system";
			reg = <0x00100000 0x00600000>;
		};

		partition@700000 {
			label = "rootfs";
			reg = <0x00700000 0x01900000>;
		};
	};
};

&sata_phy0 {
	status = "okay";
};

&sata_phy1 {
	status = "okay";
};

&srab {
	compatible = "brcm,bcm58625-srab", "brcm,nsp-srab";
	status = "okay";

	ports {
		port@0 {
			label = "port0";
			reg = <0>;
		};

		port@1 {
			label = "port1";
			reg = <1>;
		};

		port@2 {
			label = "port2";
			reg = <2>;
		};

		port@3 {
			label = "port3";
			reg = <3>;
		};

		port@4 {
			label = "port4";
			reg = <4>;
		};

		port@5 {
			label = "sfp";
			phy-mode = "sgmii";
			reg = <5>;
			sfp = <&sfp>;
			managed = "in-band-status";
		};

		port@8 {
			ethernet = <&amac2>;
			label = "cpu";
			reg = <8>;
			fixed-link {
				speed = <1000>;
				full-duplex;
			};
		};
	};
};

&uart0 {
	status = "okay";
};

&usb3_phy {
	status = "okay";
};

&xhci {
	status = "okay";
};
