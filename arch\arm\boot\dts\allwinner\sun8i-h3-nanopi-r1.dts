// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2019 <PERSON> <<EMAIL>>
 * Copyright (C) 2020 Jayantajit Gogoi <<EMAIL>>
 * Copyright (C) 2020 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
*/

#include "sun8i-h3-nanopi.dtsi"
#include <dt-bindings/leds/common.h>

/ {
	model = "FriendlyARM NanoPi R1";
	compatible = "friendlyarm,nanopi-r1", "allwinner,sun8i-h3";

	aliases {
		serial1 = &uart1;
		ethernet0 = &emac;
		ethernet1 = &wifi;
	};

	reg_gmac_3v3: gmac-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "gmac-3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		startup-delay-us = <100000>;
		enable-active-high;
		gpio = <&pio 3 6 GPIO_ACTIVE_HIGH>; /* PD6 */
	};

	reg_vdd_cpux: gpio-regulator {
		compatible = "regulator-gpio";
		regulator-name = "vdd-cpux";
		regulator-type = "voltage";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1100000>;
		regulator-max-microvolt = <1300000>;
		regulator-ramp-delay = <50>;
		gpios = <&r_pio 0 6 GPIO_ACTIVE_HIGH>; /* PL6 */
		gpios-states = <0x1>;
		states = <1100000 0x0>,
			 <1300000 0x1>;
	};

	wifi_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&r_pio 0 7 GPIO_ACTIVE_LOW>; /* PL7 */
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "ext_clock";
	};

	leds {
		led-2 {
			function = LED_FUNCTION_WAN;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&pio 6 11 GPIO_ACTIVE_HIGH>; /* PG11 */
		};

		led-3 {
			function = LED_FUNCTION_LAN;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&pio 0 9 GPIO_ACTIVE_HIGH>; /* PA9 */
		};
	};
};

&cpu0 {
	cpu-supply = <&reg_vdd_cpux>;
};

&ehci1 {
	status = "okay";
};

&ehci2 {
	status = "okay";
};

&emac {
	pinctrl-names = "default";
	pinctrl-0 = <&emac_rgmii_pins>;
	phy-supply = <&reg_gmac_3v3>;
	phy-handle = <&ext_rgmii_phy>;
	phy-mode = "rgmii-id";
	status = "okay";
};

&external_mdio {
	ext_rgmii_phy: ethernet-phy@7 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <7>;
	};
};

&mmc1 {
	vmmc-supply = <&reg_vcc3v3>;
	vqmmc-supply = <&reg_vcc3v3>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	wifi: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&pio>;
		interrupts = <6 10 IRQ_TYPE_LEVEL_LOW>; /* PG10 / EINT10 */
		interrupt-names = "host-wake";
	};
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	vqmmc-supply = <&reg_vcc3v3>;
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&ohci2 {
	status = "okay";
};

&reg_usb0_vbus {
	gpio = <&r_pio 0 2 GPIO_ACTIVE_HIGH>; /* PL2 */
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>, <&uart3_rts_cts_pins>;
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "lpo";
		vbat-supply = <&reg_vcc3v3>;
		vddio-supply = <&reg_vcc3v3>;
		device-wakeup-gpios = <&pio 0 8 GPIO_ACTIVE_HIGH>; /* PA8 */
		host-wakeup-gpios = <&pio 0 7 GPIO_ACTIVE_HIGH>; /* PA7 */
		shutdown-gpios = <&pio 6 13 GPIO_ACTIVE_HIGH>; /* PG13 */
	};
};

&usb_otg {
	status = "okay";
	dr_mode = "otg";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 12 GPIO_ACTIVE_HIGH>; /* PG12 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	status = "okay";
};
