// SPDX-License-Identifier: GPL-2.0
// Copyright (c) 2020 Super Micro Computer, Inc

/dts-v1/;

#include "aspeed-g5.dtsi"

/ {
	model = "X11SPI BMC";
	compatible = "supermicro,x11spi-bmc", "aspeed,ast2500";

	chosen {
		stdout-path = &uart5;
		bootargs = "earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@7f000000 {
			no-map;
			reg = <0x7f000000 0x01000000>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 12>, <&adc 13>, <&adc 14>, <&adc 15>;
	};

};

&gpio {
    status = "okay";
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
	};
};

&uart5 {
	status = "okay";
};

&mac0 {
    status = "okay";

    pinctrl-names = "default";
    pinctrl-0 = <&pinctrl_rmii1_default>;
    clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
           <&syscon ASPEED_CLK_MAC1RCLK>;
    clock-names = "MACCLK", "RCLK";
    use-ncsi;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&gfx {
	status = "okay";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default
			 &pinctrl_pwm2_default &pinctrl_pwm3_default
			 &pinctrl_pwm4_default &pinctrl_pwm5_default
			 &pinctrl_pwm6_default &pinctrl_pwm7_default>;
};
