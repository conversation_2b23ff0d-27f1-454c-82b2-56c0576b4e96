// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "Witherspoon BMC";
	compatible = "ibm,witherspoon-bmc", "aspeed,ast2500";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		flash_memory: region@98000000 {
			no-map;
			reg = <0x98000000 0x04000000>; /* 64M */
		};

		vga_memory: region@9f000000 {
			no-map;
			compatible = "shared-dma-pool";
			reg = <0x9f000000 0x01000000>; /* 16M */
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32MM */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-air-water {
			label = "air-water";
			gpios = <&gpio ASPEED_GPIO(B, 5) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(B, 5)>;
		};

		event-checkstop {
			label = "checkstop";
			gpios = <&gpio ASPEED_GPIO(J, 2) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(J, 2)>;
		};

		event-ps0-presence {
			label = "ps0-presence";
			gpios = <&gpio ASPEED_GPIO(P, 7) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(P, 7)>;
		};

		event-ps1-presence {
			label = "ps1-presence";
			gpios = <&gpio ASPEED_GPIO(N, 0) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(N, 0)>;
		};
	};

	iio-hwmon-battery {
		compatible = "iio-hwmon";
		io-channels = <&adc 12>;
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-fan0-presence {
			label = "fan0-presence";
			gpios = <&pca0 4 GPIO_ACTIVE_LOW>;
			linux,code = <4>;
		};

		event-fan1-presence {
			label = "fan1-presence";
			gpios = <&pca0 5 GPIO_ACTIVE_LOW>;
			linux,code = <5>;
		};

		event-fan2-presence {
			label = "fan2-presence";
			gpios = <&pca0 6 GPIO_ACTIVE_LOW>;
			linux,code = <6>;
		};

		event-fan3-presence {
			label = "fan3-presence";
			gpios = <&pca0 7 GPIO_ACTIVE_LOW>;
			linux,code = <7>;
		};
	};

	leds {
		compatible = "gpio-leds";

		fan0 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca0 0 GPIO_ACTIVE_LOW>;
		};

		fan1 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca0 1 GPIO_ACTIVE_LOW>;
		};

		fan2 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca0 2 GPIO_ACTIVE_LOW>;
		};

		fan3 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca0 3 GPIO_ACTIVE_LOW>;
		};

		front-fault {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca0 13 GPIO_ACTIVE_LOW>;
		};

		front-power {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca0 14 GPIO_ACTIVE_LOW>;
		};

		front-id {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca0 15 GPIO_ACTIVE_LOW>;
		};

		rear-fault {
			gpios = <&gpio ASPEED_GPIO(N, 2) GPIO_ACTIVE_LOW>;
		};

		rear-id {
			gpios = <&gpio ASPEED_GPIO(N, 4) GPIO_ACTIVE_LOW>;
		};

		rear-power {
			gpios = <&gpio ASPEED_GPIO(N, 3) GPIO_ACTIVE_LOW>;
		};

		power-button {
			gpios = <&gpio ASPEED_GPIO(R, 5) GPIO_ACTIVE_LOW>;
		};
	};

	fsi: gpio-fsi {
		compatible = "fsi-master-gpio", "fsi-master";
		#address-cells = <2>;
		#size-cells = <0>;
		no-gpio-delays;

		clock-gpios = <&gpio ASPEED_GPIO(AA, 0) GPIO_ACTIVE_HIGH>;
		data-gpios = <&gpio ASPEED_GPIO(E, 0) GPIO_ACTIVE_HIGH>;
		mux-gpios = <&gpio ASPEED_GPIO(A, 6) GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio ASPEED_GPIO(D, 0) GPIO_ACTIVE_HIGH>;
		trans-gpios = <&gpio ASPEED_GPIO(R, 2) GPIO_ACTIVE_HIGH>;
	};

	iio-hwmon-dps310 {
		compatible = "iio-hwmon";
		io-channels = <&dps 0>;
	};

	iio-hwmon-bmp280 {
		compatible = "iio-hwmon";
		io-channels = <&bmp 1>;
	};

};

&gpio {
	gpio-line-names =
	/*A0-A7*/	"","cfam-reset","","","","","fsi-mux","",
	/*B0-B7*/	"","","","","","air-water","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"fsi-enable","","","","","","","",
	/*E0-E7*/	"fsi-data","","","","","","","",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","","","","","",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","checkstop","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"presence-ps1","","led-rear-fault","led-rear-power",
		        "led-rear-id","","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"","","","","","","","presence-ps0",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"","","fsi-trans","","","power-button","","",
	/*S0-S7*/	"","","","","","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","",
	/*AA0-AA7*/	"fsi-clock","","","","","","","",
	/*AB0-AB7*/	"","","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";
};

&fmc {
	status = "okay";

	flash@0 {
		status = "okay";
		label = "bmc";
		m25p,fast-read;
		spi-max-frequency = <50000000>;

		partitions {
			#address-cells = < 1 >;
			#size-cells = < 1 >;
			compatible = "fixed-partitions";
			u-boot@0 {
				reg = < 0 0x60000 >;
				label = "u-boot";
			};
			u-boot-env@60000 {
				reg = < 0x60000 0x20000 >;
				label = "u-boot-env";
			};
			obmc-ubi@80000 {
				reg = < 0x80000 0x1F80000 >;
				label = "obmc-ubi";
			};
		};
	};

	flash@1 {
		status = "okay";
		label = "alt-bmc";
		m25p,fast-read;
		spi-max-frequency = <50000000>;

		partitions {
			#address-cells = < 1 >;
			#size-cells = < 1 >;
			compatible = "fixed-partitions";
			u-boot@0 {
				reg = < 0 0x60000 >;
				label = "alt-u-boot";
			};
			u-boot-env@60000 {
				reg = < 0x60000 0x20000 >;
				label = "alt-u-boot-env";
			};
			obmc-ubi@80000 {
				reg = < 0x80000 0x1F80000 >;
				label = "alt-obmc-ubi";
			};
		};
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		label = "pnor";
		m25p,fast-read;
		spi-max-frequency = <100000000>;
	};
};

&uart1 {
	/* Rear RS-232 connector */
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default
			&pinctrl_nrts1_default
			&pinctrl_ndtr1_default
			&pinctrl_ndsr1_default
			&pinctrl_ncts1_default
			&pinctrl_ndcd1_default
			&pinctrl_nri1_default>;
};

&uart2 {
	/* APSS */
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default &pinctrl_rxd2_default>;
};

&uart5 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&i2c2 {
	status = "okay";

	/* MUX ->
	 *    Samtec 1
	 *    Samtec 2
	 */
};

&i2c3 {
	status = "okay";

	bmp: bmp280@77 {
		compatible = "bosch,bmp280";
		reg = <0x77>;
		#io-channel-cells = <1>;
	};

	max31785@52 {
		compatible = "maxim,max31785a";
		reg = <0x52>;
		#address-cells = <1>;
		#size-cells = <0>;
	};

	dps: dps310@76 {
		compatible = "infineon,dps310";
		reg = <0x76>;
		#io-channel-cells = <0>;
	};

	pca0: pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio@0 {
			reg = <0>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@1 {
			reg = <1>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@2 {
			reg = <2>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@3 {
			reg = <3>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@4 {
			reg = <4>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@5 {
			reg = <5>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@6 {
			reg = <6>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@7 {
			reg = <7>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@8 {
			reg = <8>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@9 {
			reg = <9>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@10 {
			reg = <10>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@11 {
			reg = <11>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@12 {
			reg = <12>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@13 {
			reg = <13>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@14 {
			reg = <14>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@15 {
			reg = <15>;
			type = <PCA955X_TYPE_GPIO>;
		};
	};

	power-supply@68 {
		compatible = "ibm,cffps1";
		reg = <0x68>;
	};

	power-supply@69 {
		compatible = "ibm,cffps1";
		reg = <0x69>;
	};
};

&i2c4 {
	status = "okay";

	tmp423a@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	ir35221@70 {
		compatible = "infineon,ir35221";
		reg = <0x70>;
	};

	ir35221@71 {
		compatible = "infineon,ir35221";
		reg = <0x71>;
	};
};


&i2c5 {
	status = "okay";

	tmp423a@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	ir35221@70 {
		compatible = "infineon,ir35221";
		reg = <0x70>;
	};

	ir35221@71 {
		compatible = "infineon,ir35221";
		reg = <0x71>;
	};
};

&i2c9 {
	status = "okay";

	tmp275@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};
};

&i2c10 {
	/* MUX
	 *   -> PCIe Slot 3
	 *   -> PCIe Slot 4
	 */
	status = "okay";
};

&i2c11 {
	status = "okay";

	pca9552: pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names = "PS_SMBUS_RESET_N", "APSS_RESET_N",
			"GPU0_TH_OVERT_N_BUFF",	"GPU1_TH_OVERT_N_BUFF",
			"GPU2_TH_OVERT_N_BUFF", "GPU3_TH_OVERT_N_BUFF",
			"GPU4_TH_OVERT_N_BUFF",	"GPU5_TH_OVERT_N_BUFF",
			"GPU0_PWR_GOOD_BUFF", "GPU1_PWR_GOOD_BUFF",
			"GPU2_PWR_GOOD_BUFF", "GPU3_PWR_GOOD_BUFF",
			"GPU4_PWR_GOOD_BUFF", "GPU5_PWR_GOOD_BUFF",
			"12V_BREAKER_FLT_N", "THROTTLE_UNLATCHED_N";

		gpio@0 {
			reg = <0>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@1 {
			reg = <1>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@2 {
			reg = <2>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@3 {
			reg = <3>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@4 {
			reg = <4>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@5 {
			reg = <5>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@6 {
			reg = <6>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@7 {
			reg = <7>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@8 {
			reg = <8>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@9 {
			reg = <9>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@10 {
			reg = <10>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@11 {
			reg = <11>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@12 {
			reg = <12>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@13 {
			reg = <13>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@14 {
			reg = <14>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@15 {
			reg = <15>;
			type = <PCA955X_TYPE_GPIO>;
		};
	};

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	ucd90160@64 {
		compatible = "ti,ucd90160";
		reg = <0x64>;
	};
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&gfx {
	status = "okay";
	memory-region = <&gfx_memory>;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	aspeed,alt-boot;
};

&ibt {
	status = "okay";
};

&adc {
	status = "okay";
};

&vhub {
	status = "okay";
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

#include "ibm-power9-dual.dtsi"
