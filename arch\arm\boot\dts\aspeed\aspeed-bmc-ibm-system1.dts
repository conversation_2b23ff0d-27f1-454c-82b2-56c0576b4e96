// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2023 IBM Corp.
/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "System1";
	compatible = "ibm,system1-bmc", "aspeed,ast2600";

	aliases {
		i2c16 = &i2c8mux1chn0;
		i2c17 = &i2c8mux1chn1;
		i2c18 = &i2c8mux1chn2;
		i2c19 = &i2c8mux1chn3;
		i2c20 = &i2c8mux1chn4;
		i2c21 = &i2c8mux1chn5;
		i2c22 = &i2c8mux1chn6;
		i2c23 = &i2c8mux1chn7;
		i2c24 = &i2c3mux0chn0;
		i2c25 = &i2c3mux0chn1;
		i2c26 = &i2c3mux0chn2;
		i2c27 = &i2c3mux0chn3;
		i2c28 = &i2c3mux0chn4;
		i2c29 = &i2c3mux0chn5;
		i2c30 = &i2c3mux0chn6;
		i2c31 = &i2c3mux0chn7;
		i2c32 = &i2c6mux0chn0;
		i2c33 = &i2c6mux0chn1;
		i2c34 = &i2c6mux0chn2;
		i2c35 = &i2c6mux0chn3;
		i2c36 = &i2c6mux0chn4;
		i2c37 = &i2c6mux0chn5;
		i2c38 = &i2c6mux0chn6;
		i2c39 = &i2c6mux0chn7;
		i2c40 = &i2c7mux0chn0;
		i2c41 = &i2c7mux0chn1;
		i2c42 = &i2c7mux0chn2;
		i2c43 = &i2c7mux0chn3;
		i2c44 = &i2c7mux0chn4;
		i2c45 = &i2c7mux0chn5;
		i2c46 = &i2c7mux0chn6;
		i2c47 = &i2c7mux0chn7;
		i2c48 = &i2c8mux0chn0;
		i2c49 = &i2c8mux0chn1;
		i2c50 = &i2c8mux0chn2;
		i2c51 = &i2c8mux0chn3;
		i2c52 = &i2c8mux0chn4;
		i2c53 = &i2c8mux0chn5;
		i2c54 = &i2c8mux0chn6;
		i2c55 = &i2c8mux0chn7;
		i2c56 = &i2c14mux0chn0;
		i2c57 = &i2c14mux0chn1;
		i2c58 = &i2c14mux0chn2;
		i2c59 = &i2c14mux0chn3;
		i2c60 = &i2c14mux0chn4;
		i2c61 = &i2c14mux0chn5;
		i2c62 = &i2c14mux0chn6;
		i2c63 = &i2c14mux0chn7;
		i2c64 = &i2c15mux0chn0;
		i2c65 = &i2c15mux0chn1;
		i2c66 = &i2c15mux0chn2;
		i2c67 = &i2c15mux0chn3;
		i2c68 = &i2c15mux0chn4;
		i2c69 = &i2c15mux0chn5;
		i2c70 = &i2c15mux0chn6;
		i2c71 = &i2c15mux0chn7;
	};

	chosen {
		stdout-path = "uart5:115200n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		eventlog: tcg-event-log@b3d00000 {
			no-map;
			reg = <0xb3d00000 0x100000>;
		};

		ramoops@b3e00000 {
			compatible = "ramoops";
			reg = <0xb3e00000 0x200000>; /* 16 * (4 * 0x8000) */
			record-size = <0x8000>;
			console-size = <0x8000>;
			ftrace-size = <0x8000>;
			pmsg-size = <0x8000>;
			max-reason = <3>; /* KMSG_DUMP_EMERG */
		};

		/* LPC FW cycle bridge region requires natural alignment */
		flash_memory: region@b4000000 {
			no-map;
			reg = <0xb4000000 0x04000000>; /* 64M */
		};

		/* VGA region is dictated by hardware strapping */
		vga_memory: region@bf000000 {
			no-map;
			compatible = "shared-dma-pool";
			reg = <0xbf000000 0x01000000>;  /* 16M */
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			gpios = <&gpio0 ASPEED_GPIO(L, 7) GPIO_ACTIVE_HIGH>;
		};

		led-1 {
			gpios = <&gpio0 ASPEED_GPIO(P, 7) GPIO_ACTIVE_HIGH>;
		};

		led-2 {
			gpios = <&gpio0 ASPEED_GPIO(S, 6) GPIO_ACTIVE_HIGH>;
		};

		led-3 {
			gpios = <&gpio0 ASPEED_GPIO(S, 7) GPIO_ACTIVE_HIGH>;
		};

		led-4 {
			gpios = <&pca3 5 GPIO_ACTIVE_LOW>;
		};

		led-5 {
			gpios = <&pca3 6 GPIO_ACTIVE_LOW>;
		};

		led-6 {
			gpios = <&pca3 7 GPIO_ACTIVE_LOW>;
		};

		led-7 {
			gpios = <&pca3 8 GPIO_ACTIVE_LOW>;
		};

		led-8 {
			gpios = <&pca3 9 GPIO_ACTIVE_LOW>;
		};

		led-9 {
			gpios = <&pca3 10 GPIO_ACTIVE_LOW>;
		};

		led-a {
			gpios = <&pca3 11 GPIO_ACTIVE_LOW>;
		};

		led-b {
			gpios = <&pca4 4 GPIO_ACTIVE_HIGH>;
		};

		led-c {
			gpios = <&pca4 5 GPIO_ACTIVE_HIGH>;
		};

		led-d {
			gpios = <&pca4 6 GPIO_ACTIVE_HIGH>;
		};

		led-e {
			gpios = <&pca4 7 GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-nvme0-presence {
			label = "nvme0-presence";
			gpios = <&pca4 0 GPIO_ACTIVE_LOW>;
			linux,code = <0>;
		};

		event-nvme1-presence {
			label = "nvme1-presence";
			gpios = <&pca4 1 GPIO_ACTIVE_LOW>;
			linux,code = <1>;
		};

		event-nvme2-presence {
			label = "nvme2-presence";
			gpios = <&pca4 2 GPIO_ACTIVE_LOW>;
			linux,code = <2>;
		};

		event-nvme3-presence {
			label = "nvme3-presence";
			gpios = <&pca4 3 GPIO_ACTIVE_LOW>;
			linux,code = <3>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&p12v_vd 0>, <&p5v_aux_vd 0>,
			<&p5v_bmc_aux_vd 0>, <&p3v3_aux_vd 0>,
			<&p3v3_bmc_aux_vd 0>, <&p1v8_bmc_aux_vd 0>,
			<&adc1 4>, <&adc0 2>, <&adc1 0>,
			<&p2v5_aux_vd 0>, <&adc1 7>;
	};

	p12v_vd: voltage-divider1 {
		compatible = "voltage-divider";
		io-channels = <&adc1 3>;
		#io-channel-cells = <1>;

		/*
		 * Scale the system voltage by 1127/127 to fit the ADC range.
		 * Use small nominator to prevent integer overflow.
		 */
		output-ohms = <15>;
		full-ohms = <133>;
	};

	p5v_aux_vd: voltage-divider2 {
		compatible = "voltage-divider";
		io-channels = <&adc1 5>;
		#io-channel-cells = <1>;

		/*
		 * Scale the system voltage by 1365/365 to fit the ADC range.
		 * Use small nominator to prevent integer overflow.
		 */
		output-ohms = <50>;
		full-ohms = <187>;
	};

	p5v_bmc_aux_vd: voltage-divider3 {
		compatible = "voltage-divider";
		io-channels = <&adc0 3>;
		#io-channel-cells = <1>;

		/*
		 * Scale the system voltage by 1365/365 to fit the ADC range.
		 * Use small nominator to prevent integer overflow.
		 */
		output-ohms = <50>;
		full-ohms = <187>;
	};

	p3v3_aux_vd: voltage-divider4 {
		compatible = "voltage-divider";
		io-channels = <&adc1 2>;
		#io-channel-cells = <1>;

		/*
		 * Scale the system voltage by 1698/698 to fit the ADC range.
		 * Use small nominator to prevent integer overflow.
		 */
		output-ohms = <14>;
		full-ohms = <34>;
	};

	p3v3_bmc_aux_vd: voltage-divider5 {
		compatible = "voltage-divider";
		io-channels = <&adc0 7>;
		#io-channel-cells = <1>;

		/*
		 * Scale the system voltage by 1698/698 to fit the ADC range.
		 * Use small nominator to prevent integer overflow.
		 */
		output-ohms = <14>;
		full-ohms = <34>;
	};

	p1v8_bmc_aux_vd: voltage-divider6 {
		compatible = "voltage-divider";
		io-channels = <&adc0 6>;
		#io-channel-cells = <1>;

		/*
		 * Scale the system voltage by 4000/3000 to fit the ADC range.
		 * Use small nominator to prevent integer overflow.
		 */
		output-ohms = <3>;
		full-ohms = <4>;
	};

	p2v5_aux_vd: voltage-divider7 {
		compatible = "voltage-divider";
		io-channels = <&adc1 1>;
		#io-channel-cells = <1>;

		/*
		 * Scale the system voltage by 2100/1100 to fit the ADC range.
		 * Use small nominator to prevent integer overflow.
		 */
		output-ohms = <11>;
		full-ohms = <21>;
	};

	p1v8_bmc_aux: fixedregulator-p1v8-bmc-aux {
		compatible = "regulator-fixed";
		regulator-name = "p1v8_bmc_aux";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};
};

&adc0 {
	status = "okay";
	vref-supply = <&p1v8_bmc_aux>;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
		&pinctrl_adc1_default
		&pinctrl_adc2_default
		&pinctrl_adc3_default
		&pinctrl_adc4_default
		&pinctrl_adc5_default
		&pinctrl_adc6_default
		&pinctrl_adc7_default>;
};

&adc1 {
	status = "okay";
	vref-supply = <&p1v8_bmc_aux>;
	aspeed,battery-sensing;

	aspeed,int-vref-microvolt = <2500000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc8_default
		&pinctrl_adc9_default
		&pinctrl_adc10_default
		&pinctrl_adc11_default
		&pinctrl_adc12_default
		&pinctrl_adc13_default
		&pinctrl_adc14_default
		&pinctrl_adc15_default>;
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"","","","","bmc-tpm-reset","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","","","","","",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","bmc-ready",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"fpga-debug-enable","","","","","","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"","","","","","","","bmc-hb",
	/*Q0-Q7*/	"","","","","","","pch-ready","",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","","","","rear-enc-fault0","rear-enc-id0",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","rtc-battery-voltage-read-enable","","power-chassis-control","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"fpga-pgood","power-chassis-good","pch-pgood","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","";
};

&emmc_controller {
	status = "okay";
};

&pinctrl_emmc_default {
	bias-disable;
};

&emmc {
	status = "okay";
	clk-phase-mmc-hs200 = <180>, <180>;
};

&sgpiom0 {
	status = "okay";
	ngpios = <128>;
	bus-frequency = <1000000>;
};

&ibt {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&vuart1 {
	status = "okay";
};

&vuart2 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC3CLK>,
		 <&syscon ASPEED_CLK_MAC3RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC4CLK>,
		 <&syscon ASPEED_CLK_MAC4RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	status = "okay";
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8 0xcac>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
	aspeed,lpc-interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
};

&peci0 {
	status = "okay";
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>, <0x81>;
};

&i2c0 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	regulator@60 {
		compatible = "maxim,max8952";
		reg = <0x60>;

		max8952,default-mode = <0>;
		max8952,dvs-mode-microvolt = <1250000>, <1200000>,
						<1050000>, <950000>;
		max8952,sync-freq = <0>;
		max8952,ramp-speed = <0>;

		regulator-name = "VR_v77_1v4";
		regulator-min-microvolt = <770000>;
		regulator-max-microvolt = <1400000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&i2c1 {
	status = "okay";

	regulator@42 {
		compatible = "infineon,ir38263";
		reg = <0x42>;
	};

	led-controller@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "nic1-perst";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "bmc-perst";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "reset-M2-SSD1-2-perst";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "pcie-perst1";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "pcie-perst2";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "pcie-perst3";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "pcie-perst4";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "pcie-perst5";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "pcie-perst6";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "pcie-perst7";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "pcie-perst8";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "PV-cp0-sw1stk4-perst";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "PV-cp0-sw1stk5-perst";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "pe-cp-drv0-perst";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "pe-cp-drv1-perst";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "lom-perst";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	gpio@74 {
		compatible = "nxp,pca9539";
		reg = <0x74>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"PLUG_DETECT_PCIE_J101_N",
			"PLUG_DETECT_PCIE_J102_N",
			"PLUG_DETECT_PCIE_J103_N",
			"PLUG_DETECT_PCIE_J104_N",
			"PLUG_DETECT_PCIE_J105_N",
			"PLUG_DETECT_PCIE_J106_N",
			"PLUG_DETECT_PCIE_J107_N",
			"PLUG_DETECT_PCIE_J108_N",
			"PLUG_DETECT_M2_SSD1_N",
			"PLUG_DETECT_NIC1_N",
			"SEL_SMB_DIMM_CPU0",
			"presence-ps2",
			"presence-ps3",
			"", "",
			"PWRBRD_PLUG_DETECT2_N";
	};
};

&i2c2 {
	status = "okay";

	power-supply@58 {
		compatible = "intel,crps185";
		reg = <0x58>;
	};

	power-supply@59 {
		compatible = "intel,crps185";
		reg = <0x59>;
	};

	power-supply@5a {
		compatible = "intel,crps185";
		reg = <0x5a>;
	};

	power-supply@5b {
		compatible = "intel,crps185";
		reg = <0x5b>;
	};
};

&i2c3 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c3mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c3mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c3mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c3mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		i2c3mux0chn4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		i2c3mux0chn5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		i2c3mux0chn6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		i2c3mux0chn7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";

	regulator@42 {
		compatible = "infineon,ir38263";
		reg = <0x42>;
	};

	regulator@43 {
		compatible = "infineon,ir38060";
		reg = <0x43>;
	};
};

&i2c6 {
	status = "okay";

	fan-controller@52 {
		compatible = "maxim,max31785a";
		reg = <0x52>;
	};

	fan-controller@54 {
		compatible = "maxim,max31785a";
		reg = <0x54>;
	};

	eeprom@55 {
		compatible = "atmel,24c64";
		reg = <0x55>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c6mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c6mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c6mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c6mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		i2c6mux0chn4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;

			humidity-sensor@40 {
				compatible = "ti,hdc1080";
				reg = <0x40>;
			};

			temperature-sensor@48 {
				compatible = "ti,tmp275";
				reg = <0x48>;
			};

			eeprom@50 {
				compatible = "atmel,24c32";
				reg = <0x50>;
			};

			led-controller@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "enclosure-id-led";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "attention-led";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@2 {
					label = "enclosure-fault-rollup-led";
					reg = <2>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@3 {
					label = "power-on-led";
					reg = <3>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};

			temperature-sensor@76 {
				compatible = "infineon,dps310";
				reg = <0x76>;
			};
		};

		i2c6mux0chn5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		i2c6mux0chn6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		i2c6mux0chn7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};

	pca3: gpio@74 {
		compatible = "nxp,pca9539";
		reg = <0x74>;

		gpio-controller;
		#gpio-cells = <2>;
	};

	pca4: gpio@77 {
		compatible = "nxp,pca9539";
		reg = <0x77>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"PE_NVMED0_EXP_PRSNT_N",
			"PE_NVMED1_EXP_PRSNT_N",
			"PE_NVMED2_EXP_PRSNT_N",
			"PE_NVMED3_EXP_PRSNT_N",
			"LED_FAULT_NVMED0",
			"LED_FAULT_NVMED1",
			"LED_FAULT_NVMED2",
			"LED_FAULT_NVMED3",
			"FAN0_PRESENCE_R_N",
			"FAN1_PRESENCE_R_N",
			"FAN2_PRESENCE_R_N",
			"FAN3_PRESENCE_R_N",
			"FAN4_PRESENCE_R_N",
			"FAN5_PRESENCE_N",
			"FAN6_PRESENCE_N",
			"";
	};
};

&i2c7 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c7mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c7mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c7mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c7mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			regulator@58 {
				compatible = "mps,mp2973";
				reg = <0x58>;
			};
		};

		i2c7mux0chn4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		i2c7mux0chn5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;

			regulator@40 {
				compatible = "infineon,tda38640";
				reg = <0x40>;
			};
		};

		i2c7mux0chn6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		i2c7mux0chn7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c8 {
	status = "okay";
	bus-frequency = <400000>;

	i2c-mux@71 {
		compatible = "nxp,pca9548";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c8mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			regulator@58 {
				compatible = "mps,mp2971";
				reg = <0x58>;
			};
		};

		i2c8mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			regulator@40 {
				compatible = "infineon,tda38640";
				reg = <0x40>;
			};

			regulator@41 {
				compatible = "infineon,tda38640";
				reg = <0x41>;
			};

			regulator@58 {
				compatible = "mps,mp2971";
				reg = <0x58>;
			};

			regulator@5b {
				compatible = "mps,mp2971";
				reg = <0x5b>;
			};
		};

		i2c8mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c8mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		i2c8mux0chn4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;

			i2c-mux@70 {
				compatible = "nxp,pca9548";
				reg = <0x70>;
				#address-cells = <1>;
				#size-cells = <0>;
				i2c-mux-idle-disconnect;

				i2c8mux1chn0: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				i2c8mux1chn1: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				i2c8mux1chn2: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				i2c8mux1chn3: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				i2c8mux1chn4: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				i2c8mux1chn5: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				i2c8mux1chn6: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				i2c8mux1chn7: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		i2c8mux0chn5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		i2c8mux0chn6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;

			temperature-sensor@4c {
				compatible = "ti,tmp432";
				reg = <0x4c>;
			};
		};

		i2c8mux0chn7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;

			regulator@40 {
				compatible = "infineon,ir38060";
				reg = <0x40>;
			};
		};
	};
};

&i2c9 {
	status = "okay";

	regulator@40 {
		compatible = "infineon,ir38263";
		reg = <0x40>;
	};

	regulator@41 {
		compatible = "infineon,ir38263";
		reg = <0x41>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	regulator@60 {
		compatible = "maxim,max8952";
		reg = <0x60>;

		max8952,default-mode = <0>;
		max8952,dvs-mode-microvolt = <1250000>, <1200000>,
						<1050000>, <950000>;
		max8952,sync-freq = <0>;
		max8952,ramp-speed = <0>;

		regulator-name = "VR_v77_1v4";
		regulator-min-microvolt = <770000>;
		regulator-max-microvolt = <1400000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&i2c11 {
	status = "okay";

	tpm@2e {
		compatible = "tcg,tpm-tis-i2c";
		reg = <0x2e>;
		memory-region = <&eventlog>;
	};
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";

	regulator@41 {
		compatible = "infineon,ir38263";
		reg = <0x41>;
	};

	led-controller@61 {
		compatible = "nxp,pca9552";
		reg = <0x61>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "efuse-12v-slots";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "efuse-3p3v-slot";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "nic2-pert";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "pcie-perst9";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "pcie-perst10";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "pcie-perst11";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "pcie-perst12";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "pcie-perst13";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "pcie-perst14";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "pcie-perst15";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "pcie-perst16";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "PV-cp1-sw1stk4-perst";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "PV-cp1-sw1stk5-perst";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "pe-cp-drv2-perst";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "pe-cp-drv3-perst";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	gpio@75 {
		compatible = "nxp,pca9539";
		reg = <0x75>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"PLUG_DETECT_PCIE_J109_N",
			"PLUG_DETECT_PCIE_J110_N",
			"PLUG_DETECT_PCIE_J111_N",
			"PLUG_DETECT_PCIE_J112_N",
			"PLUG_DETECT_PCIE_J113_N",
			"PLUG_DETECT_PCIE_J114_N",
			"PLUG_DETECT_PCIE_J115_N",
			"PLUG_DETECT_PCIE_J116_N",
			"PLUG_DETECT_M2_SSD2_N",
			"PLUG_DETECT_NIC2_N",
			"SEL_SMB_DIMM_CPU1",
			"presence-ps0",
			"presence-ps1",
			"", "",
			"PWRBRD_PLUG_DETECT1_N";
	};

	gpio@76 {
		compatible = "nxp,pca9539";
		reg = <0x76>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"SW1_BOOTRCVRYB1_N",
			"SW1_BOOTRCVRYB0_N",
			"SW2_BOOTRCVRYB1_N",
			"SW2_BOOTRCVRYB0_N",
			"SW3_4_BOOTRCVRYB1_N",
			"SW3_4_BOOTRCVRYB0_N",
			"SW5_BOOTRCVRYB1_N",
			"SW5_BOOTRCVRYB0_N",
			"SW6_BOOTRCVRYB1_N",
			"SW6_BOOTRCVRYB0_N",
			"SW1_RESET_N",
			"SW3_RESET_N",
			"SW4_RESET_N",
			"SW2_RESET_N",
			"SW5_RESET_N",
			"SW6_RESET_N";
	};
};

&i2c14 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c14mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c14mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c14mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c14mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			regulator@58 {
				compatible = "mps,mp2973";
				reg = <0x58>;
			};
		};

		i2c14mux0chn4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		i2c14mux0chn5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;

			regulator@40 {
				compatible = "infineon,tda38640";
				reg = <0x40>;
			};
		};

		i2c14mux0chn6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		i2c14mux0chn7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c15 {
	status = "okay";
	bus-frequency = <400000>;

	i2c-mux@71 {
		compatible = "nxp,pca9548";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c15mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			regulator@58 {
				compatible = "mps,mp2971";
				reg = <0x58>;
			};
		};

		i2c15mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			regulator@40 {
				compatible = "infineon,tda38640";
				reg = <0x40>;
			};

			regulator@41 {
				compatible = "infineon,tda38640";
				reg = <0x41>;
			};

			regulator@58 {
				compatible = "mps,mp2971";
				reg = <0x58>;
			};

			regulator@5b {
				compatible = "mps,mp2971";
				reg = <0x5b>;
			};
		};

		i2c15mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c15mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		i2c15mux0chn4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;

			i2c-mux@70 {
				compatible = "nxp,pca9548";
				reg = <0x70>;
				#address-cells = <1>;
				#size-cells = <0>;
				i2c-mux-idle-disconnect;

				i2c15mux1chn0: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				i2c15mux1chn1: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				i2c15mux1chn2: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				i2c15mux1chn3: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				i2c15mux1chn4: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				i2c15mux1chn5: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				i2c15mux1chn6: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				i2c15mux1chn7: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		i2c15mux0chn5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		i2c15mux0chn6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;

			temperature-sensor@4c {
				compatible = "ti,tmp432";
				reg = <0x4c>;
			};
		};

		i2c15mux0chn7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;

			regulator@40 {
				compatible = "infineon,ir38060";
				reg = <0x40>;
			};

			temperature-sensor@4c {
				compatible = "ti,tmp432";
				reg = <0x4c>;
			};
		};
	};
};
