// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2018 Facebook Inc.
/dts-v1/;

#include "ast2500-facebook-netbmc-common.dtsi"

/ {
	model = "Facebook YAMP 100 BMC";
	compatible = "facebook,yamp-bmc", "aspeed,ast2500";

	aliases {
		/*
		 * Override the default uart aliases to avoid breaking
		 * the legacy applications.
		 */
		serial0 = &uart5;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS0,9600n8 root=/dev/ram rw";
	};
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default
		     &pinctrl_rxd2_default>;
};

&mac0 {
	status = "okay";
	use-ncsi;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
};

&mac1 {
	status = "disabled";
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	i2c-mux@75 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x75>;
	};
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&fmc_flash0 {
#include "facebook-bmc-flash-layout.dtsi"
};

&fmc_flash1 {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		flash1@0 {
			reg = <0x0 0x2000000>;
			label = "flash1";
		};
	};
};
