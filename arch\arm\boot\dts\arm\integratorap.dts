// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree for the ARM Integrator/AP platform
 */

/dts-v1/;
#include "integrator.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "ARM Integrator/AP";
	compatible = "arm,integrator-ap";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			/*
			 * Since the board has pluggable CPU modules, we
			 * cannot define a proper compatible here. Let the
			 * boot loader fill in the apropriate compatible
			 * string if necessary.
			 */
			/* compatible = "arm,arm926ej-s"; */
			reg = <0>;
			/*
			 * The documentation in ARM DUI 0138E page 3-12 states
			 * that the maximum frequency for this clock is 200 MHz
			 * but painful trial-and-error has proved to me that it
			 * is actually just hanging the system above 71 MHz.
			 * Sad but true.
			 */
					 /* kHz     uV   */
			operating-points = <71000  0
					    66000  0
					    60000  0
					    48000  0
					    36000  0
					    24000  0
					    12000  0>;
			clocks = <&cmosc>;
			clock-names = "cpu";
			clock-latency = <1000000>; /* 1 ms */
		};
	};

	aliases {
		arm,timer-primary = &timer2;
		arm,timer-secondary = &timer1;
	};

	chosen {
		bootargs = "root=/dev/ram0 console=ttyAM0,38400n8 earlyprintk";
	};

	/* 24 MHz chrystal on the Integrator/AP development board */
	xtal24mhz: pclk: clock-24000000 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
	};

	/* The UART clock is 14.74 MHz divided by an ICS525 */
	uartclk: clock-14745600 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <14745600>;
		clocks = <&xtal24mhz>;
	};

	core-module@10000000 {
		/* 24 MHz chrystal on the core module */
		cm24mhz: clock-24000000 {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
		};

		/* Oscillator on the core module, clocks the CPU core */
		cmosc: clock-controller@8 {
			compatible = "arm,syscon-icst525-integratorap-cm";
			reg = <0x08 0x04>;
			#clock-cells = <0>;
			lock-offset = <0x14>;
			vco-offset = <0x08>;
			clocks = <&cm24mhz>;
		};

		/* Auxilary oscillator on the core module, 32.369MHz at boot */
		auxosc: clock-controller@1c {
			compatible = "arm,syscon-icst525";
			reg = <0x1c 0x04>;
			#clock-cells = <0>;
			lock-offset = <0x14>;
			vco-offset = <0x1c>;
			clocks = <&cm24mhz>;
		};
	};

	syscon {
		compatible = "arm,integrator-ap-syscon", "syscon";
		reg = <0x11000000 0x100>;
		ranges = <0x0 0x11000000 0x100>;
		#size-cells = <1>;
		#address-cells = <1>;

		/*
		 * SYSCLK clocks PCIv3 bridge, system controller and the
		 * logic modules.
		 */
		sysclk: clock-controller@4 {
			compatible = "arm,syscon-icst525-integratorap-sys";
			reg = <0x04 0x04>;
			#clock-cells = <0>;
			lock-offset = <0x1c>;
			vco-offset = <0x04>;
			clocks = <&xtal24mhz>;
		};

		/* One-bit control for the PCI bus clock (33 or 25 MHz) */
		pciclk: clock-controller@4,8 {
			compatible = "arm,syscon-icst525-integratorap-pci";
			reg = <0x04 0x04>;
			#clock-cells = <0>;
			lock-offset = <0x1c>;
			vco-offset = <0x04>;
			clocks = <&xtal24mhz>;
		};
	};

	timer0: timer@13000000 {
		compatible = "arm,integrator-timer";
		clocks = <&xtal24mhz>;
	};

	timer1: timer@13000100 {
		compatible = "arm,integrator-timer";
		clocks = <&xtal24mhz>;
	};

	timer2: timer@13000200 {
		compatible = "arm,integrator-timer";
		clocks = <&xtal24mhz>;
	};

	pic: pic@14000000 {
		valid-mask = <0x003fffff>;
	};

	pci: pci@62000000 {
		compatible = "arm,integrator-ap-pci", "v3,v360epc-pci";
		device_type = "pci";
		#interrupt-cells = <1>;
		#size-cells = <2>;
		#address-cells = <3>;
		/* Bridge registers and config access space */
		reg = <0x62000000 0x10000>, <0x61000000 0x01000000>;
		interrupt-parent = <&pic>;
		interrupts = <17>; /* Bus error IRQ */
		clocks = <&pciclk>;
		bus-range = <0x00 0xff>;
		ranges = <0x01000000 0 0x0000000 /* I/O space @00000000 */
			0x60000000 0 0x00010000 /* 64 KB @ LB 60000000 */
			0x02000000 0 0x40000000 /* non-prefectable memory @40000000 */
			0x40000000 0 0x10000000 /* 256 MiB @ LB 40000000 1:1 */
			0x42000000 0 0x50000000 /* prefetchable memory @50000000 */
			0x50000000 0 0x10000000>; /* 256 MiB @ LB 50000000 1:1 */
		dma-ranges = <0x02000000 0 0x20000000 /* EBI memory space */
			0x20000000 0 0x20000000 /* 512 MB @ LB 20000000 1:1 */
			0x02000000 0 0x80000000 /* Core module alias memory */
			0x80000000 0 0x40000000>; /* 1GB @ LB 80000000 */
		interrupt-map-mask = <0xf800 0 0 0x7>;
		interrupt-map = <
		/* IDSEL 9 */
		0x4800 0 0 1 &pic 13 /* INT A on slot 9 is irq 13 */
		0x4800 0 0 2 &pic 14 /* INT B on slot 9 is irq 14 */
		0x4800 0 0 3 &pic 15 /* INT C on slot 9 is irq 15 */
		0x4800 0 0 4 &pic 16 /* INT D on slot 9 is irq 16 */
		/* IDSEL 10 */
		0x5000 0 0 1 &pic 14 /* INT A on slot 10 is irq 14 */
		0x5000 0 0 2 &pic 15 /* INT B on slot 10 is irq 15 */
		0x5000 0 0 3 &pic 16 /* INT C on slot 10 is irq 16 */
		0x5000 0 0 4 &pic 13 /* INT D on slot 10 is irq 13 */
		/* IDSEL 11 */
		0x5800 0 0 1 &pic 15 /* INT A on slot 11 is irq 15 */
		0x5800 0 0 2 &pic 16 /* INT B on slot 11 is irq 16 */
		0x5800 0 0 3 &pic 13 /* INT C on slot 11 is irq 13 */
		0x5800 0 0 4 &pic 14 /* INT D on slot 11 is irq 14 */
		/* IDSEL 12 */
		0x6000 0 0 1 &pic 16 /* INT A on slot 12 is irq 16 */
		0x6000 0 0 2 &pic 13 /* INT B on slot 12 is irq 13 */
		0x6000 0 0 3 &pic 14 /* INT C on slot 12 is irq 14 */
		0x6000 0 0 4 &pic 15 /* INT D on slot 12 is irq 15 */
		>;
	};

	fpga {
		/*
		 * The Integator/AP predates the idea to have magic numbers
		 * identifying the PrimeCell in hardware, thus we have to
		 * supply these from the device tree.
		 */
		rtc: rtc@15000000 {
			compatible = "arm,pl030", "arm,primecell";
			arm,primecell-periphid = <0x00041030>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		uart0: serial@16000000 {
			compatible = "arm,pl010", "arm,primecell";
			arm,primecell-periphid = <0x00041010>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		uart1: serial@17000000 {
			compatible = "arm,pl010", "arm,primecell";
			arm,primecell-periphid = <0x00041010>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		kmi0: kmi@18000000 {
			compatible = "arm,pl050", "arm,primecell";
			arm,primecell-periphid = <0x00041050>;
			clocks = <&xtal24mhz>, <&pclk>;
			clock-names = "KMIREFCLK", "apb_pclk";
		};

		kmi1: kmi@19000000 {
			compatible = "arm,pl050", "arm,primecell";
			arm,primecell-periphid = <0x00041050>;
			clocks = <&xtal24mhz>, <&pclk>;
			clock-names = "KMIREFCLK", "apb_pclk";
		};
	};

	/*
	 * Logic module bus, we support up to 4 logical modules
	 * They appear at 0xc0000000, 0xd0000000, 0xe0000000 and 0xf0000000
	 * and use interrupts 9, 10, 11 and 12 respectively.
	 */
	bus@c0000000 {
		compatible = "arm,integrator-ap-lm";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0xc0000000 0xc0000000 0x40000000>;
		dma-ranges;

		lm0: bus@c0000000 {
			compatible = "simple-bus";
			ranges = <0x00000000 0xc0000000 0x10000000>;
			dma-ranges = <0x00000000 0xc0000000 0x10000000>;
			reg = <0xc0000000 0x10000000>;
			#address-cells = <1>;
			#size-cells = <1>;
		};
		lm1: bus@d0000000 {
			compatible = "simple-bus";
			ranges = <0x00000000 0xd0000000 0x10000000>;
			dma-ranges = <0x00000000 0xd0000000 0x10000000>;
			reg = <0xd0000000 0x10000000>;
			#address-cells = <1>;
			#size-cells = <1>;
		};
		lm2: bus@e0000000 {
			compatible = "simple-bus";
			ranges = <0x00000000 0xe0000000 0x10000000>;
			dma-ranges = <0x00000000 0xe0000000 0x10000000>;
			reg = <0xe0000000 0x10000000>;
			#address-cells = <1>;
			#size-cells = <1>;
		};
		lm3: bus@f0000000 {
			compatible = "simple-bus";
			ranges = <0x00000000 0xf0000000 0x10000000>;
			dma-ranges = <0x00000000 0xf0000000 0x10000000>;
			reg = <0xf0000000 0x10000000>;
			#address-cells = <1>;
			#size-cells = <1>;
		};
	};
};
