/*
 * Copyright 2014 Linaro Ltd
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

/dts-v1/;
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	model = "ARM RealView PB1176";
	compatible = "arm,realview-pb1176";

	chosen { };

	aliases {
		serial0 = &pb1176_serial0;
		serial1 = &pb1176_serial1;
		serial2 = &pb1176_serial2;
		serial3 = &pb1176_serial3;
		serial4 = &fpga_serial;
	};

	memory {
		device_type = "memory";
		/* 128 MiB memory @ 0x0 */
		reg = <0x00000000 0x08000000>;
	};

	/* The voltage to the MMC card is hardwired at 3.3V */
	vmmc: regulator-vmmc {
		compatible = "regulator-fixed";
		regulator-name = "vmmc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
        };

	veth: regulator-veth {
		compatible = "regulator-fixed";
		regulator-name = "veth";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
	};

	xtal24mhz: mclk: kmiclk: sspclk: uartclk: clock-******** {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <********>;
	};

	timclk: clock-1000000 {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-div = <24>;
		clock-mult = <1>;
		clocks = <&xtal24mhz>;
	};

	/* FIXME: this actually hangs off the PLL clocks */
	pclk: clock-pclk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <0>;
	};

	flash@******** {
		compatible = "arm,versatile-flash", "cfi-flash";
		reg = <0x******** 0x4000000>;
		bank-width = <4>;
		partitions {
			compatible = "arm,arm-firmware-suite";
		};
	};

	fpga_flash@******** {
		compatible = "arm,versatile-flash", "cfi-flash";
		reg = <0x******** 0x800000>;
		bank-width = <4>;
		partitions {
			compatible = "arm,arm-firmware-suite";
		};
	};

	/*
	 * The "secure flash" contains things like the boot
	 * monitor so we don't want people to accidentally
	 * screw this up. Mark the device tree node disabled
	 * by default.
	 */
	secflash@3c000000 {
		compatible = "arm,versatile-flash", "cfi-flash";
		reg = <0x3c000000 0x4000000>;
		bank-width = <4>;
		status = "disabled";
	};

	/* SMSC 9118 ethernet with PHY and EEPROM */
	ethernet@3a000000 {
		compatible = "smsc,lan9118", "smsc,lan9115";
		reg = <0x3a000000 0x10000>;
		interrupt-parent = <&intc_fpga1176>;
		interrupts = <0 10 IRQ_TYPE_LEVEL_HIGH>;
		phy-mode = "mii";
		reg-io-width = <4>;
		smsc,irq-active-high;
		smsc,irq-push-pull;
		vdd33a-supply = <&veth>;
		vddvario-supply = <&veth>;
	};

	usb@3b000000 {
		compatible = "nxp,usb-isp1761";
		reg = <0x3b000000 0x20000>;
		interrupt-parent = <&intc_fpga1176>;
		interrupts = <0 11 IRQ_TYPE_LEVEL_HIGH>;
		dr_mode = "peripheral";
	};

	bridge {
		compatible = "ti,ths8134a", "ti,ths8134";
		#address-cells = <1>;
		#size-cells = <0>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				vga_bridge_in: endpoint {
					remote-endpoint = <&clcd_pads>;
				};
			};

			port@1 {
				reg = <1>;

				vga_bridge_out: endpoint {
					remote-endpoint = <&vga_con_in>;
				};
			};
		};
	};

	vga {
		compatible = "vga-connector";

		port {
			vga_con_in: endpoint {
				remote-endpoint = <&vga_bridge_out>;
			};
		};
	};

	soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "arm,realview-pb1176-soc", "simple-bus";
		regmap = <&syscon>;
		ranges;

		syscon: syscon@10000000 {
			compatible = "arm,realview-pb1176-syscon", "syscon", "simple-mfd";
			reg = <0x10000000 0x1000>;
			ranges = <0x0 0x10000000 0x1000>;
			#address-cells = <1>;
			#size-cells = <1>;

			led@8,0 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x01>;
				label = "versatile:0";
				linux,default-trigger = "heartbeat";
				default-state = "on";
			};
			led@8,1 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x02>;
				label = "versatile:1";
				linux,default-trigger = "mmc0";
				default-state = "off";
			};
			led@8,2 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x04>;
				label = "versatile:2";
				linux,default-trigger = "cpu0";
				default-state = "off";
			};
			led@8,3 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x08>;
				label = "versatile:3";
				default-state = "off";
			};
			led@8,4 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x10>;
				label = "versatile:4";
				default-state = "off";
			};
			led@8,5 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x20>;
				label = "versatile:5";
				default-state = "off";
			};
			led@8,6 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x40>;
				label = "versatile:6";
				default-state = "off";
			};
			led@8,7 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x80>;
				label = "versatile:7";
				default-state = "off";
			};
			oscclk0: clock-controller@c {
				compatible = "arm,syscon-icst307";
				reg = <0x0c 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x0C>;
				clocks = <&xtal24mhz>;
			};
			oscclk1: clock-controller@10 {
				compatible = "arm,syscon-icst307";
				reg = <0x10 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x10>;
				clocks = <&xtal24mhz>;
			};
			oscclk2: clock-controller@14 {
				compatible = "arm,syscon-icst307";
				reg = <0x14 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x14>;
				clocks = <&xtal24mhz>;
			};
			oscclk3: clock-controller@18 {
				compatible = "arm,syscon-icst307";
				reg = <0x18 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x18>;
				clocks = <&xtal24mhz>;
			};
			oscclk4: clock-controller@1c {
				compatible = "arm,syscon-icst307";
				reg = <0x1c 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x1c>;
				clocks = <&xtal24mhz>;
			};
		};

		/* Primary DevChip GIC synthesized with the CPU */
		intc_dc1176: interrupt-controller@10120000 {
			compatible = "arm,arm1176jzf-devchip-gic", "arm,arm11mp-gic";
			#interrupt-cells = <3>;
			#address-cells = <1>;
			interrupt-controller;
			reg = <0x10121000 0x1000>,
			      <0x10120000 0x100>;
		};

		L2: cache-controller {
			compatible = "arm,l220-cache";
			reg = <0x10110000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 13 IRQ_TYPE_LEVEL_HIGH>;
			cache-unified;
			cache-level = <2>;
			/*
			 * Override default cache size, sets and
			 * associativity as these may be erroneously set
			 * up by boot loader(s).
			 */
			arm,override-auxreg;
			cache-size = <131072>; // 128kB
			cache-sets = <512>;
			cache-line-size = <32>;
		};

		pmu {
			compatible = "arm,arm1176-pmu";
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 7 IRQ_TYPE_LEVEL_HIGH>;
		};

		timer01: timer@10104000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x10104000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 8 IRQ_TYPE_LEVEL_HIGH>, <0 9 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&timclk>, <&timclk>, <&pclk>;
			clock-names = "timer1", "timer2", "apb_pclk";
		};

		timer23: timer@10105000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x10105000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 10 IRQ_TYPE_LEVEL_HIGH>;
			arm,sp804-has-irq = <1>;
			clocks = <&timclk>, <&timclk>, <&pclk>;
			clock-names = "timer1", "timer2", "apb_pclk";
		};

		pb1176_rtc: rtc@10108000 {
			compatible = "arm,pl031", "arm,primecell";
			reg = <0x10108000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 14 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		pb1176_gpio0: gpio@1010a000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x1010a000 0x1000>;
			gpio-controller;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 16 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		pb1176_ssp: spi@1010b000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x1010b000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 17 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&sspclk>, <&pclk>;
			clock-names = "sspclk", "apb_pclk";
		};

		pb1176_serial0: serial@1010c000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x1010c000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 18 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		pb1176_serial1: serial@1010d000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x1010d000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		pb1176_serial2: serial@1010e000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x1010e000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 20 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		pb1176_serial3: serial@1010f000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x1010f000 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 21 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		/* Direct-mapped development chip ROM */
		pb1176_rom@******** {
			compatible = "mtd-rom";
			reg = <0x******** 0x4000>;
			bank-width = <1>;
		};

		clcd@******** {
			compatible = "arm,pl111", "arm,primecell";
			reg = <0x******** 0x1000>;
			interrupt-parent = <&intc_dc1176>;
			interrupt-names = "combined";
			interrupts = <0 47 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&oscclk0>, <&pclk>;
			clock-names = "clcdclk", "apb_pclk";
			/* 1024x768 16bpp @65MHz works fine */
			max-memory-bandwidth = <********>;

			port {
				clcd_pads: endpoint {
					remote-endpoint = <&vga_bridge_in>;
					arm,pl11x,tft-r0g0b0-pads = <0 8 16>;
				};
			};
		};
	};

	/* These peripherals are inside the FPGA rather than the DevChip */
	fpga {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges;

		i2c0: i2c@******** {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "arm,versatile-i2c";
			reg = <0x******** 0x1000>;

			rtc@68 {
				compatible = "dallas,ds1338";
				reg = <0x68>;
			};
		};

		fpga_aaci: aaci@******** {
			compatible = "arm,pl041", "arm,primecell";
			reg = <0x******** 0x1000>;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		fpga_mci: mmcsd@10005000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x10005000 0x1000>;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 1 IRQ_TYPE_LEVEL_HIGH>,
					<0 2 IRQ_TYPE_LEVEL_HIGH>;
			/* Due to frequent FIFO overruns, use just 500 kHz */
			max-frequency = <500000>;
			bus-width = <4>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			clocks = <&mclk>, <&pclk>;
			clock-names = "mclk", "apb_pclk";
			vmmc-supply = <&vmmc>;
			cd-gpios = <&fpga_gpio1 0 GPIO_ACTIVE_LOW>;
			wp-gpios = <&fpga_gpio1 1 GPIO_ACTIVE_HIGH>;
		};

		fpga_kmi0: kmi@10006000 {
			compatible = "arm,pl050", "arm,primecell";
			reg = <0x10006000 0x1000>;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 3 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&kmiclk>, <&pclk>;
			clock-names = "KMIREFCLK", "apb_pclk";
		};

		fpga_kmi1: kmi@10007000 {
			compatible = "arm,pl050", "arm,primecell";
			reg = <0x10007000 0x1000>;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 4 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&kmiclk>, <&pclk>;
			clock-names = "KMIREFCLK", "apb_pclk";
		};

		fpga_charlcd: charlcd@10008000 {
			compatible = "arm,versatile-lcd";
			reg = <0x10008000 0x1000>;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		fpga_serial: serial@10009000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x10009000 0x1000>;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 6 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		/* This GIC on the board is cascaded off the DevChip GIC */
		intc_fpga1176: interrupt-controller@10040000 {
			compatible = "arm,arm1176jzf-devchip-gic", "arm,arm11mp-gic";
			#interrupt-cells = <3>;
			#address-cells = <1>;
			interrupt-controller;
			reg = <0x10041000 0x1000>,
			      <0x10040000 0x100>;
			interrupt-parent = <&intc_dc1176>;
			interrupts = <0 31 IRQ_TYPE_LEVEL_HIGH>;
		};

		fpga_gpio0: gpio@10014000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x10014000 0x1000>;
			gpio-controller;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 8 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		fpga_gpio1: gpio@10015000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x10015000 0x1000>;
			gpio-controller;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 9 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		fpga_rtc: rtc@10017000 {
			compatible = "arm,pl031", "arm,primecell";
			reg = <0x10017000 0x1000>;
			interrupt-parent = <&intc_fpga1176>;
			interrupts = <0 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};
	};
};
