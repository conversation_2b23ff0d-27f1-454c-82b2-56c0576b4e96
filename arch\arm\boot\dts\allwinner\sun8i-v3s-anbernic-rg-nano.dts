// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/dts-v1/;
#include <dt-bindings/input/linux-event-codes.h>
#include "sun8i-v3s.dtsi"
#include "sunxi-common-regulators.dtsi"

/ {
	model = "Anbernic RG Nano";
	compatible = "anbernic,rg-nano", "allwinner,sun8i-v3s";

	aliases {
		rtc0 = &pcf8563;
		rtc1 = &rtc;
		serial0 = &uart0;
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		brightness-levels = <0 1 2 3 8 14 21 32 46 60 80 100>;
		default-brightness-level = <11>;
		power-supply = <&reg_vcc5v0>;
		pwms = <&pwm 0 40000 1>;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	gpio_keys: gpio-keys {
		compatible = "gpio-keys";

		button-a {
			gpios = <&gpio_expander 12 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-A";
			linux,code = <BTN_EAST>;
		};

		button-b {
			gpios = <&gpio_expander 14 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-B";
			linux,code = <BTN_SOUTH>;
		};

		button-down {
			gpios = <&gpio_expander 1 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "DPAD-DOWN";
			linux,code = <BTN_DPAD_DOWN>;
		};

		button-left {
			gpios = <&gpio_expander 4 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "DPAD-LEFT";
			linux,code = <BTN_DPAD_LEFT>;
		};

		button-right {
			gpios = <&gpio_expander 0 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "DPAD-RIGHT";
			linux,code = <BTN_DPAD_RIGHT>;
		};

		button-se {
			gpios = <&gpio_expander 7 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-SELECT";
			linux,code = <BTN_SELECT>;
		};

		button-st {
			gpios = <&gpio_expander 6 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-START";
			linux,code = <BTN_START>;
		};

		button-tl {
			gpios = <&gpio_expander 2 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-L";
			linux,code = <BTN_TL>;
		};

		button-tr {
			gpios = <&gpio_expander 15 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-R";
			linux,code = <BTN_TR>;
		};

		button-up {
			gpios = <&gpio_expander 3 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "DPAD-UP";
			linux,code = <BTN_DPAD_UP>;
		};

		button-x {
			gpios = <&gpio_expander 11 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-X";
			linux,code = <BTN_NORTH>;
		};

		button-y {
			gpios = <&gpio_expander 13 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
			label = "BTN-Y";
			linux,code = <BTN_WEST>;
		};
	};
};

&codec {
	allwinner,audio-routing = "Speaker", "HP",
				  "MIC1", "Mic",
				  "Mic", "HBIAS";
	allwinner,pa-gpios = <&pio 5 6 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PF6 */
	status = "okay";
};

&ehci {
	status = "okay";
};

&i2c0 {
	status = "okay";

	gpio_expander: gpio@20 {
		compatible = "nxp,pcal6416";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		#interrupt-cells = <2>;
		interrupt-controller;
		interrupt-parent = <&pio>;
		interrupts = <1 3 IRQ_TYPE_EDGE_BOTH>; /* PB3/EINT3 */
		vcc-supply = <&reg_vcc3v3>;
	};

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&pio>;
		interrupts = <1 5 IRQ_TYPE_EDGE_FALLING>; /* PB5/EINT5 */
	};

	pcf8563: rtc@51 {
		compatible = "nxp,pcf8563";
		reg = <0x51>;
	};
};

#include "axp209.dtsi"

&battery_power_supply {
	status = "okay";
};

&mmc0 {
	broken-cd;
	bus-width = <4>;
	disable-wp;
	vmmc-supply = <&reg_vcc3v3>;
	vqmmc-supply = <&reg_vcc3v3>;
	status = "okay";
};

&ohci {
	status = "okay";
};

&pio {
	vcc-pb-supply = <&reg_vcc3v3>;
	vcc-pc-supply = <&reg_vcc3v3>;
	vcc-pf-supply = <&reg_vcc3v3>;
	vcc-pg-supply = <&reg_vcc3v3>;

	spi0_no_miso_pins: spi0-no-miso-pins {
		pins = "PC1", "PC2", "PC3";
		function = "spi0";
	};
};

&pwm {
	pinctrl-0 = <&pwm0_pin>;
	pinctrl-names = "default";
	status = "okay";
};

/* DCDC2 wired into vdd-cpu, vdd-sys, and vdd-ephy. */
&reg_dcdc2 {
	regulator-always-on;
	regulator-max-microvolt = <1250000>;
	regulator-min-microvolt = <1250000>;
	regulator-name = "vdd-cpu";
};

/* DCDC3 wired into every 3.3v input that isn't the RTC. */
&reg_dcdc3 {
	regulator-always-on;
	regulator-max-microvolt = <3300000>;
	regulator-min-microvolt = <3300000>;
	regulator-name = "vcc-io";
};

/* LDO1 wired into RTC, voltage is hard-wired at 3.3v. */
&reg_ldo1 {
	regulator-always-on;
	regulator-name = "vcc-rtc";
};

/* LDO2 wired into VCC-PLL and audio codec. */
&reg_ldo2 {
	regulator-always-on;
	regulator-max-microvolt = <3000000>;
	regulator-min-microvolt = <3000000>;
	regulator-name = "vcc-pll";
};

/* LDO3, LDO4, and LDO5 unused. */
&reg_ldo3 {
	status = "disabled";
};

&reg_ldo4 {
	status = "disabled";
};

/* RTC uses internal oscillator */
&rtc {
	/delete-property/ clocks;
};

&spi0 {
	pinctrl-0 = <&spi0_no_miso_pins>;
	pinctrl-names = "default";
	status = "okay";

	display@0 {
		compatible = "saef,sftc154b", "panel-mipi-dbi-spi";
		reg = <0>;
		backlight = <&backlight>;
		dc-gpios = <&pio 2 0 GPIO_ACTIVE_HIGH>; /* PC0 */
		reset-gpios = <&pio 1 2 GPIO_ACTIVE_HIGH>; /* PB2 */
		spi-max-frequency = <100000000>;

		height-mm = <39>;
		width-mm = <39>;

		/* Set hb-porch to compensate for non-visible area */
		panel-timing {
			hactive = <240>;
			vactive = <240>;
			hback-porch = <80>;
			vback-porch = <0>;
			clock-frequency = <0>;
			hfront-porch = <0>;
			hsync-len = <0>;
			vfront-porch = <0>;
			vsync-len = <0>;
		};
	};
};

&uart0 {
	pinctrl-0 = <&uart0_pb_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 5 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PG5 */
	status = "okay";
};
