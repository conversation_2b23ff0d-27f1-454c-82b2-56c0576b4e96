// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2024 Icenowy Zheng <<EMAIL>>
 */

#include "sun8i-a33.dtsi"

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&mmc2_8bit_pins {
	/* Increase drive strength for DDR modes */
	drive-strength = <40>;
};

&r_rsb {
	status = "okay";

	axp22x: pmic@3a3 {
		compatible = "x-powers,axp223";
		reg = <0x3a3>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		eldoin-supply = <&reg_dcdc1>;
		x-powers,drive-vbus-en;
	};
};

#include "axp223.dtsi"

&reg_aldo1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-io";
};

&reg_aldo2 {
	regulator-always-on;
	regulator-min-microvolt = <2350000>;
	regulator-max-microvolt = <2650000>;
	regulator-name = "vdd-dll";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-avcc";
};

&reg_dc5ldo {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpus";
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-3v3";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-sys";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

&reg_rtc_ldo {
	regulator-name = "vcc-rtc";
};
