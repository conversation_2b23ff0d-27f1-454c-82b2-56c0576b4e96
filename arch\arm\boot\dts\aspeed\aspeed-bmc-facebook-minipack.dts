// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2018 Facebook Inc.
/dts-v1/;

#include "ast2500-facebook-netbmc-common.dtsi"

/ {
	model = "Facebook Minipack 100 BMC";
	compatible = "facebook,minipack-bmc", "aspeed,ast2500";

	aliases {
		/*
		 * Override the default serial aliases to avoid breaking
		 * the legacy applications.
		 */
		serial0 = &uart5;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;
		serial4 = &uart4;

		/*
		 * i2c switch 2-0070, pca9548, 8 child channels assigned
		 * with bus number 16-23.
		 */
		i2c16 = &imux16;
		i2c17 = &imux17;
		i2c18 = &imux18;
		i2c19 = &imux19;
		i2c20 = &imux20;
		i2c21 = &imux21;
		i2c22 = &imux22;
		i2c23 = &imux23;

		/*
		 * i2c switch 8-0070, pca9548, 8 child channels assigned
		 * with bus number 24-31.
		 */
		i2c24 = &imux24;
		i2c25 = &imux25;
		i2c26 = &imux26;
		i2c27 = &imux27;
		i2c28 = &imux28;
		i2c29 = &imux29;
		i2c30 = &imux30;
		i2c31 = &imux31;

		/*
		 * i2c switch 9-0070, pca9548, 8 child channels assigned
		 * with bus number 32-39.
		 */
		i2c32 = &imux32;
		i2c33 = &imux33;
		i2c34 = &imux34;
		i2c35 = &imux35;
		i2c36 = &imux36;
		i2c37 = &imux37;
		i2c38 = &imux38;
		i2c39 = &imux39;

		/*
		 * i2c switch 11-0070, pca9548, 8 child channels assigned
		 * with bus number 40-47.
		 */
		i2c40 = &imux40;
		i2c41 = &imux41;
		i2c42 = &imux42;
		i2c43 = &imux43;
		i2c44 = &imux44;
		i2c45 = &imux45;
		i2c46 = &imux46;
		i2c47 = &imux47;

		/*
		 * I2C Switch 24-0071 (channel #0 of 8-0070): 8 channels for
		 * connecting to left PDB (Power Distribution Board).
		 */
		i2c48 = &imux48;
		i2c49 = &imux49;
		i2c50 = &imux50;
		i2c51 = &imux51;
		i2c52 = &imux52;
		i2c53 = &imux53;
		i2c54 = &imux54;
		i2c55 = &imux55;

		/*
		 * I2C Switch 25-0072 (channel #1 of 8-0070): 8 channels for
		 * connecting to right PDB (Power Distribution Board).
		 */
		i2c56 = &imux56;
		i2c57 = &imux57;
		i2c58 = &imux58;
		i2c59 = &imux59;
		i2c60 = &imux60;
		i2c61 = &imux61;
		i2c62 = &imux62;
		i2c63 = &imux63;

		/*
		 * I2C Switch 26-0076 (channel #2 of 8-0070): 8 channels for
		 * connecting to top FCM (Fan Control Module).
		 */
		i2c64 = &imux64;
		i2c65 = &imux65;
		i2c66 = &imux66;
		i2c67 = &imux67;
		i2c68 = &imux68;
		i2c69 = &imux69;
		i2c70 = &imux70;
		i2c71 = &imux71;

		/*
		 * I2C Switch 27-0076 (channel #3 of 8-0070): 8 channels for
		 * connecting to bottom FCM (Fan Control Module).
		 */
		i2c72 = &imux72;
		i2c73 = &imux73;
		i2c74 = &imux74;
		i2c75 = &imux75;
		i2c76 = &imux76;
		i2c77 = &imux77;
		i2c78 = &imux78;
		i2c79 = &imux79;

		/*
		 * I2C Switch 40-0073 (channel #0 of 11-0070): connecting
		 * to PIM (Port Interface Module) #1 (1-based).
		 */
		i2c80 = &imux80;
		i2c81 = &imux81;
		i2c82 = &imux82;
		i2c83 = &imux83;
		i2c84 = &imux84;
		i2c85 = &imux85;
		i2c86 = &imux86;
		i2c87 = &imux87;

		/*
		 * I2C Switch 41-0073 (channel #1 of 11-0070): connecting
		 * to PIM (Port Interface Module) #2 (1-based).
		 */
		i2c88 = &imux88;
		i2c89 = &imux89;
		i2c90 = &imux90;
		i2c91 = &imux91;
		i2c92 = &imux92;
		i2c93 = &imux93;
		i2c94 = &imux94;
		i2c95 = &imux95;

		/*
		 * I2C Switch 42-0073 (channel #2 of 11-0070): connecting
		 * to PIM (Port Interface Module) #3 (1-based).
		 */
		i2c96 = &imux96;
		i2c97 = &imux97;
		i2c98 = &imux98;
		i2c99 = &imux99;
		i2c100 = &imux100;
		i2c101 = &imux101;
		i2c102 = &imux102;
		i2c103 = &imux103;

		/*
		 * I2C Switch 43-0073 (channel #3 of 11-0070): connecting
		 * to PIM (Port Interface Module) #4 (1-based).
		 */
		i2c104 = &imux104;
		i2c105 = &imux105;
		i2c106 = &imux106;
		i2c107 = &imux107;
		i2c108 = &imux108;
		i2c109 = &imux109;
		i2c110 = &imux110;
		i2c111 = &imux111;

		/*
		 * I2C Switch 44-0073 (channel #4 of 11-0070): connecting
		 * to PIM (Port Interface Module) #5 (1-based).
		 */
		i2c112 = &imux112;
		i2c113 = &imux113;
		i2c114 = &imux114;
		i2c115 = &imux115;
		i2c116 = &imux116;
		i2c117 = &imux117;
		i2c118 = &imux118;
		i2c119 = &imux119;

		/*
		 * I2C Switch 45-0073 (channel #5 of 11-0070): connecting
		 * to PIM (Port Interface Module) #6 (1-based).
		 */
		i2c120 = &imux120;
		i2c121 = &imux121;
		i2c122 = &imux122;
		i2c123 = &imux123;
		i2c124 = &imux124;
		i2c125 = &imux125;
		i2c126 = &imux126;
		i2c127 = &imux127;

		/*
		 * I2C Switch 46-0073 (channel #6 of 11-0070): connecting
		 * to PIM (Port Interface Module) #7 (1-based).
		 */
		i2c128 = &imux128;
		i2c129 = &imux129;
		i2c130 = &imux130;
		i2c131 = &imux131;
		i2c132 = &imux132;
		i2c133 = &imux133;
		i2c134 = &imux134;
		i2c135 = &imux135;

		/*
		 * I2C Switch 47-0073 (channel #7 of 11-0070): connecting
		 * to PIM (Port Interface Module) #8 (1-based).
		 */
		i2c136 = &imux136;
		i2c137 = &imux137;
		i2c138 = &imux138;
		i2c139 = &imux139;
		i2c140 = &imux140;
		i2c141 = &imux141;
		i2c142 = &imux142;
		i2c143 = &imux143;
	};

	chosen {
		stdout-path = &uart1;
		bootargs = "debug console=ttyS1,9600n8 root=/dev/ram rw";
	};
};

&wdt2 {
	status = "okay";
	aspeed,reset-type = "system";
};

/*
 * Both firmware flashes are 64MB on Minipack BMC.
 */
&fmc_flash0 {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		/*
		 * u-boot partition: 384KB.
		 */
		u-boot@0 {
			reg = <0x0 0x60000>;
			label = "u-boot";
		};

		/*
		 * u-boot environment variables: 128KB.
		 */
		u-boot-env@60000 {
			reg = <0x60000 0x20000>;
			label = "env";
		};

		/*
		 * FIT image: 55.5 MB.
		 */
		fit@80000 {
			reg = <0x80000 0x3780000>;
			label = "fit";
		};

		/*
		 * "data0" partition (8MB) is reserved for persistent
		 * data store.
		 */
		data0@3800000 {
			reg = <0x3800000 0x800000>;
			label = "data0";
		};

		/*
		 * "flash0" partition (covering the entire flash) is
		 * explicitly created to avoid breaking legacy applications.
		 */
		flash0@0 {
			reg = <0x0 0x4000000>;
			label = "flash0";
		};
	};
};

&fmc_flash1 {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		flash1@0 {
			reg = <0x0 0x4000000>;
		};
	};
};

&uart1 {
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default
		     &pinctrl_ncts1_default
		     &pinctrl_ndsr1_default
		     &pinctrl_ndtr1_default
		     &pinctrl_nrts1_default>;
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default
		     &pinctrl_rxd2_default>;
};

&uart4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd4_default
		     &pinctrl_rxd4_default>;
};

&i2c0 {
	status = "okay";
	bus-frequency = <400000>;
	multi-master;
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	/*
	 * I2C Switch 2-0070 is connecting to SCM (System Controller
	 * Module).
	 */
	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		imux16: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		imux17: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		imux18: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		imux19: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		imux20: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux21: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux22: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux23: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
	multi-master;
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		/*
		 * I2C Switch 8-0070 channel #0: connecting to left PDB
		 * (Power Distribution Board).
		 */
		imux24: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			i2c-mux@71 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x71>;
				i2c-mux-idle-disconnect;

				imux48: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux49: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux50: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux51: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux52: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux53: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux54: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux55: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 8-0070 channel #1: connecting to right PDB
		 * (Power Distribution Board).
		 */
		imux25: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			i2c-mux@72 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x72>;
				i2c-mux-idle-disconnect;

				imux56: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux57: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux58: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux59: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux60: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux61: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux62: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux63: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 8-0070 channel #2: connecting to top FCM
		 * (Fan Control Module).
		 */
		imux26: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;

			i2c-mux@76 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x76>;
				i2c-mux-idle-disconnect;

				imux64: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux65: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux66: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux67: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux68: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux69: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux70: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux71: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 8-0070 channel #3: connecting to bottom
		 * FCM (Fan Control Module).
		 */
		imux27: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			i2c-mux@76 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x76>;
				i2c-mux-idle-disconnect;

				imux72: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux73: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux74: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux75: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux76: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux77: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux78: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux79: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		imux28: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux29: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux30: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux31: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c9 {
	status = "okay";

	/*
	 * I2C Switch 9-0070 is connecting to MAC/PHY EEPROMs on SMB
	 * (Switch Main Board).
	 */
	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		imux32: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		imux33: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		imux34: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		imux35: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};

		imux36: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		imux37: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		imux38: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		imux39: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		/*
		 * I2C Switch 11-0070 channel #0: connecting to PIM
		 * (Port Interface Module) #1 (1-based).
		 */
		imux40: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux80: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux81: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux82: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux83: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux84: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux85: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux86: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux87: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 11-0070 channel #1: connecting to PIM
		 * (Port Interface Module) #2 (1-based).
		 */
		imux41: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux88: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux89: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux90: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux91: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux92: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux93: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux94: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux95: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 11-0070 channel #2: connecting to PIM
		 * (Port Interface Module) #3 (1-based).
		 */
		imux42: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux96: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux97: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux98: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux99: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux100: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux101: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux102: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux103: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 11-0070 channel #3: connecting to PIM
		 * (Port Interface Module) #4 (1-based).
		 */
		imux43: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux104: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux105: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux106: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux107: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux108: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux109: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux110: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux111: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 11-0070 channel #4: connecting to PIM
		 * (Port Interface Module) #5 (1-based).
		 */
		imux44: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux112: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux113: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux114: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux115: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux116: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux117: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux118: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux119: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 11-0070 channel #5: connecting to PIM
		 * (Port Interface Module) #6 (1-based).
		 */
		imux45: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux120: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux121: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux122: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux123: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux124: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux125: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux126: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux127: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 11-0070 channel #6: connecting to PIM
		 * (Port Interface Module) #7 (1-based).
		 */
		imux46: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux128: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux129: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux130: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux131: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux132: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux133: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux134: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux135: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};

		/*
		 * I2C Switch 11-0070 channel #7: connecting to PIM
		 * (Port Interface Module) #8 (1-based).
		 */
		imux47: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;

			i2c-mux@73 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;
				i2c-mux-idle-disconnect;

				imux136: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux137: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux138: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux139: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

				imux140: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;
				};

				imux141: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <5>;
				};

				imux142: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <6>;
				};

				imux143: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <7>;
				};
			};
		};
	};
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};
