// SPDX-License-Identifier: GPL-2.0
// Copyright (c) 2018 Inspur Corporation
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "ON5263M5 BMC";
	compatible = "inspur,on5263m5-bmc", "aspeed,ast2500";

	chosen {
		stdout-path = &uart5;
		bootargs = "earlycon";
	};

	memory {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@9f000000 {
			no-map;
			reg = <0x9f000000 0x01000000>;
		};
	};

	leds {
	    compatible = "gpio-leds";
	    bmc_alive {
		label = "bmc_alive";
		gpios = <&gpio ASPEED_GPIO(I, 1) GPIO_ACTIVE_LOW>;
		linux,default-trigger = "timer";
	    };
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>;
	};

};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
	};
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c6 {
	status = "okay";

	tmp421@4e {
		compatible = "ti,tmp421";
		reg = <0x4e>;
	};

	tmp112@48 {
		compatible = "ti,tmp112";
		reg = <0x48>;
	};

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
		pagesize = <32>;
	};
};

&i2c7 {
	status = "okay";

	adm1278@11 {
		  compatible = "adi,adm1278";
		  reg = <0x11>;
	};
};

&gfx {
	status = "okay";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00 0x01>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02 0x03>;
	};
};

&adc {
	status = "okay";
};
