// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

#include "aspeed-g4.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Palmetto BMC";
	compatible = "tyan,palmetto-bmc", "aspeed,ast2400";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@40000000 {
		reg = <0x40000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@5f000000 {
			no-map;
			reg = <0x5f000000 0x01000000>; /* 16M */
		};

		coldfire_memory: codefire_memory@5ee00000 {
			reg = <0x5ee00000 0x00200000>;
			no-map;
		};

		flash_memory: region@5c000000 {
			no-map;
			reg = <0x5C000000 0x02000000>; /* 32MB */
		};
	};

	leds {
		compatible = "gpio-leds";

		heartbeat {
			gpios = <&gpio ASPEED_GPIO(R, 4) GPIO_ACTIVE_LOW>;
		};

		power {
			gpios = <&gpio ASPEED_GPIO(R, 5) GPIO_ACTIVE_LOW>;
		};

		identify {
			gpios = <&gpio ASPEED_GPIO(A, 2) GPIO_ACTIVE_LOW>;
		};
	};

	fsi: gpio-fsi {
		compatible = "aspeed,ast2400-cf-fsi-master", "fsi-master";
		#address-cells = <2>;
		#size-cells = <0>;

		memory-region = <&coldfire_memory>;
		aspeed,sram = <&sram>;
		aspeed,cvic = <&cvic>;

		clock-gpios = <&gpio ASPEED_GPIO(A, 4) GPIO_ACTIVE_HIGH>;
		data-gpios = <&gpio ASPEED_GPIO(A, 5) GPIO_ACTIVE_HIGH>;
		mux-gpios = <&gpio ASPEED_GPIO(A, 6) GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio ASPEED_GPIO(D, 0) GPIO_ACTIVE_HIGH>;
		trans-gpios = <&gpio ASPEED_GPIO(H, 6) GPIO_ACTIVE_HIGH>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-checkstop {
			label = "checkstop";
			gpios = <&gpio ASPEED_GPIO(P, 5) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(P, 5)>;
		};
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1debug_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
		label = "pnor";
	};
};

&pinctrl {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_flbusy_default &pinctrl_flwp_default

			&pinctrl_vgahs_default &pinctrl_vgavs_default
			&pinctrl_ddcclk_default &pinctrl_ddcdat_default>;
};

&uart1 {
	/* Rear RS-232 connector */
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default
			&pinctrl_nrts1_default
			&pinctrl_ndtr1_default
			&pinctrl_ndsr1_default
			&pinctrl_ncts1_default
			&pinctrl_ndcd1_default
			&pinctrl_nri1_default>;
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	use-ncsi;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
};

&i2c0 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c256";
		reg = <0x50>;
		pagesize = <64>;
	};

	rtc@68 {
		compatible = "dallas,ds3231";
		reg = <0x68>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	tmp423@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};
};

&i2c3 {
	status = "okay";

	occ-hwmon@50 {
		compatible = "ibm,p8-occ-hwmon";
		reg = <0x50>;
	};
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&ibt {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi>;
};

&gpio {
	pin_func_mode0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(C, 4) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "func_mode0";
	};

	pin_func_mode1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(C, 5)  GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "func_mode1";
	};

	pin_func_mode2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(C, 6) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "func_mode2";
	};

	pin_gpio_a0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(A, 0) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "BMC_FAN_RESERVED_N";
	};

	pin_gpio_a1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(A, 1) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "APSS_WDT_N";
	};

	pin_gpio_b1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(B, 1) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "APSS_BOOT_MODE";
	};

	pin_gpio_b2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(B, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "APSS_RESET_N";
	};

	pin_gpio_b7 {
		gpio-hog;
		gpios = <ASPEED_GPIO(B, 7) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "SPIVID_STBY_RESET_N";
	};

	pin_gpio_d1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(D, 1) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_POWER_UP";
	};

	pin_gpio_f1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 1) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "BMC_BATTERY_TEST";
	};

	pin_gpio_f4 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 4) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "AST_HW_FAULT_N";
	};

	pin_gpio_f5 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 5) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "AST_SYS_FAULT_N";
	};

	pin_gpio_f7 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 7) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_FULL_SPEED_N";
	};

	pin_gpio_g3 {
		gpio-hog;
		gpios = <ASPEED_GPIO(G, 3) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_FAN_ERROR_N";
	};

	pin_gpio_g4 {
		gpio-hog;
		gpios = <ASPEED_GPIO(G, 4) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "BMC_WDT_RST1_P";
	};

	pin_gpio_g5 {
		gpio-hog;
		gpios = <ASPEED_GPIO(G, 5) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "BMC_WDT_RST2_P";
	};

	pin_gpio_h0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(H, 0) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "PE_SLOT_TEST_EN_N";
	};

	pin_gpio_h1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(H, 1) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "BMC_RTCRST_N";
	};

	pin_gpio_h2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(H, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "SYS_PWROK_BMC";
	};

	pin_gpio_h7 {
		gpio-hog;
		gpios = <ASPEED_GPIO(H, 7) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_TPM_INT_N";
	};
};

&fsi {
	cfam@0,0 {
		reg = <0 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <0>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		fsi_hub0: hub@3400 {
			compatible = "ibm,fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;
			no-scan-on-init;
		};
	};
};
