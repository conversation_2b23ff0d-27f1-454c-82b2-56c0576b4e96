# SPDX-License-Identifier: GPL-2.0
# Enables support for device-tree overlays
DTC_FLAGS_bcm2835-rpi-b := -@
DTC_FLAGS_bcm2835-rpi-a := -@
DTC_FLAGS_bcm2835-rpi-b-rev2 := -@
DTC_FLAGS_bcm2835-rpi-b-plus := -@
DTC_FLAGS_bcm2835-rpi-a-plus := -@
DTC_FLAGS_bcm2835-rpi-cm1-io1 := -@
DTC_FLAGS_bcm2836-rpi-2-b := -@
DTC_FLAGS_bcm2837-rpi-2-b := -@
DTC_FLAGS_bcm2837-rpi-3-a-plus := -@
DTC_FLAGS_bcm2837-rpi-3-b := -@
DTC_FLAGS_bcm2837-rpi-3-b-plus := -@
DTC_FLAGS_bcm2837-rpi-cm3-io3 := -@
DTC_FLAGS_bcm2837-rpi-zero-2-w := -@
DTC_FLAGS_bcm2711-rpi-400 := -@
DTC_FLAGS_bcm2711-rpi-4-b := -@
DTC_FLAGS_bcm2711-rpi-cm4-io := -@
DTC_FLAGS_bcm2835-rpi-zero := -@
DTC_FLAGS_bcm2835-rpi-zero-w := -@
dtb-$(CONFIG_ARCH_BCM2835) += \
	bcm2835-rpi-b.dtb \
	bcm2835-rpi-a.dtb \
	bcm2835-rpi-b-rev2.dtb \
	bcm2835-rpi-b-plus.dtb \
	bcm2835-rpi-a-plus.dtb \
	bcm2835-rpi-cm1-io1.dtb \
	bcm2836-rpi-2-b.dtb \
	bcm2837-rpi-2-b.dtb \
	bcm2837-rpi-3-a-plus.dtb \
	bcm2837-rpi-3-b.dtb \
	bcm2837-rpi-3-b-plus.dtb \
	bcm2837-rpi-cm3-io3.dtb \
	bcm2837-rpi-zero-2-w.dtb \
	bcm2711-rpi-400.dtb \
	bcm2711-rpi-4-b.dtb \
	bcm2711-rpi-cm4-io.dtb \
	bcm2835-rpi-zero.dtb \
	bcm2835-rpi-zero-w.dtb
dtb-$(CONFIG_ARCH_BCMBCA) += \
	bcm6846-genexis-xg6846b.dtb \
	bcm947622.dtb \
	bcm963138.dtb \
	bcm963138dvt.dtb \
	bcm963148.dtb \
	bcm963178.dtb \
	bcm96756.dtb \
	bcm96846.dtb \
	bcm96855.dtb \
	bcm96878.dtb
dtb-$(CONFIG_ARCH_BCM_5301X) += \
	bcm4708-asus-rt-ac56u.dtb \
	bcm4708-asus-rt-ac68u.dtb \
	bcm4708-buffalo-wzr-1750dhp.dtb \
	bcm4708-buffalo-wzr-1166dhp.dtb \
	bcm4708-buffalo-wzr-1166dhp2.dtb \
	bcm4708-linksys-ea6300-v1.dtb \
	bcm4708-linksys-ea6500-v2.dtb \
	bcm4708-luxul-xap-1510.dtb \
	bcm4708-luxul-xwc-1000.dtb \
	bcm4708-netgear-r6250.dtb \
	bcm4708-netgear-r6300-v2.dtb \
	bcm4708-smartrg-sr400ac.dtb \
	bcm47081-asus-rt-n18u.dtb \
	bcm47081-buffalo-wzr-600dhp2.dtb \
	bcm47081-buffalo-wzr-900dhp.dtb \
	bcm47081-luxul-xap-1410.dtb \
	bcm47081-luxul-xwr-1200.dtb \
	bcm47081-tplink-archer-c5-v2.dtb \
	bcm4709-asus-rt-ac3200.dtb \
	bcm4709-asus-rt-ac87u.dtb \
	bcm4709-buffalo-wxr-1900dhp.dtb \
	bcm4709-linksys-ea9200.dtb \
	bcm4709-netgear-r7000.dtb \
	bcm4709-netgear-r8000.dtb \
	bcm4709-tplink-archer-c9-v1.dtb \
	bcm47094-asus-rt-ac3100.dtb \
	bcm47094-asus-rt-ac5300.dtb \
	bcm47094-asus-rt-ac88u.dtb \
	bcm47094-dlink-dir-885l.dtb \
	bcm47094-dlink-dir-890l.dtb \
	bcm47094-linksys-panamera.dtb \
	bcm47094-luxul-abr-4500.dtb \
	bcm47094-luxul-xap-1610.dtb \
	bcm47094-luxul-xbr-4500.dtb \
	bcm47094-luxul-xwc-2000.dtb \
	bcm47094-luxul-xwr-3100.dtb \
	bcm47094-luxul-xwr-3150-v1.dtb \
	bcm47094-netgear-r8500.dtb \
	bcm47094-phicomm-k3.dtb \
	bcm53015-meraki-mr26.dtb \
	bcm53016-dlink-dwl-8610ap.dtb \
	bcm53016-meraki-mr32.dtb \
	bcm94708.dtb \
	bcm94709.dtb \
	bcm953012er.dtb \
	bcm953012hr.dtb \
	bcm953012k.dtb
dtb-$(CONFIG_ARCH_BCM_53573) += \
	bcm47189-luxul-xap-1440.dtb \
	bcm47189-luxul-xap-810.dtb \
	bcm47189-tenda-ac9.dtb \
	bcm947189acdbmr.dtb
dtb-$(CONFIG_ARCH_BCM_CYGNUS) += \
	bcm911360_entphn.dtb \
	bcm911360k.dtb \
	bcm958300k.dtb \
	bcm958305k.dtb
dtb-$(CONFIG_ARCH_BCM_HR2) += \
	bcm53340-ubnt-unifi-switch8.dtb
dtb-$(CONFIG_ARCH_BCM_MOBILE) += \
	bcm28155-ap.dtb \
	bcm21664-garnet.dtb \
	bcm23550-sparrow.dtb
dtb-$(CONFIG_ARCH_BCM_NSP) += \
	bcm958522er.dtb \
	bcm958525er.dtb \
	bcm958525xmc.dtb \
	bcm958622hr.dtb \
	bcm958623hr.dtb \
	bcm958625-meraki-mx64.dtb \
	bcm958625-meraki-mx64-a0.dtb \
	bcm958625-meraki-mx64w.dtb \
	bcm958625-meraki-mx64w-a0.dtb \
	bcm958625-meraki-mx65.dtb \
	bcm958625-meraki-mx65w.dtb \
	bcm958625hr.dtb \
	bcm988312hr.dtb \
	bcm958625k.dtb
dtb-$(CONFIG_ARCH_BRCMSTB) += \
	bcm7445-bcm97445svmb.dtb
