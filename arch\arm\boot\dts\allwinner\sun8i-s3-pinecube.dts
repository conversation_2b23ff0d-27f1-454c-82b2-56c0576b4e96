// SPDX-License-Identifier: (GPL-2.0+ OR X11)
/*
 * Copyright 2019 Icenowy Zheng <<EMAIL>>
 */

/dts-v1/;
#include "sun8i-v3.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "PineCube IP Camera";
	compatible = "pine64,pinecube", "sochip,s3", "allwinner,sun8i-v3";

	aliases {
		serial0 = &uart2;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led1 {
			label = "pine64:ir:led1";
			gpios = <&pio 1 10 GPIO_ACTIVE_LOW>; /* PB10 */
		};

		led2 {
			label = "pine64:ir:led2";
			gpios = <&pio 1 12 GPIO_ACTIVE_LOW>; /* PB12 */
		};
	};

	reg_vcc5v0: vcc5v0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	reg_vcc_wifi: vcc-wifi {
		compatible = "regulator-fixed";
		regulator-name = "vcc-wifi";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pio 1 2 GPIO_ACTIVE_LOW>; /* PB2 WIFI-EN */
		vin-supply = <&reg_dcdc3>;
		startup-delay-us = <200000>;
	};

	wifi_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 1 3 GPIO_ACTIVE_LOW>; /* PB3 WIFI-RST */
		post-power-on-delay-ms = <200>;
	};
};

&csi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&csi1_8bit_pins>;
	status = "okay";

	port {
		csi1_ep: endpoint {
			remote-endpoint = <&ov5640_ep>;
			bus-width = <8>;
			hsync-active = <1>; /* Active high */
			vsync-active = <0>; /* Active low */
			data-active = <1>;  /* Active high */
			pclk-sample = <1>;  /* Rising */
		};
	};
};

&emac {
	phy-handle = <&int_mii_phy>;
	phy-mode = "mii";
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pe_pins>;
	status = "okay";

	ov5640: camera@3c {
		compatible = "ovti,ov5640";
		reg = <0x3c>;
		pinctrl-names = "default";
		pinctrl-0 = <&csi1_mclk_pin>;
		clocks = <&ccu CLK_CSI1_MCLK>;
		clock-names = "xclk";

		AVDD-supply = <&reg_ldo3>;
		DOVDD-supply = <&reg_ldo3>;
		DVDD-supply = <&reg_ldo4>;
		reset-gpios = <&pio 4 23 GPIO_ACTIVE_LOW>; /* PE23 */
		powerdown-gpios = <&pio 4 24 GPIO_ACTIVE_HIGH>; /* PE24 */

		port {
			ov5640_ep: endpoint {
				remote-endpoint = <&csi1_ep>;
				bus-width = <8>;
				hsync-active = <1>; /* Active high */
				vsync-active = <0>; /* Active low */
				data-active = <1>;  /* Active high */
				pclk-sample = <1>;  /* Rising */
			};
		};
	};
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-200 {
		label = "Setup";
		linux,code = <KEY_SETUP>;
		channel = <0>;
		voltage = <190000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_dcdc3>;
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&mmc1 {
	vmmc-supply = <&reg_vcc_wifi>;
	vqmmc-supply = <&reg_dcdc3>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&pio {
	vcc-pd-supply = <&reg_dcdc3>;
	vcc-pe-supply = <&reg_ldo3>;
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1250000>;
	regulator-max-microvolt = <1250000>;
	regulator-name = "vdd-sys-cpu-ephy";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-3v3";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_ldo3 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-name = "avdd-dovdd-2v8-csi";
	regulator-soft-start;
	regulator-ramp-delay = <1600>;
};

&reg_ldo4 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "dvdd-1v8-csi";
};

&spi0 {
	status = "okay";

	flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "winbond,w25q128", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <40000000>;
	};
};

&uart2 {
	status = "okay";
};

&usb_otg {
	dr_mode = "host";
	status = "okay";
};

&usbphy {
	usb0_vbus-supply = <&reg_vcc5v0>;
	status = "okay";
};
