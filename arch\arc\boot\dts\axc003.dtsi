// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014-15 Synopsys, Inc. (www.synopsys.com)
 */

/*
 * Device tree for AXC003 CPU card: HS38x UP configuration
 */

/include/ "skeleton_hs.dtsi"

/ {
	compatible = "snps,arc";
	#address-cells = <2>;
	#size-cells = <2>;

	cpu_card {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		ranges = <0x00000000 0x0 0xf0000000 0x10000000>;

		input_clk: input-clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <33333333>;
		};

		core_clk: core-clk@80 {
			compatible = "snps,axs10x-arc-pll-clock";
			reg = <0x80 0x10>, <0x100 0x10>;
			#clock-cells = <0>;
			clocks = <&input_clk>;

			/*
			 * Set initial core pll output frequency to 90MHz.
			 * It will be applied at the core pll driver probing
			 * on early boot.
			 */
			assigned-clocks = <&core_clk>;
			assigned-clock-rates = <90000000>;
		};

		core_intc: archs-intc@cpu {
			compatible = "snps,archs-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		/*
		 * this GPIO block ORs all interrupts on CPU card (creg,..)
		 * to uplink only 1 IRQ to ARC core intc
		 */
		dw-apb-gpio@2000 {
			compatible = "snps,dw-apb-gpio";
			reg = < 0x2000 0x80 >;
			#address-cells = <1>;
			#size-cells = <0>;

			ictl_intc: gpio-controller@0 {
				compatible = "snps,dw-apb-gpio-port";
				gpio-controller;
				#gpio-cells = <2>;
				ngpios = <30>;
				reg = <0>;
				interrupt-controller;
				#interrupt-cells = <2>;
				interrupt-parent = <&core_intc>;
				interrupts = <25>;
			};
		};

		debug_uart: dw-apb-uart@5000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x5000 0x100>;
			clock-frequency = <33333000>;
			interrupt-parent = <&ictl_intc>;
			interrupts = <2 4>;
			baud = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
		};

		arcpct0: pct {
			compatible = "snps,archs-pct";
			#interrupt-cells = <1>;
			interrupt-parent = <&core_intc>;
			interrupts = <20>;
		};
	};

	/*
	 * Mark DMA peripherals connected via IOC port as dma-coherent. We do
	 * it via overlay because peripherals defined in axs10x_mb.dtsi are
	 * used for both AXS101 and AXS103 boards and only AXS103 has IOC (so
	 * only AXS103 board has HW-coherent DMA peripherals)
	 * We don't need to mark pgu@17000 as dma-coherent because it uses
	 * external DMA buffer located outside of IOC aperture.
	 */
	axs10x_mb {
		ethernet@18000 {
			dma-coherent;
		};

		usb@40000 {
			dma-coherent;
		};

		usb@60000 {
			dma-coherent;
		};

		mmc@15000 {
			dma-coherent;
		};
	};

	/*
	 * The DW APB ICTL intc on MB is connected to CPU intc via a
	 * DT "invisible" DW APB GPIO block, configured to simply pass thru
	 * interrupts - setup accordingly in platform init (plat-axs10x/ax10x.c)
	 *
	 * So here we mimic a direct connection between them, ignoring the
	 * ABPG GPIO. Thus set "interrupts = <24>" (DW APB GPIO to core)
	 * instead of "interrupts = <12>" (DW APB ICTL to DW APB GPIO)
	 *
	 * This intc actually resides on MB, but we move it here to
	 * avoid duplicating the MB dtsi file given that IRQ from
	 * this intc to cpu intc are different for axs101 and axs103
	 */
	mb_intc: interrupt-controller@e0012000 {
		#interrupt-cells = <1>;
		compatible = "snps,dw-apb-ictl";
		reg = < 0x0 0xe0012000 0x0 0x200 >;
		interrupt-controller;
		interrupt-parent = <&core_intc>;
		interrupts = < 24 >;
	};

	memory {
		device_type = "memory";
		/* CONFIG_LINUX_RAM_BASE needs to match low mem start */
		reg = <0x0 0x80000000 0x0 0x20000000	/* 512 MiB low mem */
		       0x1 0xc0000000 0x0 0x40000000>;	/* 1 GiB highmem */
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		/*
		 * Move frame buffer out of IOC aperture (0x8z-0xaz).
		 */
		frame_buffer: frame_buffer@be000000 {
			compatible = "shared-dma-pool";
			reg = <0x0 0xbe000000 0x0 0x2000000>;
			no-map;
		};
	};
};
