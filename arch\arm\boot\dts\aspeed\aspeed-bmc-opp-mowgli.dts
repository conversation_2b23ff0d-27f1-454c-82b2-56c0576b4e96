// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "Mowgli BMC";
	compatible = "ibm,mowgli-bmc", "aspeed,ast2500";


	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		flash_memory: region@98000000 {
			no-map;
			reg = <0x98000000 0x04000000>; /* 64M */
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-air-water {
			label = "air-water";
			gpios = <&gpio ASPEED_GPIO(F, 6) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(F, 6)>;
		};

		event-checkstop {
			label = "checkstop";
			gpios = <&gpio ASPEED_GPIO(J, 2) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(J, 2)>;
		};

		event-ps0-presence {
			label = "ps0-presence";
			gpios = <&gpio ASPEED_GPIO(Z, 2) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(Z, 2)>;
		};

		event-ps1-presence {
			label = "ps1-presence";
			gpios = <&gpio ASPEED_GPIO(Z, 0) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(Z, 0)>;
		};

		button-id {
			label = "id-button";
			gpios = <&gpio ASPEED_GPIO(F, 1) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(F, 1)>;
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-fan0-presence {
			label = "fan0-presence";
			gpios = <&pca9552 9 GPIO_ACTIVE_LOW>;
			linux,code = <9>;
		};

		event-fan1-presence {
			label = "fan1-presence";
			gpios = <&pca9552 10 GPIO_ACTIVE_LOW>;
			linux,code = <10>;
		};

		event-fan2-presence {
			label = "fan2-presence";
			gpios = <&pca9552 11 GPIO_ACTIVE_LOW>;
			linux,code = <11>;
		};

		event-fan3-presence {
			label = "fan3-presence";
			gpios = <&pca9552 12 GPIO_ACTIVE_LOW>;
			linux,code = <12>;
		};

		event-fan4-presence {
			label = "fan4-presence";
			gpios = <&pca9552 13 GPIO_ACTIVE_LOW>;
			linux,code = <13>;
		};
	};

	leds {
		compatible = "gpio-leds";

		front-fault {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&gpio ASPEED_GPIO(AA, 0) GPIO_ACTIVE_LOW>;
		};

		power-button {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&gpio ASPEED_GPIO(AA, 1) GPIO_ACTIVE_LOW>;
		};

		front-id {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&gpio ASPEED_GPIO(AA, 2) GPIO_ACTIVE_LOW>;
		};

		fan0 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca9552 0 GPIO_ACTIVE_LOW>;
		};

		fan1 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca9552 1 GPIO_ACTIVE_LOW>;
		};

		fan2 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca9552 2 GPIO_ACTIVE_LOW>;
		};

		fan3 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca9552 3 GPIO_ACTIVE_LOW>;
		};

		fan4 {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&pca9552 4 GPIO_ACTIVE_LOW>;
		};
	};

	fsi: gpio-fsi {
		compatible = "fsi-master-gpio", "fsi-master";
		#address-cells = <2>;
		#size-cells = <0>;
		no-gpio-delays;

		clock-gpios = <&gpio ASPEED_GPIO(E, 6) GPIO_ACTIVE_HIGH>;
		data-gpios = <&gpio ASPEED_GPIO(E, 7) GPIO_ACTIVE_HIGH>;
		mux-gpios = <&gpio ASPEED_GPIO(R, 2) GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio ASPEED_GPIO(D, 0) GPIO_ACTIVE_HIGH>;
		trans-gpios = <&gpio ASPEED_GPIO(E, 5) GPIO_ACTIVE_HIGH>;
	};

	iio-hwmon-12v {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>;
	};

	iio-hwmon-5v {
		compatible = "iio-hwmon";
		io-channels = <&adc 1>;
	};

	iio-hwmon-3v {
		compatible = "iio-hwmon";
		io-channels = <&adc 2>;
	};

	iio-hwmon-vdd {
		compatible = "iio-hwmon";
		io-channels = <&adc 3>;
	};

	iio-hwmon-vcs {
		compatible = "iio-hwmon";
		io-channels = <&adc 5>;
	};

	iio-hwmon-vdn {
		compatible = "iio-hwmon";
		io-channels = <&adc 7>;
	};

	iio-hwmon-vio {
		compatible = "iio-hwmon";
		io-channels = <&adc 9>;
	};

	iio-hwmon-vddra {
		compatible = "iio-hwmon";
		io-channels = <&adc 11>;
	};

	iio-hwmon-battery {
		compatible = "iio-hwmon";
		io-channels = <&adc 12>;
	};

	iio-hwmon-vddrb {
		compatible = "iio-hwmon";
		io-channels = <&adc 13>;
	};
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default
		&pinctrl_pwm2_default &pinctrl_pwm3_default
		&pinctrl_pwm4_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};

	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};

	fan@5 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05>;
	};

	fan@6 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06>;
	};

	fan@7 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x07>;
	};

	fan@8 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x08>;
	};

	fan@9 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x09>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		label = "bmc";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
		partitions {
			#address-cells = < 1 >;
			#size-cells = < 1 >;
			compatible = "fixed-partitions";
			u-boot@0 {
				reg = < 0 0x60000 >;
				label = "u-boot";
			};
			u-boot-env@60000 {
				reg = < 0x60000 0x20000 >;
				label = "u-boot-env";
			};
			obmc-ubi@80000 {
				reg = < 0x80000 0x1F80000 >;
				label = "obmc-ubi";
			};
		};
	};
	flash@1 {
		status = "okay";
		label = "alt-bmc";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
		partitions {
			#address-cells = < 1 >;
			#size-cells = < 1 >;
			compatible = "fixed-partitions";
			u-boot@0 {
				reg = < 0 0x60000 >;
				label = "alt-u-boot";
			};
			u-boot-env@60000 {
				reg = < 0x60000 0x20000 >;
				label = "alt-u-boot-env";
			};
			obmc-ubi@80000 {
				reg = < 0x80000 0x1F80000 >;
				label = "alt-obmc-ubi";
			};
		};
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		label = "pnor";
		m25p,fast-read;
		spi-max-frequency = <100000000>;
	};
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&uart1 {
	/* Rear RS-232 connector */
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default
			&pinctrl_nrts1_default
			&pinctrl_ndtr1_default
			&pinctrl_ndsr1_default
			&pinctrl_ncts1_default
			&pinctrl_ndcd1_default
			&pinctrl_nri1_default>;
};

&uart2 {
	/* APSS */
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default &pinctrl_rxd2_default>;
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c0 {
	status = "okay";

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};
};

&i2c1 {
	status = "disabled";
};

&i2c2 {
	status = "okay";

	/* CPU MFG CONN */

};

&i2c3 {
	status = "okay";

	/* APSS */
	/* CPLD */

	/* PCA9516 (repeater) ->
	 *    CLK Buffer 9FGS9092
	 *    Power Supply 0
	 *    Power Supply 1
	 *    PCA 9552 LED
	 */

	pca9552: pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio@0 {
			reg = <0>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@1 {
			reg = <1>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@2 {
			reg = <2>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@3 {
			reg = <3>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@4 {
			reg = <4>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@5 {
			reg = <5>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@6 {
			reg = <6>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@7 {
			reg = <7>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@8 {
			reg = <8>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@9 {
			reg = <9>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@10 {
			reg = <10>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@11 {
			reg = <11>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@12 {
			reg = <12>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@13 {
			reg = <13>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@14 {
			reg = <14>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@15 {
			reg = <15>;
			type = <PCA955X_TYPE_GPIO>;
		};
	};

	power-supply@68 {
		compatible = "ibm,cffps1";
		reg = <0x68>;
	};

	power-supply@69 {
		compatible = "ibm,cffps1";
		reg = <0x69>;
	};
};

&i2c4 {
	status = "okay";

	/* CP0 VDD & VCS : IR35221 */
	/* CP0 VDN & VIO : IR35221 */
	/* CP0 VDDR : IR35221 */

	ir35221@28 {
		compatible = "infineon,ir35221";
		reg = <0x28>;
	};

	ir35221@29 {
		compatible = "infineon,ir35221";
		reg = <0x29>;
	};

	ir35221@2d {
		compatible = "infineon,ir35221";
		reg = <0x2d>;
	};

};

&i2c5 {
	status = "disabled";
};

&i2c6 {
	status = "disabled";
};

&i2c7 {
	status = "disabled";
};

&i2c8 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};
};

&i2c9 {
	status = "okay";

	/* PCIe G3 x16 slot */
};

&i2c10 {
	status = "disabled";
};

&i2c11 {
	status = "okay";

	/* CPLD */
	/* TPM */
	/* RTC RX8900CE */
	/* TMP275A */
	/* TMP275A */

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	tmp275@49 {
		compatible = "ti,tmp275";
		reg = <0x49>;
	};

};

&i2c12 {
	status = "disabled";
};

&i2c13 {
	status = "disabled";
};

&vuart {
	status = "okay";
};

&gfx {
	status = "okay";
	memory-region = <&gfx_memory>;
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
			&pinctrl_adc1_default
			&pinctrl_adc2_default
			&pinctrl_adc3_default
			&pinctrl_adc4_default
			&pinctrl_adc5_default
			&pinctrl_adc6_default
			&pinctrl_adc7_default
			&pinctrl_adc8_default
			&pinctrl_adc9_default
			&pinctrl_adc10_default
			&pinctrl_adc11_default
			&pinctrl_adc12_default
			&pinctrl_adc13_default
			&pinctrl_adc14_default
			&pinctrl_adc15_default>;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	aspeed,alt-boot;
};

&ibt {
	status = "okay";
};

&vhub {
	status = "okay";
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

#include "ibm-power9-dual.dtsi"
