/*
 * Copyright 2016 Linaro Ltd
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

/dts-v1/;
#include "arm-realview-eb-mp.dtsi"

/ {
	model = "ARM RealView Emulation Baseboard with ARM11MPCore Rev C Core Tile";
	arm,hbi = <0x146>;

	/*
	 * This is the ARM11 MPCore tile (HBI-0146) used with the RealView EB.
	 * Reference: ARM DUI 0318F
	 *
	 * To run this machine with QEMU, specify the following:
	 * qemu-system-arm -M realview-eb-mpcore -smp cpus=4
	 */
	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "arm,realview-smp";

		MP11_0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,arm11mpcore";
			reg = <0>;
			next-level-cache = <&L2>;
		};

		MP11_1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,arm11mpcore";
			reg = <1>;
			next-level-cache = <&L2>;
		};

		MP11_2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,arm11mpcore";
			reg = <2>;
			next-level-cache = <&L2>;
		};

		MP11_3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,arm11mpcore";
			reg = <3>;
			next-level-cache = <&L2>;
		};
	};
};

&pmu {
	interrupt-affinity = <&MP11_0>, <&MP11_1>, <&MP11_2>, <&MP11_3>;
};
