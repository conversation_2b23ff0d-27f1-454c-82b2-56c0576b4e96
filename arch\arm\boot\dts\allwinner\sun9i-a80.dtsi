/*
 * Copyright 2014 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

#include <dt-bindings/clock/sun9i-a80-ccu.h>
#include <dt-bindings/clock/sun9i-a80-de.h>
#include <dt-bindings/clock/sun9i-a80-usb.h>
#include <dt-bindings/reset/sun9i-a80-ccu.h>
#include <dt-bindings/reset/sun9i-a80-de.h>
#include <dt-bindings/reset/sun9i-a80-usb.h>

/ {
	#address-cells = <2>;
	#size-cells = <2>;
	interrupt-parent = <&gic>;

	aliases {
		ethernet0 = &gmac;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			cci-control-port = <&cci_control0>;
			clock-frequency = <12000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x0>;
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			cci-control-port = <&cci_control0>;
			clock-frequency = <12000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x1>;
		};

		cpu2: cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			cci-control-port = <&cci_control0>;
			clock-frequency = <12000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x2>;
		};

		cpu3: cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			cci-control-port = <&cci_control0>;
			clock-frequency = <12000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x3>;
		};

		cpu4: cpu@100 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			cci-control-port = <&cci_control1>;
			clock-frequency = <18000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x100>;
		};

		cpu5: cpu@101 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			cci-control-port = <&cci_control1>;
			clock-frequency = <18000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x101>;
		};

		cpu6: cpu@102 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			cci-control-port = <&cci_control1>;
			clock-frequency = <18000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x102>;
		};

		cpu7: cpu@103 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			cci-control-port = <&cci_control1>;
			clock-frequency = <18000000>;
			enable-method = "allwinner,sun9i-a80-smp";
			reg = <0x103>;
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		clock-frequency = <24000000>;
		arm,cpu-registers-not-fw-configured;
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		/*
		 * map 64 bit address range down to 32 bits,
		 * as the peripherals are all under 512MB.
		 */
		ranges = <0 0 0 0x20000000>;

		/*
		 * This clock is actually configurable from the PRCM address
		 * space. The external 24M oscillator can be turned off, and
		 * the clock switched to an internal 16M RC oscillator. Under
		 * normal operation there's no reason to do this, and the
		 * default is to use the external good one, so just model this
		 * as a fixed clock. Also it is not entirely clear if the
		 * osc24M mux in the PRCM affects the entire clock tree, which
		 * would also throw all the PLL clock rates off, or just the
		 * downstream clocks in the PRCM.
		 */
		osc24M: clk-24M {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-output-names = "osc24M";
		};

		/*
		 * The 32k clock is from an external source, normally the
		 * AC100 codec/RTC chip. This serves as a placeholder for
		 * board dts files to specify the source.
		 */
		osc32k: clk-32k {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clock-div = <1>;
			clock-mult = <1>;
			clock-output-names = "osc32k";
		};

		/*
		 * The following two are dummy clocks, placeholders
		 * used in the gmac_tx clock. The gmac driver will
		 * choose one parent depending on the PHY interface
		 * mode, using clk_set_rate auto-reparenting.
		 *
		 * The actual TX clock rate is not controlled by the
		 * gmac_tx clock.
		 */
		mii_phy_tx_clk: mii-phy-tx-clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <25000000>;
			clock-output-names = "mii_phy_tx";
		};

		gmac_int_tx_clk: gmac-int-tx-clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <125000000>;
			clock-output-names = "gmac_int_tx";
		};

		gmac_tx_clk: clk@800030 {
			#clock-cells = <0>;
			compatible = "allwinner,sun7i-a20-gmac-clk";
			reg = <0x00800030 0x4>;
			clocks = <&mii_phy_tx_clk>, <&gmac_int_tx_clk>;
			clock-output-names = "gmac_tx";
		};

		cpus_clk: clk@8001410 {
			compatible = "allwinner,sun9i-a80-cpus-clk";
			reg = <0x08001410 0x4>;
			#clock-cells = <0>;
			clocks = <&osc32k>, <&osc24M>,
				 <&ccu CLK_PLL_PERIPH0>,
				 <&ccu CLK_PLL_AUDIO>;
			clock-output-names = "cpus";
		};

		ahbs: clk-ahbs {
			compatible = "fixed-factor-clock";
			#clock-cells = <0>;
			clock-div = <1>;
			clock-mult = <1>;
			clocks = <&cpus_clk>;
			clock-output-names = "ahbs";
		};

		apbs: clk@800141c {
			compatible = "allwinner,sun8i-a23-apb0-clk";
			reg = <0x0800141c 0x4>;
			#clock-cells = <0>;
			clocks = <&ahbs>;
			clock-output-names = "apbs";
		};

		apbs_gates: clk@8001428 {
			compatible = "allwinner,sun9i-a80-apbs-gates-clk";
			reg = <0x08001428 0x4>;
			#clock-cells = <1>;
			clocks = <&apbs>;
			clock-indices = <0>, <1>,
					<2>, <3>,
					<4>, <5>,
					<6>, <7>,
					<12>, <13>,
					<16>, <17>,
					<18>, <20>;
			clock-output-names = "apbs_pio", "apbs_ir",
					"apbs_timer", "apbs_rsb",
					"apbs_uart", "apbs_1wire",
					"apbs_i2c0", "apbs_i2c1",
					"apbs_ps2_0", "apbs_ps2_1",
					"apbs_dma", "apbs_i2s0",
					"apbs_i2s1", "apbs_twd";
		};

		r_1wire_clk: clk@8001450 {
			reg = <0x08001450 0x4>;
			#clock-cells = <0>;
			compatible = "allwinner,sun4i-a10-mod0-clk";
			clocks = <&osc32k>, <&osc24M>;
			clock-output-names = "r_1wire";
		};

		r_ir_clk: clk@8001454 {
			reg = <0x08001454 0x4>;
			#clock-cells = <0>;
			compatible = "allwinner,sun4i-a10-mod0-clk";
			clocks = <&osc32k>, <&osc24M>;
			clock-output-names = "r_ir";
		};
	};

	de: display-engine {
		compatible = "allwinner,sun9i-a80-display-engine";
		allwinner,pipelines = <&fe0>, <&fe1>;
		status = "disabled";
	};

	soc@20000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		/*
		 * map 64 bit address range down to 32 bits,
		 * as the peripherals are all under 512MB.
		 */
		ranges = <0 0 0 0x20000000>;

		sram_b: sram@20000 {
			/* 256 KiB secure SRAM at 0x20000 */
			compatible = "mmio-sram";
			reg = <0x00020000 0x40000>;

			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x00020000 0x40000>;

			smp-sram@1000 {
				/*
				 * This is checked by BROM to determine if
				 * cpu0 should jump to SMP entry vector
				 */
				compatible = "allwinner,sun9i-a80-smp-sram";
				reg = <0x1000 0x8>;
			};
		};

		gmac: ethernet@830000 {
			compatible = "allwinner,sun7i-a20-gmac";
			reg = <0x00830000 0x1054>;
			interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq";
			clocks = <&ccu CLK_BUS_GMAC>, <&gmac_tx_clk>;
			clock-names = "stmmaceth", "allwinner_gmac_tx";
			resets = <&ccu RST_BUS_GMAC>;
			reset-names = "stmmaceth";
			snps,pbl = <2>;
			snps,fixed-burst;
			snps,force_sf_dma_mode;
			status = "disabled";

			mdio: mdio {
				compatible = "snps,dwmac-mdio";
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};

		ehci0: usb@a00000 {
			compatible = "allwinner,sun9i-a80-ehci", "generic-ehci";
			reg = <0x00a00000 0x100>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&usb_clocks CLK_BUS_HCI0>;
			resets = <&usb_clocks RST_USB0_HCI>;
			phys = <&usbphy1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci0: usb@a00400 {
			compatible = "allwinner,sun9i-a80-ohci", "generic-ohci";
			reg = <0x00a00400 0x100>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&usb_clocks CLK_BUS_HCI0>,
				 <&usb_clocks CLK_USB_OHCI0>;
			resets = <&usb_clocks RST_USB0_HCI>;
			phys = <&usbphy1>;
			phy-names = "usb";
			status = "disabled";
		};

		usbphy1: phy@a00800 {
			compatible = "allwinner,sun9i-a80-usb-phy";
			reg = <0x00a00800 0x4>;
			clocks = <&usb_clocks CLK_USB0_PHY>;
			clock-names = "phy";
			resets = <&usb_clocks RST_USB0_PHY>;
			reset-names = "phy";
			status = "disabled";
			#phy-cells = <0>;
		};

		ehci1: usb@a01000 {
			compatible = "allwinner,sun9i-a80-ehci", "generic-ehci";
			reg = <0x00a01000 0x100>;
			interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&usb_clocks CLK_BUS_HCI1>;
			resets = <&usb_clocks RST_USB1_HCI>;
			phys = <&usbphy2>;
			phy-names = "usb";
			status = "disabled";
		};

		usbphy2: phy@a01800 {
			compatible = "allwinner,sun9i-a80-usb-phy";
			reg = <0x00a01800 0x4>;
			clocks = <&usb_clocks CLK_USB1_PHY>,
				 <&usb_clocks CLK_USB_HSIC>,
				 <&usb_clocks CLK_USB1_HSIC>;
			clock-names = "phy",
				      "hsic_12M",
				      "hsic_480M";
			resets = <&usb_clocks RST_USB1_PHY>,
				 <&usb_clocks RST_USB1_HSIC>;
			reset-names = "phy",
				      "hsic";
			status = "disabled";
			#phy-cells = <0>;
			/* usb1 is always used with HSIC */
			phy_type = "hsic";
		};

		ehci2: usb@a02000 {
			compatible = "allwinner,sun9i-a80-ehci", "generic-ehci";
			reg = <0x00a02000 0x100>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&usb_clocks CLK_BUS_HCI2>;
			resets = <&usb_clocks RST_USB2_HCI>;
			phys = <&usbphy3>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci2: usb@a02400 {
			compatible = "allwinner,sun9i-a80-ohci", "generic-ohci";
			reg = <0x00a02400 0x100>;
			interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&usb_clocks CLK_BUS_HCI2>,
				 <&usb_clocks CLK_USB_OHCI2>;
			resets = <&usb_clocks RST_USB2_HCI>;
			phys = <&usbphy3>;
			phy-names = "usb";
			status = "disabled";
		};

		usbphy3: phy@a02800 {
			compatible = "allwinner,sun9i-a80-usb-phy";
			reg = <0x00a02800 0x4>;
			clocks = <&usb_clocks CLK_USB2_PHY>,
				 <&usb_clocks CLK_USB_HSIC>,
				 <&usb_clocks CLK_USB2_HSIC>;
			clock-names = "phy",
				      "hsic_12M",
				      "hsic_480M";
			resets = <&usb_clocks RST_USB2_PHY>,
				 <&usb_clocks RST_USB2_HSIC>;
			reset-names = "phy",
				      "hsic";
			status = "disabled";
			#phy-cells = <0>;
		};

		usb_clocks: clock@a08000 {
			compatible = "allwinner,sun9i-a80-usb-clks";
			reg = <0x00a08000 0x8>;
			clocks = <&ccu CLK_BUS_USB>, <&osc24M>;
			clock-names = "bus", "hosc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		cpucfg@1700000 {
			compatible = "allwinner,sun9i-a80-cpucfg";
			reg = <0x01700000 0x100>;
		};

		crypto: crypto@1c02000 {
			compatible = "allwinner,sun9i-a80-crypto";
			reg = <0x01c02000 0x1000>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&ccu RST_BUS_SS>;
			clocks = <&ccu CLK_BUS_SS>, <&ccu CLK_SS>;
			clock-names = "bus", "mod";
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun9i-a80-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&mmc_config_clk 0>, <&ccu CLK_MMC0>,
				 <&ccu CLK_MMC0_OUTPUT>,
				 <&ccu CLK_MMC0_SAMPLE>;
			clock-names = "ahb", "mmc", "output", "sample";
			resets = <&mmc_config_clk 0>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun9i-a80-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&mmc_config_clk 1>, <&ccu CLK_MMC1>,
				 <&ccu CLK_MMC1_OUTPUT>,
				 <&ccu CLK_MMC1_SAMPLE>;
			clock-names = "ahb", "mmc", "output", "sample";
			resets = <&mmc_config_clk 1>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun9i-a80-mmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&mmc_config_clk 2>, <&ccu CLK_MMC2>,
				 <&ccu CLK_MMC2_OUTPUT>,
				 <&ccu CLK_MMC2_SAMPLE>;
			clock-names = "ahb", "mmc", "output", "sample";
			resets = <&mmc_config_clk 2>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc3: mmc@1c12000 {
			compatible = "allwinner,sun9i-a80-mmc";
			reg = <0x01c12000 0x1000>;
			clocks = <&mmc_config_clk 3>, <&ccu CLK_MMC3>,
				 <&ccu CLK_MMC3_OUTPUT>,
				 <&ccu CLK_MMC3_SAMPLE>;
			clock-names = "ahb", "mmc", "output", "sample";
			resets = <&mmc_config_clk 3>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 63 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc_config_clk: clk@1c13000 {
			compatible = "allwinner,sun9i-a80-mmc-config-clk";
			reg = <0x01c13000 0x10>;
			clocks = <&ccu CLK_BUS_MMC>;
			resets = <&ccu RST_BUS_MMC>;
			#clock-cells = <1>;
			#reset-cells = <1>;
			clock-output-names = "mmc0_config", "mmc1_config",
					     "mmc2_config", "mmc3_config";
		};

		gic: interrupt-controller@1c41000 {
			compatible = "arm,gic-400";
			reg = <0x01c41000 0x1000>,
			      <0x01c42000 0x2000>,
			      <0x01c44000 0x2000>,
			      <0x01c46000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		cci: cci@1c90000 {
			compatible = "arm,cci-400";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x01c90000 0x1000>;
			ranges = <0x0 0x01c90000 0x10000>;

			cci_control0: slave-if@4000 {
				compatible = "arm,cci-400-ctrl-if";
				interface-type = "ace";
				reg = <0x4000 0x1000>;
			};

			cci_control1: slave-if@5000 {
				compatible = "arm,cci-400-ctrl-if";
				interface-type = "ace";
				reg = <0x5000 0x1000>;
			};

			pmu@9000 {
				 compatible = "arm,cci-400-pmu,r1";
				 reg = <0x9000 0x5000>;
				 interrupts = <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
					      <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
					      <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
					      <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
					      <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		de_clocks: clock@3000000 {
			compatible = "allwinner,sun9i-a80-de-clks";
			reg = <0x03000000 0x30>;
			clocks = <&ccu CLK_DE>,
				 <&ccu CLK_SDRAM>,
				 <&ccu CLK_BUS_DE>;
			clock-names = "mod",
				      "dram",
				      "bus";
			resets = <&ccu RST_BUS_DE>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		fe0: display-frontend@3100000 {
			compatible = "allwinner,sun9i-a80-display-frontend";
			reg = <0x03100000 0x40000>;
			interrupts = <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_FE0>, <&de_clocks CLK_FE0>,
				 <&de_clocks CLK_DRAM_FE0>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&de_clocks RST_FE0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe0_out: port@1 {
					reg = <1>;

					fe0_out_deu0: endpoint {
						remote-endpoint = <&deu0_in_fe0>;
					};
				};
			};
		};

		fe1: display-frontend@3140000 {
			compatible = "allwinner,sun9i-a80-display-frontend";
			reg = <0x03140000 0x40000>;
			interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_FE1>, <&de_clocks CLK_FE1>,
				 <&de_clocks CLK_DRAM_FE1>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&de_clocks RST_FE0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe1_out: port@1 {
					reg = <1>;

					fe1_out_deu1: endpoint {
						remote-endpoint = <&deu1_in_fe1>;
					};
				};
			};
		};

		be0: display-backend@3200000 {
			compatible = "allwinner,sun9i-a80-display-backend";
			reg = <0x03200000 0x40000>;
			interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_BE0>, <&de_clocks CLK_BE0>,
				 <&de_clocks CLK_DRAM_BE0>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&de_clocks RST_BE0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be0_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					be0_in_deu0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&deu0_out_be0>;
					};

					be0_in_deu1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&deu1_out_be0>;
					};
				};

				be0_out: port@1 {
					reg = <1>;

					be0_out_drc0: endpoint {
						remote-endpoint = <&drc0_in_be0>;
					};
				};
			};
		};

		be1: display-backend@3240000 {
			compatible = "allwinner,sun9i-a80-display-backend";
			reg = <0x03240000 0x40000>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_BE1>, <&de_clocks CLK_BE1>,
				 <&de_clocks CLK_DRAM_BE1>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&de_clocks RST_BE1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					be1_in_deu0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&deu0_out_be1>;
					};

					be1_in_deu1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&deu1_out_be1>;
					};
				};

				be1_out: port@1 {
					reg = <1>;

					be1_out_drc1: endpoint {
						remote-endpoint = <&drc1_in_be1>;
					};
				};
			};
		};

		deu0: deu@3300000 {
			compatible = "allwinner,sun9i-a80-deu";
			reg = <0x03300000 0x40000>;
			interrupts = <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_DEU0>,
				 <&de_clocks CLK_IEP_DEU0>,
				 <&de_clocks CLK_DRAM_DEU0>;
			clock-names = "ahb",
				      "mod",
				      "ram";
			resets = <&de_clocks RST_DEU0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				deu0_in: port@0 {
					reg = <0>;

					deu0_in_fe0: endpoint {
						remote-endpoint = <&fe0_out_deu0>;
					};
				};

				deu0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					deu0_out_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_in_deu0>;
					};

					deu0_out_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_in_deu0>;
					};
				};
			};
		};

		deu1: deu@3340000 {
			compatible = "allwinner,sun9i-a80-deu";
			reg = <0x03340000 0x40000>;
			interrupts = <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_DEU1>,
				 <&de_clocks CLK_IEP_DEU1>,
				 <&de_clocks CLK_DRAM_DEU1>;
			clock-names = "ahb",
				      "mod",
				      "ram";
			resets = <&de_clocks RST_DEU1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				deu1_in: port@0 {
					reg = <0>;

					deu1_in_fe1: endpoint {
						remote-endpoint = <&fe1_out_deu1>;
					};
				};

				deu1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					deu1_out_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_in_deu1>;
					};

					deu1_out_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_in_deu1>;
					};
				};
			};
		};

		drc0: drc@3400000 {
			compatible = "allwinner,sun9i-a80-drc";
			reg = <0x03400000 0x40000>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_DRC0>,
				 <&de_clocks CLK_IEP_DRC0>,
				 <&de_clocks CLK_DRAM_DRC0>;
			clock-names = "ahb",
				      "mod",
				      "ram";
			resets = <&de_clocks RST_DRC0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				drc0_in: port@0 {
					reg = <0>;

					drc0_in_be0: endpoint {
						remote-endpoint = <&be0_out_drc0>;
					};
				};

				drc0_out: port@1 {
					reg = <1>;

					drc0_out_tcon0: endpoint {
						remote-endpoint = <&tcon0_in_drc0>;
					};
				};
			};
		};

		drc1: drc@3440000 {
			compatible = "allwinner,sun9i-a80-drc";
			reg = <0x03440000 0x40000>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&de_clocks CLK_BUS_DRC1>,
				 <&de_clocks CLK_IEP_DRC1>,
				 <&de_clocks CLK_DRAM_DRC1>;
			clock-names = "ahb",
				      "mod",
				      "ram";
			resets = <&de_clocks RST_DRC1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				drc1_in: port@0 {
					reg = <0>;

					drc1_in_be1: endpoint {
						remote-endpoint = <&be1_out_drc1>;
					};
				};

				drc1_out: port@1 {
					reg = <1>;

					drc1_out_tcon1: endpoint {
						remote-endpoint = <&tcon1_in_drc1>;
					};
				};
			};
		};

		tcon0: lcd-controller@3c00000 {
			compatible = "allwinner,sun9i-a80-tcon-lcd";
			reg = <0x03c00000 0x10000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_LCD0>, <&ccu CLK_LCD0>;
			clock-names = "ahb", "tcon-ch0";
			resets = <&ccu RST_BUS_LCD0>,
				 <&ccu RST_BUS_EDP>,
				 <&ccu RST_BUS_LVDS>;
			reset-names = "lcd",
				      "edp",
				      "lvds";
			clock-output-names = "tcon0-pixel-clock";
			#clock-cells = <0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					reg = <0>;

					tcon0_in_drc0: endpoint {
						remote-endpoint = <&drc0_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					reg = <1>;
				};
			};
		};

		tcon1: lcd-controller@3c10000 {
			compatible = "allwinner,sun9i-a80-tcon-tv";
			reg = <0x03c10000 0x10000>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_LCD1>, <&ccu CLK_LCD1>;
			clock-names = "ahb", "tcon-ch1";
			resets = <&ccu RST_BUS_LCD1>, <&ccu RST_BUS_EDP>;
			reset-names = "lcd", "edp";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon1_in: port@0 {
					reg = <0>;

					tcon1_in_drc1: endpoint {
						remote-endpoint = <&drc1_out_tcon1>;
					};
				};

				tcon1_out: port@1 {
					reg = <1>;
				};
			};
		};

		ccu: clock@6000000 {
			compatible = "allwinner,sun9i-a80-ccu";
			reg = <0x06000000 0x800>;
			clocks = <&osc24M>, <&osc32k>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		timer@6000c00 {
			compatible = "allwinner,sun4i-a10-timer";
			reg = <0x06000c00 0xa0>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;

			clocks = <&osc24M>;
		};

		wdt: watchdog@6000ca0 {
			compatible = "allwinner,sun6i-a31-wdt";
			reg = <0x06000ca0 0x20>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		pio: pinctrl@6000800 {
			compatible = "allwinner,sun9i-a80-pinctrl";
			reg = <0x06000800 0x400>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_PIO>, <&osc24M>, <&osc32k>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			gmac_rgmii_pins: gmac-rgmii-pins {
				pins = "PA0", "PA1", "PA2", "PA3", "PA4", "PA5",
				       "PA7", "PA8", "PA9", "PA10", "PA12",
				       "PA13", "PA15", "PA16", "PA17";
				function = "gmac";
				/*
				 * data lines in RGMII mode use DDR mode
				 * and need a higher signal drive strength
				 */
				drive-strength = <40>;
			};

			i2c3_pins: i2c3-pins {
				pins = "PG10", "PG11";
				function = "i2c3";
			};

			lcd0_rgb888_pins: lcd0-rgb888-pins {
				pins = "PD0", "PD1", "PD2", "PD3",
				       "PD4", "PD5", "PD6", "PD7",
				       "PD8", "PD9", "PD10", "PD11",
				       "PD12", "PD13", "PD14", "PD15",
				       "PD16", "PD17", "PD18", "PD19",
				       "PD20", "PD21", "PD22", "PD23",
				       "PD24", "PD25", "PD26", "PD27";
				function = "lcd0";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1" ,"PF2", "PF3",
				       "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc1_pins: mmc1-pins {
				pins = "PG0", "PG1" ,"PG2", "PG3",
						 "PG4", "PG5";
				function = "mmc1";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_8bit_pins: mmc2-8bit-pins {
				pins = "PC6", "PC7", "PC8", "PC9",
				       "PC10", "PC11", "PC12",
				       "PC13", "PC14", "PC15",
				       "PC16";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			uart0_ph_pins: uart0-ph-pins {
				pins = "PH12", "PH13";
				function = "uart0";
			};

			uart4_pins: uart4-pins {
				pins = "PG12", "PG13", "PG14", "PG15";
				function = "uart4";
			};
		};

		uart0: serial@7000000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x07000000 0x400>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART0>;
			resets = <&ccu RST_BUS_UART0>;
			status = "disabled";
		};

		uart1: serial@7000400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x07000400 0x400>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART1>;
			resets = <&ccu RST_BUS_UART1>;
			status = "disabled";
		};

		uart2: serial@7000800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x07000800 0x400>;
			interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART2>;
			resets = <&ccu RST_BUS_UART2>;
			status = "disabled";
		};

		uart3: serial@7000c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x07000c00 0x400>;
			interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART3>;
			resets = <&ccu RST_BUS_UART3>;
			status = "disabled";
		};

		uart4: serial@7001000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x07001000 0x400>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART4>;
			resets = <&ccu RST_BUS_UART4>;
			status = "disabled";
		};

		uart5: serial@7001400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x07001400 0x400>;
			interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART5>;
			resets = <&ccu RST_BUS_UART5>;
			status = "disabled";
		};

		i2c0: i2c@7002800 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x07002800 0x400>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C0>;
			resets = <&ccu RST_BUS_I2C0>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@7002c00 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x07002c00 0x400>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C1>;
			resets = <&ccu RST_BUS_I2C1>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@7003000 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x07003000 0x400>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C2>;
			resets = <&ccu RST_BUS_I2C2>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c3: i2c@7003400 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x07003400 0x400>;
			interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C3>;
			resets = <&ccu RST_BUS_I2C3>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c4: i2c@7003800 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x07003800 0x400>;
			interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C4>;
			resets = <&ccu RST_BUS_I2C4>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		r_wdt: watchdog@8001000 {
			compatible = "allwinner,sun6i-a31-wdt";
			reg = <0x08001000 0x20>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		prcm@8001400 {
			compatible = "allwinner,sun9i-a80-prcm";
			reg = <0x08001400 0x200>;
		};

		apbs_rst: reset@80014b0 {
			reg = <0x080014b0 0x4>;
			compatible = "allwinner,sun6i-a31-clock-reset";
			#reset-cells = <1>;
		};

		nmi_intc: interrupt-controller@80015a0 {
			compatible = "allwinner,sun9i-a80-nmi";
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x080015a0 0xc>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		};

		r_ir: ir@8002000 {
			compatible = "allwinner,sun6i-a31-ir";
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_ir_pins>;
			clocks = <&apbs_gates 1>, <&r_ir_clk>;
			clock-names = "apb", "ir";
			resets = <&apbs_rst 1>;
			reg = <0x08002000 0x40>;
			status = "disabled";
		};

		r_uart: serial@8002800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x08002800 0x400>;
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&apbs_gates 4>;
			resets = <&apbs_rst 4>;
			status = "disabled";
		};

		r_pio: pinctrl@8002c00 {
			compatible = "allwinner,sun9i-a80-r-pinctrl";
			reg = <0x08002c00 0x400>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&apbs_gates 0>, <&osc24M>, <&osc32k>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			r_ir_pins: r-ir-pins {
				pins = "PL6";
				function = "s_cir_rx";
			};

			r_rsb_pins: r-rsb-pins {
				pins = "PN0", "PN1";
				function = "s_rsb";
				drive-strength = <20>;
				bias-pull-up;
			};
		};

		r_rsb: rsb@8003400 {
			compatible = "allwinner,sun8i-a23-rsb";
			reg = <0x08003400 0x400>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&apbs_gates 3>;
			clock-frequency = <3000000>;
			resets = <&apbs_rst 3>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_rsb_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};
};
