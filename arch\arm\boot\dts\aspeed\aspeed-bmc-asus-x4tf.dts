// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2024 ASUS Corp.

/dts-v1/;

#include "aspeed-g6.dtsi"
#include "aspeed-g6-pinctrl.dtsi"
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "ASUS-X4TF";
	compatible = "asus,x4tf-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		stdout-path = "serial4:115200n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		video_engine_memory: video {
			size = <0x04000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc0 0>, <&adc0 1>, <&adc0 2>, <&adc0 3>,
				<&adc0 4>, <&adc0 5>, <&adc0 6>, <&adc0 7>,
				<&adc1 0>, <&adc1 1>, <&adc1 2>, <&adc1 3>,
				<&adc1 4>, <&adc1 5>, <&adc1 6>, <&adc1 7>;
	};

	leds {
		compatible = "gpio-leds";

		led-heartbeat {
			gpios = <&gpio0 ASPEED_GPIO(P, 7) GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};

		led-uid {
			gpios = <&gpio0 ASPEED_GPIO(P, 1) (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN)>;
			default-state = "off";
		};

		led-status_Y {
			gpios = <&gpio1 ASPEED_GPIO(B, 1) GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		led-sys_boot_status {
			gpios = <&gpio1 ASPEED_GPIO(B, 0) GPIO_ACTIVE_LOW>;
			default-state = "off";
		};
	};
};

&adc0 {
	vref = <2500>;
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default &pinctrl_adc1_default
		&pinctrl_adc2_default &pinctrl_adc3_default
		&pinctrl_adc4_default &pinctrl_adc5_default
		&pinctrl_adc6_default &pinctrl_adc7_default>;
};

&adc1 {
	vref = <2500>;
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc9_default
		&pinctrl_adc10_default &pinctrl_adc11_default
		&pinctrl_adc12_default &pinctrl_adc13_default
		&pinctrl_adc14_default &pinctrl_adc15_default>;
};

&peci0 {
	status = "okay";
};

&lpc_snoop {
	snoop-ports = <0x80>;
	status = "okay";
};

&mac2 {
	status = "okay";
	phy-mode = "rmii";
	use-ncsi;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
};

&mac3 {
	status = "okay";
	phy-mode = "rmii";
	use-ncsi;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
};

&fmc {
	status = "okay";

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-64.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		label = "bios";
		spi-max-frequency = <50000000>;
	};
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";

	temperature-sensor@48 {
		compatible = "ti,tmp75";
		reg = <0x48>;
	};

	temperature-sensor@49 {
		compatible = "ti,tmp75";
		reg = <0x49>;
	};

	pca9555_4_20: gpio@20 {
		compatible = "nxp,pca9555";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	pca9555_4_22: gpio@22 {
		compatible = "nxp,pca9555";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	pca9555_4_24: gpio@24 {
		compatible = "nxp,pca9555";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names =
		/*A0 - A3 0*/	"", "STRAP_BMC_BATTERY_GPIO1", "", "",
		/*A4 - A7 4*/	"", "", "", "",
		/*B0 - B7 8*/	"", "", "", "", "", "", "", "";
	};

	pca9555_4_26: gpio@26 {
		compatible = "nxp,pca9555";
		reg = <0x26>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		status = "okay";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;

		channel_1: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		channel_2: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_3: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_4: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c5 {
	status = "okay";

	pca9555_5_24: gpio@24 {
		compatible = "nxp,pca9555";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	i2c-mux@70  {
		compatible = "nxp,pca9546";
		status = "okay";
		reg = <0x70 >;
		#address-cells = <1>;
		#size-cells = <0>;

		channel_5: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			pca9555_5_5_20: gpio@20 {
				compatible = "nxp,pca9555";
				reg = <0x20>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio-line-names =
					"", "", "", "", "", "", "", "",
					"", "", "SYS_FAN6", "SYS_FAN5",
					"SYS_FAN4", "SYS_FAN3",
					"SYS_FAN2", "SYS_FAN1";
			};

			pca9555_5_5_21: gpio@21 {
				compatible = "nxp,pca9555";
				reg = <0x21>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			power-monitor@44 {
				compatible = "ti,ina219";
				reg = <0x44>;
				shunt-resistor = <2>;
			};
		};

		channel_6: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_7: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_8: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c6 {
	status = "okay";

	pca9555_6_27: gpio@27 {
		compatible = "nxp,pca9555";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	pca9555_6_20: gpio@20 {
		compatible = "nxp,pca9555";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names =
		/*A0 0*/	"", "", "", "", "", "", "", "",
		/*B0 8*/	"Drive_NVMe1", "Drive_NVMe2", "", "",
		/*B4 12*/	"", "", "", "";
	};

	pca9555_6_21: gpio@21 {
		compatible = "nxp,pca9555";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

&i2c7 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		status = "okay";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		idle-state = <1>;

		channel_9: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			temperature-sensor@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};

			temperature-sensor@49 {
				compatible = "ti,tmp75";
				reg = <0x49>;
			};

			power-monitor@40 {
				compatible = "ti,ina219";
				reg = <0x40>;
				shunt-resistor = <2>;
			};

			power-monitor@41 {
				compatible = "ti,ina219";
				reg = <0x41>;
				shunt-resistor = <5>;
			};
		};

		channel_10: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_11: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_12: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	i2c-mux@71 {
		compatible = "nxp,pca9546";
		status = "okay";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		channel_13: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		channel_14: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_15: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_16: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c8 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		status = "okay";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		channel_17: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		channel_18: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			temperature-sensor@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};

			power-monitor@41 {
				compatible = "ti,ina219";
				reg = <0x41>;
				shunt-resistor = <5>;
			};
		};

		channel_19: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_20: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&i2c14 {
	status = "okay";
	multi-master;

	eeprom@50 {
		compatible = "atmel,24c08";
		reg = <0x50>;
	};

	eeprom@51 {
		compatible = "atmel,24c08";
		reg = <0x51>;
	};
};

&sgpiom0 {
	status = "okay";
	ngpios = <128>;
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&sdc {
	status = "okay";
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&kcs1 {
	aspeed,lpc-io-reg = <0xca0>;
	status = "okay";
};

&kcs2 {
	aspeed,lpc-io-reg = <0xca8>;
	status = "okay";
};

&kcs3 {
	aspeed,lpc-io-reg = <0xca2>;
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&uart_routing {
	status = "okay";
};

&vhub {
	status = "okay";
};

&gpio0 {
	gpio-line-names =
	/*A0 0*/	"", "", "", "", "", "", "", "",
	/*B0 8*/	"", "", "", "", "", "", "PS_PWROK", "",
	/*C0 16*/	"", "", "", "", "", "", "", "",
	/*D0 24*/	"", "", "", "", "", "", "", "",
	/*E0 32*/	"", "", "", "", "", "", "", "",
	/*F0 40*/	"", "", "", "", "", "", "", "",
	/*G0 48*/	"", "", "", "", "", "", "", "",
	/*H0 56*/	"", "", "", "", "", "", "", "",
	/*I0 64*/	"", "", "", "", "", "", "", "",
	/*J0 72*/	"", "", "", "", "", "", "", "",
	/*K0 80*/	"", "", "", "", "", "", "", "",
	/*L0 88*/	"", "", "", "", "", "", "", "",
	/*M0 96*/	"", "", "", "", "", "", "", "",
	/*N0 104*/	"", "", "", "",
	/*N4 108*/	"POST_COMPLETE", "ESR1_GPIO_AST_SPISEL", "", "",
	/*O0 112*/	"", "", "", "", "", "", "", "",
	/*P0 120*/	"ID_BUTTON", "ID_OUT", "POWER_BUTTON", "POWER_OUT",
	/*P4 124*/	"RESET_BUTTON", "RESET_OUT", "", "HEARTBEAT",
	/*Q0 128*/	"", "", "", "", "", "", "", "",
	/*R0 136*/	"", "", "", "", "", "", "", "",
	/*S0 144*/	"", "", "", "", "", "", "", "",
	/*T0 152*/	"", "", "", "", "", "", "", "",
	/*U0 160*/	"", "", "", "", "", "", "", "",
	/*V0 168*/	"", "", "", "", "", "", "", "",
	/*W0 176*/	"", "", "", "", "", "", "", "",
	/*X0 184*/	"", "", "", "", "", "", "", "",
	/*Y0 192*/	"", "", "", "", "", "", "", "",
	/*Z0 200*/	"", "", "", "", "", "", "", "";
};
