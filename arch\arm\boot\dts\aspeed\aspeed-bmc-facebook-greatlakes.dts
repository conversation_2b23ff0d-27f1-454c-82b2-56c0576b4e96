// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2022 Facebook Inc.

/dts-v1/;
#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/leds/leds-pca955x.h>
#include <dt-bindings/i2c/i2c.h>

/ {
	model = "Facebook Greatlakes BMC";
	compatible = "facebook,greatlakes-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc0 0>, <&adc0 1>, <&adc0 2>, <&adc0 3>,
				<&adc0 4>, <&adc0 5>, <&adc0 6>, <&adc0 7>,
				<&adc1 0>, <&adc1 2>, <&adc1 3>, <&adc1 4>,
				<&adc1 5>, <&adc1 6>;
	};
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&wdt1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
	aspeed,reset-type = "soc";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;
	aspeed,ext-pulse-duration = <256>;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	no-hw-checksum;
	use-ncsi;
	mellanox,multi-host;
	ncsi-ctrl,start-redo-probe;
	ncsi-ctrl,no-channel-monitor;
	ncsi-package = <1>;
	ncsi-channel = <1>;
	ncsi-rexmit = <1>;
	ncsi-timeout = <2>;
};

&rtc {
	status = "okay";
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-rx-bus-width = <4>;
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-64.dtsi"
	};
	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "bmc2";
		spi-rx-bus-width = <4>;
		spi-max-frequency = <50000000>;
	};
};

&i2c0 {
	status = "okay";
	multi-master;
	ipmb@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c1 {
	status = "okay";
	multi-master;
	ipmb@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c2 {
	status = "okay";
	multi-master;
	ipmb@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c3 {
	status = "okay";
	multi-master;
	ipmb@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
	mctp-controller;
	temperature-sensor@1f {
		compatible = "ti,tmp421";
		reg = <0x1f>;
	};
	// NIC EEPROM
	eeprom@50 {
		compatible = "st,24c32";
		reg = <0x50>;
	};
	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};
};

&i2c9 {
	status = "okay";
	multi-master;
	ipmb@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
	eeprom@51 {
		compatible = "atmel,24c128";
		reg = <0x51>;
	};
	eeprom@54 {
		compatible = "atmel,24c128";
		reg = <0x54>;
	};
};

&i2c12 {
	status = "okay";
	temperature-sensor@4f {
		compatible = "national,lm75";
		reg = <0x4f>;
	};
};

&i2c13 {
	status = "okay";
};

&adc0 {
	status = "okay";
	pinctrl-0 = <&pinctrl_adc0_default &pinctrl_adc1_default
			&pinctrl_adc2_default &pinctrl_adc3_default
			&pinctrl_adc4_default &pinctrl_adc5_default
			&pinctrl_adc6_default &pinctrl_adc7_default>;
};

&adc1 {
	status = "okay";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc10_default
			&pinctrl_adc11_default &pinctrl_adc12_default
			&pinctrl_adc13_default &pinctrl_adc14_default>;
};


&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&gpio0 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_gpiu1_default &pinctrl_gpiu7_default>;

	gpio-line-names =
	/*A0-A7*/ "","","","","","","","",
	/*B0-B7*/ "power-bmc-nic","presence-ocp-debug",
		  "power-bmc-slot1","power-bmc-slot2",
		  "power-bmc-slot3","power-bmc-slot4","","",
	/*C0-C7*/ "presence-ocp-nic","","","reset-cause-nic-primary",
		  "reset-cause-nic-secondary","","","",
	/*D0-D7*/ "","","","","","","","",
	/*E0-E7*/ "","","","","","","","",
	/*F0-F7*/ "slot1-bmc-reset-button","slot2-bmc-reset-button",
		  "slot3-bmc-reset-button","slot4-bmc-reset-button",
		  "","","","presence-emmc",
	/*G0-G7*/ "","","","","","","","",
	/*H0-H7*/ "","","","",
		  "presence-mb-slot1","presence-mb-slot2",
		  "presence-mb-slot3","presence-mb-slot4",
	/*I0-I7*/ "","","","","","","bb-bmc-button","",
	/*J0-J7*/ "","","","","","","","",
	/*K0-K7*/ "","","","","","","","",
	/*L0-L7*/ "","","","","","","","",
	/*M0-M7*/ "","power-nic-bmc-enable","","usb-bmc-enable","","reset-cause-usb-hub","","",
	/*N0-N7*/ "","","","","bmc-ready","","","",
	/*O0-O7*/ "","","","","","","fan0-bmc-cpld-enable","fan1-bmc-cpld-enable",
	/*P0-P7*/ "fan2-bmc-cpld-enable","fan3-bmc-cpld-enable",
		  "reset-cause-pcie-slot1","reset-cause-pcie-slot2",
		  "reset-cause-pcie-slot3","reset-cause-pcie-slot4","","",
	/*Q0-Q7*/ "","","","","","","","",
	/*R0-R7*/ "","","","","","","","",
	/*S0-S7*/ "","","power-p5v-usb","presence-bmc-tpm","","","","",
	/*T0-T7*/ "","","","","","","","",
	/*U0-U7*/ "","","","","","","","GND",
	/*V0-V7*/ "bmc-slot1-ac-button","bmc-slot2-ac-button",
		  "bmc-slot3-ac-button","bmc-slot4-ac-button",
		  "","","","",
	/*W0-W7*/ "","","","","","","","",
	/*X0-X7*/ "","","","","","","","",
	/*Y0-Y7*/ "","","","reset-cause-emmc","","","","",
	/*Z0-Z7*/ "","","","","","","","";
};

&gpio1 {
	gpio-line-names =
	/*18A0-18A7*/ "","","","","","","","",
	/*18B0-18B7*/ "","","","","","","","",
	/*18C0-18C7*/ "","","","","","","","",
	/*18D0-18D7*/ "","","","","","","","",
	/*18E0-18E3*/ "","","","","","","","";
};
