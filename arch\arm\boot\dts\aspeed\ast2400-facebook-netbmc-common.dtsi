// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2020 Facebook Inc.
/dts-v1/;

#include "aspeed-g4.dtsi"

/ {
	aliases {
		/*
		 * Override the default uart aliases to avoid breaking
		 * the legacy applications.
		 */
		serial0 = &uart5;
		serial1 = &uart1;
		serial2 = &uart3;
		serial3 = &uart4;
	};

	memory@40000000 {
		reg = <0x40000000 0x20000000>;
	};
};

&wdt1 {
	status = "okay";
	aspeed,reset-type = "system";
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "spi0.0";
#include "facebook-bmc-flash-layout.dtsi"
	};
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default>;
};

&uart3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd3_default
		     &pinctrl_rxd3_default>;
};

&uart4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd4_default
		     &pinctrl_rxd4_default
		     &pinctrl_ndts4_default>;
};

&uart5 {
	status = "okay";
};

&mac1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&vhub {
	status = "okay";
};

&adc {
	status = "okay";
};
