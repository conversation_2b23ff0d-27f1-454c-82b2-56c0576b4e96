// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Device Tree Bindings for Cisco Meraki MX64 with B0+ SoC.
 *
 * Copyright (C) 2020-2021 <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "bcm958625-meraki-kingpin.dtsi"

/ {
	model = "Cisco Meraki MX64";
	compatible = "meraki,mx64", "brcm,bcm58625", "brcm,nsp";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x80000000>;
	};
};
