// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2023 IBM Corp.

&fsim0 {
	status = "okay";

	#address-cells = <2>;
	#size-cells = <0>;

	cfam-reset-gpios = <&gpio0 ASPEED_GPIO(Q, 0) GPIO_ACTIVE_HIGH>;

	cfam@0,0 {
		reg = <0 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <0>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam0_i2c0: i2c-bus@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;	/* OMI01 */
			};

			cfam0_i2c1: i2c-bus@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;	/* OMI23 */
			};

			cfam0_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */
			};

			cfam0_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */
			};

			cfam0_i2c12: i2c-bus@c {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <12>;	/* OP4A */
			};

			cfam0_i2c13: i2c-bus@d {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <13>;	/* OP4B */
			};

			cfam0_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */
			};

			cfam0_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam0_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam0_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam0_spi2: spi@40 {
				reg = <0x40>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam0_spi3: spi@60 {
				reg = <0x60>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ0: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub0: hub@3400 {
			#interrupt-cells = <1>;
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;
			interrupt-controller;
		};
	};
};

&fsi_hub0 {
	cfam@1,0 {
		reg = <1 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <1>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam1_i2c2: i2c-bus@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <2>;	/* OMI45 */
			};

			cfam1_i2c3: i2c-bus@3 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <3>;	/* OMI67 */
			};

			cfam1_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */
			};

			cfam1_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */
			};

			cfam1_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */
			};

			cfam1_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */
			};

			cfam1_i2c16: i2c-bus@10 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <16>;	/* OP6A */
			};

			cfam1_i2c17: i2c-bus@11 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <17>;	/* OP6B */
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam1_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam1_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam1_spi2: spi@40 {
				reg = <0x40>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam1_spi3: spi@60 {
				reg = <0x60>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ1: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub1: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};
};

/* Legacy OCC numbering (to get rid of when userspace is fixed) */
&fsi_occ0 {
	reg = <1>;
};

&fsi_occ1 {
	reg = <2>;
};

/ {
	aliases {
		i2c100 = &cfam0_i2c0;
		i2c101 = &cfam0_i2c1;
		i2c110 = &cfam0_i2c10;
		i2c111 = &cfam0_i2c11;
		i2c112 = &cfam0_i2c12;
		i2c113 = &cfam0_i2c13;
		i2c114 = &cfam0_i2c14;
		i2c115 = &cfam0_i2c15;
		i2c202 = &cfam1_i2c2;
		i2c203 = &cfam1_i2c3;
		i2c210 = &cfam1_i2c10;
		i2c211 = &cfam1_i2c11;
		i2c214 = &cfam1_i2c14;
		i2c215 = &cfam1_i2c15;
		i2c216 = &cfam1_i2c16;
		i2c217 = &cfam1_i2c17;

		spi10 = &cfam0_spi0;
		spi11 = &cfam0_spi1;
		spi12 = &cfam0_spi2;
		spi13 = &cfam0_spi3;
		spi20 = &cfam1_spi0;
		spi21 = &cfam1_spi1;
		spi22 = &cfam1_spi2;
		spi23 = &cfam1_spi3;
	};
};
