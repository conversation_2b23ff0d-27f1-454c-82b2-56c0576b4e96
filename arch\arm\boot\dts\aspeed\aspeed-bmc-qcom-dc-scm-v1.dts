// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright (c) 2021-2022 Qualcomm Innovation Center, Inc. All rights reserved.

/dts-v1/;

#include "aspeed-g6.dtsi"

/ {
	model = "Qualcomm DC-SCM V1 BMC";
	compatible = "qcom,dc-scm-v1-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};
};

&mdio3 {
	status = "okay";

	ethphy3: ethernet-phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <1>;
	};
};

&mac2 {
	status = "okay";

	/* Bootloader sets up the MAC to insert delay */
	phy-mode = "rgmii";
	phy-handle = <&ethphy3>;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii3_default>;
};

&mac3 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;

	use-ncsi;
};

&rtc {
	status = "okay";
};

&fmc {
	status = "okay";

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <133000000>;
#include "openbmc-flash-layout-64.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
		spi-max-frequency = <133000000>;
#include "openbmc-flash-layout-64-alt.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bios";
		spi-max-frequency = <133000000>;
	};
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"BMC_FLASH_MUX_SEL","","","","","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","","","","","",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"BMC_FWSPI_RST_N","","GPIO_1_BMC_3V3","","","","","",
	/*O0-O7*/	"JTAG_MUX_A","JTAG_MUX_B","","","","","","",
	/*P0-P7*/	"","","","","","","","",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","","","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","SCMFPGA_SPARE_GPIO1_3V3",
			"SCMFPGA_SPARE_GPIO2_3V3","SCMFPGA_SPARE_GPIO3_3V3",
			"SCMFPGA_SPARE_GPIO4_3V3","SCMFPGA_SPARE_GPIO5_3V3",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","",
	/*AA0-AA7*/	"","","","","","","","",
	/*AB0-AB7*/	"","","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";
};

&gpio1 {
	gpio-line-names =
	/*A0-A7*/	"GPI_1_BMC_1V8","","","","","",
			"SCMFPGA_SPARE_GPIO1_1V8","SCMFPGA_SPARE_GPIO2_1V8",
	/*B0-B7*/	"SCMFPGA_SPARE_GPIO3_1V8","SCMFPGA_SPARE_GPIO4_1V8",
			"SCMFPGA_SPARE_GPIO5_1V8","","","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","BMC_SPI1_RST_N","BIOS_FLASH_MUX_SEL","",
			"","TPM2_PIRQ_N","TPM2_RST_N","",
	/*E0-E7*/	"","","","","","","","";
};

&i2c2 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&i2c14 {
	status = "okay";
};

&i2c15 {
	status = "okay";
};

&vhub {
	status = "okay";
};
