/*
 * Copyright (C) 2017 Broadcom
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the ISC license.
 */

/dts-v1/;

#include "bcm53573.dtsi"

/ {
	compatible = "brcm,bcm947189acdbmr", "brcm,bcm47189", "brcm,bcm53573";
	model = "Broadcom BCM947189ACDBMR";

	chosen {
		bootargs = "console=ttyS0,115200 earlycon";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x08000000>;
	};

	leds {
		compatible = "gpio-leds";

		led-wps {
			label = "bcm53xx:blue:wps";
			gpios = <&chipcommon 10 GPIO_ACTIVE_HIGH>;
		};

		led-5ghz {
			label = "bcm53xx:blue:5ghz";
			gpios = <&chipcommon 11 GPIO_ACTIVE_HIGH>;
		};

		led-2ghz {
			label = "bcm53xx:blue:2ghz";
			gpios = <&chipcommon 12 GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-restart {
			label = "Reset";
			linux,code = <KEY_RESTART>;
			gpios = <&chipcommon 7 GPIO_ACTIVE_HIGH>;
		};

		button-wps {
			label = "WPS";
			linux,code = <KEY_WPS_BUTTON>;
			gpios = <&chipcommon 9 GPIO_ACTIVE_LOW>;
		};
	};

	spi {
		compatible = "spi-gpio";
		num-chipselects = <1>;
		sck-gpios = <&chipcommon 21 0>;
		miso-gpios = <&chipcommon 22 0>;
		mosi-gpios = <&chipcommon 23 0>;
		cs-gpios = <&chipcommon 24 0>;
		#address-cells = <1>;
		#size-cells = <0>;

		/* External BCM6802 MoCA chip is connected */
	};
};

&pcie0 {
	ranges = <0x00000000 0 0 0 0 0x00100000>;
	#address-cells = <3>;
	#size-cells = <2>;

	bridge@0,0,0 {
		reg = <0x0000 0 0 0 0>;
		ranges = <0x00000000 0 0 0 0 0 0 0x00100000>;
		#address-cells = <3>;
		#size-cells = <2>;

		wifi@0,1,0 {
			reg = <0x0000 0 0 0 0>;
			ranges = <0x00000000 0 0 0 0x00100000>;
			#address-cells = <1>;
			#size-cells = <1>;
		};
	};
};

&usb2 {
	vcc-gpio = <&chipcommon 8 GPIO_ACTIVE_HIGH>;
};
