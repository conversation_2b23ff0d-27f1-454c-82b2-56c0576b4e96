// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

#include "aspeed-g5.dtsi"

/ {
	model = "AST2500 EVB";
	compatible = "aspeed,ast2500-evb", "aspeed,ast2500";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=tty0 console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-max-frequency = <100000000>;
	};
};

&spi2 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c3 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c08";
		reg = <0x50>;
		pagesize = <16>;
	};
};

&i2c7 {
	status = "okay";

	lm75@4d {
		compatible = "national,lm75";
		reg = <0x4d>;
	};
};

&sdmmc {
	status = "okay";
};

&sdhci0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sd1_default>;
};

/*
 * Enable port A as device (via the virtual hub) and port B as
 * host by default on the eval board. This can be easily changed
 * by replacing the override below with &ehci0 { ... } to enable
 * host on both ports.
 */
&vhub {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&gfx {
     status = "okay";
     memory-region = <&gfx_memory>;
};

&rtc {
	status = "okay";
};
