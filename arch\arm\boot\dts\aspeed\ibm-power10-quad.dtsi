// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2023 IBM Corp.

#include "ibm-power10-dual.dtsi"

&cfam0_i2c0 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom100: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo100: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam0_i2c1 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom101: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo101: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam0_i2c10 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom110: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo110: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam0_i2c11 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom111: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo111: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam0_i2c12 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom112: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo112: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam0_i2c13 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom113: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo113: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam0_i2c14 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom114: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo114: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam0_i2c15 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom115: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo115: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c2 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom202: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo202: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c3 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom203: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo203: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c10 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom210: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo210: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c11 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom211: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo211: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c14 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom214: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo214: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c15 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom215: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo215: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c16 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom216: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo216: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&cfam1_i2c17 {
	i2cr@20 {
		compatible = "ibm,i2cr-fsi-master";
		reg = <0x20>;
		#address-cells = <2>;
		#size-cells = <0>;

		cfam@0,0 {
			reg = <0 0>;
			#address-cells = <1>;
			#size-cells = <1>;
			chip-id = <0>;

			scom217: scom@1000 {
				compatible = "ibm,i2cr-scom";
				reg = <0x1000 0x400>;
			};

			sbefifo217: sbefifo@2400 {
				compatible = "ibm,p9-sbefifo";
				reg = <0x2400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};

&fsi_hub0 {
	cfam@2,0 {
		reg = <2 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <2>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam2_i2c0: i2c-bus@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;	/* OM01 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom300: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo300: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam2_i2c1: i2c-bus@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;	/* OM23 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom301: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo301: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam2_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom310: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo310: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam2_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom311: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo311: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam2_i2c12: i2c-bus@c {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <12>;	/* OP4A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom312: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo312: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam2_i2c13: i2c-bus@d {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <13>;	/* OP4B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom313: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo313: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam2_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom314: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo314: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam2_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom315: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo315: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam2_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam2_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam2_spi2: spi@40 {
				reg = <0x40>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam2_spi3: spi@60 {
				reg = <0x60>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ2: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub2: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};

	cfam@3,0 {
		reg = <3 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		chip-id = <3>;

		scom@1000 {
			compatible = "ibm,fsi2pib";
			reg = <0x1000 0x400>;
		};

		i2c@1800 {
			compatible = "ibm,fsi-i2c-master";
			reg = <0x1800 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam3_i2c2: i2c-bus@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <2>;	/* OM45 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom402: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo402: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam3_i2c3: i2c-bus@3 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <3>;	/* OM67 */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom403: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo403: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam3_i2c10: i2c-bus@a {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <10>;	/* OP3A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom410: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo410: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam3_i2c11: i2c-bus@b {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <11>;	/* OP3B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom411: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo411: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam3_i2c14: i2c-bus@e {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <14>;	/* OP5A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom414: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo414: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam3_i2c15: i2c-bus@f {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <15>;	/* OP5B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom415: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo415: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam3_i2c16: i2c-bus@10 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <16>;	/* OP6A */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom416: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo416: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};

			cfam3_i2c17: i2c-bus@11 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <17>;	/* OP6B */

				i2cr@20 {
					compatible = "ibm,i2cr-fsi-master";
					reg = <0x20>;
					#address-cells = <2>;
					#size-cells = <0>;

					cfam@0,0 {
						reg = <0 0>;
						#address-cells = <1>;
						#size-cells = <1>;
						chip-id = <0>;

						scom417: scom@1000 {
							compatible = "ibm,i2cr-scom";
							reg = <0x1000 0x400>;
						};

						sbefifo417: sbefifo@2400 {
							compatible = "ibm,p9-sbefifo";
							reg = <0x2400 0x400>;
							#address-cells = <1>;
							#size-cells = <0>;
						};
					};
				};
			};
		};

		fsi2spi@1c00 {
			compatible = "ibm,fsi2spi";
			reg = <0x1c00 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			cfam3_spi0: spi@0 {
				reg = <0x0>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam3_spi1: spi@20 {
				reg = <0x20>;
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam3_spi2: spi@40 {
				reg = <0x40>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};

			cfam3_spi3: spi@60 {
				reg = <0x60>;
				compatible =  "ibm,fsi2spi";
				#address-cells = <1>;
				#size-cells = <0>;

				eeprom@0 {
					at25,byte-len = <0x80000>;
					at25,addr-mode = <4>;
					at25,page-size = <256>;

					compatible = "atmel,at25";
					reg = <0>;
					spi-max-frequency = <1000000>;
				};
			};
		};

		sbefifo@2400 {
			compatible = "ibm,p9-sbefifo";
			reg = <0x2400 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;

			fsi_occ3: occ {
				compatible = "ibm,p10-occ";

				occ-hwmon {
					compatible = "ibm,p10-occ-hwmon";
					ibm,no-poll-on-init;
				};
			};
		};

		fsi_hub3: hub@3400 {
			compatible = "fsi-master-hub";
			reg = <0x3400 0x400>;
			#address-cells = <2>;
			#size-cells = <0>;

			no-scan-on-init;
		};
	};
};

/* Legacy OCC numbering (to get rid of when userspace is fixed) */
&fsi_occ2 {
	reg = <3>;
};

&fsi_occ3 {
	reg = <4>;
};

/ {
	aliases {
		i2c300 = &cfam2_i2c0;
		i2c301 = &cfam2_i2c1;
		i2c310 = &cfam2_i2c10;
		i2c311 = &cfam2_i2c11;
		i2c312 = &cfam2_i2c12;
		i2c313 = &cfam2_i2c13;
		i2c314 = &cfam2_i2c14;
		i2c315 = &cfam2_i2c15;
		i2c402 = &cfam3_i2c2;
		i2c403 = &cfam3_i2c3;
		i2c410 = &cfam3_i2c10;
		i2c411 = &cfam3_i2c11;
		i2c414 = &cfam3_i2c14;
		i2c415 = &cfam3_i2c15;
		i2c416 = &cfam3_i2c16;
		i2c417 = &cfam3_i2c17;

		sbefifo100 = &sbefifo100;
		sbefifo101 = &sbefifo101;
		sbefifo110 = &sbefifo110;
		sbefifo111 = &sbefifo111;
		sbefifo112 = &sbefifo112;
		sbefifo113 = &sbefifo113;
		sbefifo114 = &sbefifo114;
		sbefifo115 = &sbefifo115;
		sbefifo202 = &sbefifo202;
		sbefifo203 = &sbefifo203;
		sbefifo210 = &sbefifo210;
		sbefifo211 = &sbefifo211;
		sbefifo214 = &sbefifo214;
		sbefifo215 = &sbefifo215;
		sbefifo216 = &sbefifo216;
		sbefifo217 = &sbefifo217;
		sbefifo300 = &sbefifo300;
		sbefifo301 = &sbefifo301;
		sbefifo310 = &sbefifo310;
		sbefifo311 = &sbefifo311;
		sbefifo312 = &sbefifo312;
		sbefifo313 = &sbefifo313;
		sbefifo314 = &sbefifo314;
		sbefifo315 = &sbefifo315;
		sbefifo402 = &sbefifo402;
		sbefifo403 = &sbefifo403;
		sbefifo410 = &sbefifo410;
		sbefifo411 = &sbefifo411;
		sbefifo414 = &sbefifo414;
		sbefifo415 = &sbefifo415;
		sbefifo416 = &sbefifo416;
		sbefifo417 = &sbefifo417;

		scom100 = &scom100;
		scom101 = &scom101;
		scom110 = &scom110;
		scom111 = &scom111;
		scom112 = &scom112;
		scom113 = &scom113;
		scom114 = &scom114;
		scom115 = &scom115;
		scom202 = &scom202;
		scom203 = &scom203;
		scom210 = &scom210;
		scom211 = &scom211;
		scom214 = &scom214;
		scom215 = &scom215;
		scom216 = &scom216;
		scom217 = &scom217;
		scom300 = &scom300;
		scom301 = &scom301;
		scom310 = &scom310;
		scom311 = &scom311;
		scom312 = &scom312;
		scom313 = &scom313;
		scom314 = &scom314;
		scom315 = &scom315;
		scom402 = &scom402;
		scom403 = &scom403;
		scom410 = &scom410;
		scom411 = &scom411;
		scom414 = &scom414;
		scom415 = &scom415;
		scom416 = &scom416;
		scom417 = &scom417;

		spi30 = &cfam2_spi0;
		spi31 = &cfam2_spi1;
		spi32 = &cfam2_spi2;
		spi33 = &cfam2_spi3;
		spi40 = &cfam3_spi0;
		spi41 = &cfam3_spi1;
		spi42 = &cfam3_spi2;
		spi43 = &cfam3_spi3;
	};
};
