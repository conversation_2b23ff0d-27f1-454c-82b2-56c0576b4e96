// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2018 Facebook Inc.
/dts-v1/;

#include "ast2400-facebook-netbmc-common.dtsi"

/ {
	model = "Facebook Wedge 100 BMC";
	compatible = "facebook,wedge100-bmc", "aspeed,ast2400";

	chosen {
		stdout-path = &uart3;
		bootargs = "console=ttyS2,9600n8 root=/dev/ram rw";
	};

	ast-adc-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 5>, <&adc 6>, <&adc 7>, <&adc 8>, <&adc 9>;
	};
};

&wdt2 {
	status = "okay";
	aspeed,reset-type = "system";
};

&fmc {
	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "spi0.1";

		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			flash1@0 {
				reg = <0x0 0x2000000>;
				label = "flash1";
			};
		};
	};
};

&i2c7 {
	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;
	};
};

&i2c9 {
	status = "okay";
};


&vhub {
	status = "okay";
};
