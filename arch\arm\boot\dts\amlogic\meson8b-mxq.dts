// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright 2015 Endless Mobile, Inc.
 * Author: <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include <dt-bindings/gpio/gpio.h>

#include "meson8b.dtsi"

/ {
	model = "TRONFY MXQ S805";
	compatible = "tronfy,mxq", "amlogic,meson8b";

	aliases {
		serial0 = &uart_AO;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x40000000>;
	};

	vcck: regulator-vcck {
		compatible = "pwm-regulator";

		regulator-name = "VCCK";
		regulator-min-microvolt = <860000>;
		regulator-max-microvolt = <1140000>;

		pwm-supply = <&vcc_5v>;

		pwms = <&pwm_cd 0 1148 0>;
		pwm-dutycycle-range = <100 0>;

		regulator-boot-on;
		regulator-always-on;
	};

	vcc_1v8: regulator-vcc1v8 {
		compatible = "regulator-fixed";

		regulator-name = "VCC1V8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;

		vin-supply = <&vcc_3v3>;
	};

	vcc_3v3: regulator-vcc3v3 {
		compatible = "regulator-fixed";

		regulator-name = "VCC3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		vin-supply = <&vcc_5v>;
	};

	vcc_5v: regulator-vcc5v {
		compatible = "regulator-fixed";

		regulator-name = "VCC5V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;

		regulator-boot-on;
		regulator-always-on;
	};

	vddee: regulator-vddee {
		compatible = "pwm-regulator";

		regulator-name = "VDDEE";
		regulator-min-microvolt = <860000>;
		regulator-max-microvolt = <1140000>;

		pwm-supply = <&vcc_5v>;

		pwms = <&pwm_cd 1 1148 0>;
		pwm-dutycycle-range = <100 0>;

		regulator-boot-on;
		regulator-always-on;
	};
};

&cpu0 {
	cpu-supply = <&vcck>;
};

&ethmac {
	status = "okay";

	pinctrl-0 = <&eth_rmii_pins>;
	pinctrl-names = "default";

	phy-handle = <&eth_phy0>;
	phy-mode = "rmii";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		eth_phy0: ethernet-phy@0 {
			/* IC Plus IP101A/G (0x02430c54) */
			reg = <0>;

			reset-assert-us = <10000>;
			reset-deassert-us = <10000>;
			reset-gpios = <&gpio GPIOH_4 GPIO_ACTIVE_LOW>;

			icplus,select-interrupt;
			interrupt-parent = <&gpio_intc>;
			/* GPIOH_3 */
			interrupts = <17 IRQ_TYPE_LEVEL_LOW>;
		};
	};
};

&mali {
	mali-supply = <&vddee>;
};

&saradc {
	status = "okay";
	vref-supply = <&vcc_1v8>;
};

&sdio {
	status = "okay";

	pinctrl-0 = <&sd_b_pins>;
	pinctrl-names = "default";

	/* SD card */
	sd_card_slot: slot@1 {
		compatible = "mmc-slot";
		reg = <1>;
		status = "okay";

		bus-width = <4>;
		no-sdio;
		cap-mmc-highspeed;
		cap-sd-highspeed;
		disable-wp;

		cd-gpios = <&gpio CARD_6 GPIO_ACTIVE_LOW>;

		vmmc-supply = <&vcc_3v3>;
	};
};

&pwm_cd {
	status = "okay";
	pinctrl-0 = <&pwm_c1_pins>, <&pwm_d_pins>;
	pinctrl-names = "default";
};

&uart_AO {
	status = "okay";
	pinctrl-0 = <&uart_ao_a_pins>;
	pinctrl-names = "default";
};

&usb0 {
	status = "okay";
};

&usb0_phy {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&usb1_phy {
	status = "okay";
};
