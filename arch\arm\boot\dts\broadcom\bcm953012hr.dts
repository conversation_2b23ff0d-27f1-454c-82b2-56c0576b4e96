/*
 *  SPDX-License-Identifier: BSD-3-Clause
 *
 *  Copyright(c) 2017 Broadcom
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    * Redistributions of source code must retain the above copyright
 *      notice, this list of conditions and the following disclaimer.
 *    * Redistributions in binary form must reproduce the above copyright
 *      notice, this list of conditions and the following disclaimer in
 *      the documentation and/or other materials provided with the
 *      distribution.
 *    * Neither the name of Broadcom nor the names of its contributors
 *      may be used to endorse or promote products derived from this
 *      software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/dts-v1/;

#include "bcm4708.dtsi"
#include "bcm5301x-nand-cs0-bch4.dtsi"

/ {
	model = "NorthStar HR (BCM953012HR)";
	compatible = "brcm,bcm953012hr", "brcm,bcm53012", "brcm,bcm4708";

	aliases {
		ethernet0 = &gmac0;
		ethernet1 = &gmac1;
		ethernet2 = &gmac2;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x10000000>;
	};
};

&nandcs {
	partition@0 {
		label = "nboot";
		reg = <0x00000000 0x00200000>;
		read-only;
	};
	partition@200000 {
		label = "nenv";
		reg = <0x00200000 0x00400000>;
	};
	partition@600000 {
		label = "nsystem";
		reg = <0x00600000 0x00a00000>;
	};
	partition@1000000 {
		label = "nrootfs";
		reg = <0x01000000 0x07000000>;
	};
};

&spi_nor {
	status = "okay";
	spi-max-frequency = <62500000>;

	#address-cells = <1>;
	#size-cells = <1>;

	partition@0 {
		label = "boot";
		reg = <0x00000000 0x000d0000>;
	};
	partition@d000 {
		label = "env";
		reg = <0x000d0000 0x00030000>;
	};
	partition@100000 {
		label = "system";
		reg = <0x00100000 0x00600000>;
	};
	partition@700000 {
		label = "rootfs";
		reg = <0x00700000 0x00900000>;
	};
};

&usb3_phy {
	status = "okay";
};
