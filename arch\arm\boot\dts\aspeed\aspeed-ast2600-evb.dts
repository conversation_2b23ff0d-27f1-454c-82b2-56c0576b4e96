// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2019 IBM Corp.

/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "AST2600 EVB";
	compatible = "aspeed,ast2600-evb", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		bootargs = "console=ttyS4,115200n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		video_engine_memory: video {
			size = <0x04000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	vcc_sdhci0: regulator-vcc-sdhci0 {
		compatible = "regulator-fixed";
		regulator-name = "SDHCI0 Vcc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpios = <&gpio0 ASPEED_GPIO(V, 0) GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vccq_sdhci0: regulator-vccq-sdhci0 {
		compatible = "regulator-gpio";
		regulator-name = "SDHCI0 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;
		gpios = <&gpio0 ASPEED_GPIO(V, 1) GPIO_ACTIVE_HIGH>;
		gpios-states = <1>;
		states = <3300000 1>,
			 <1800000 0>;
	};

	vcc_sdhci1: regulator-vcc-sdhci1 {
		compatible = "regulator-fixed";
		regulator-name = "SDHCI1 Vcc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpios = <&gpio0 ASPEED_GPIO(V, 2) GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vccq_sdhci1: regulator-vccq-sdhci1 {
		compatible = "regulator-gpio";
		regulator-name = "SDHCI1 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;
		gpios = <&gpio0 ASPEED_GPIO(V, 3) GPIO_ACTIVE_HIGH>;
		gpios-states = <1>;
		states = <3300000 1>,
			 <1800000 0>;
	};
};

&mdio0 {
	status = "okay";

	ethphy0: ethernet-phy@0 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

&mdio1 {
	status = "okay";

	ethphy1: ethernet-phy@0 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

&mdio2 {
	status = "okay";

	ethphy2: ethernet-phy@0 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

&mdio3 {
	status = "okay";

	ethphy3: ethernet-phy@0 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

&mac0 {
	status = "okay";

	phy-mode = "rgmii-rxid";
	phy-handle = <&ethphy0>;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default>;
};


&mac1 {
	status = "okay";

	phy-mode = "rgmii-rxid";
	phy-handle = <&ethphy1>;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default>;
};

&mac2 {
	status = "okay";

	phy-mode = "rgmii";
	phy-handle = <&ethphy2>;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii3_default>;
};

&mac3 {
	status = "okay";

	phy-mode = "rgmii";
	phy-handle = <&ethphy3>;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii4_default>;
};

&emmc_controller {
	status = "okay";
};

&emmc {
	non-removable;
	bus-width = <4>;
	max-frequency = <100000000>;
	clk-phase-mmc-hs200 = <9>, <225>;
};

&rtc {
	status = "okay";
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-rx-bus-width = <4>;
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-64.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-rx-bus-width = <4>;
		spi-max-frequency = <100000000>;
	};
};

&uart5 {
	// Workaround for A0
	compatible = "snps,dw-apb-uart";
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";

	temp@2e {
		compatible = "adi,adt7490";
		reg = <0x2e>;
	};

	eeprom@50 {
		compatible = "atmel,24c08";
		reg = <0x50>;
		pagesize = <16>;
	};
};

&i2c8 {
	status = "okay";

	lm75@4d {
		compatible = "national,lm75";
		reg = <0x4d>;
	};
};

&i2c9 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&i2c14 {
	status = "okay";
};

&i2c15 {
	status = "okay";
};

&fsim0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&sdc {
	status = "okay";
};

/*
 * The signal voltage of sdhci0 and sdhci1 on AST2600-A2 EVB is able to be
 * toggled by GPIO pins.
 * In the reference design, GPIOV0 of AST2600-A2 EVB is connected to the
 * power load switch that provides 3.3v to sdhci0 vdd, GPIOV1 is connected to
 * a 1.8v and a 3.3v power load switch that provides signal voltage to
 * sdhci0 bus.
 * If GPIOV0 is active high, sdhci0 is enabled, otherwise, sdhci0 is disabled.
 * If GPIOV1 is active high, 3.3v power load switch is enabled, sdhci0 signal
 * voltage is 3.3v, otherwise, 1.8v power load switch will be enabled,
 * sdhci0 signal voltage becomes 1.8v.
 * AST2600-A2 EVB also supports toggling signal voltage for sdhci1.
 * The design is the same as sdhci0, it uses GPIOV2 as power-gpio and GPIOV3
 * as power-switch-gpio.
 */
&sdhci0 {
	status = "okay";
	bus-width = <4>;
	max-frequency = <100000000>;
	sdhci-drive-type = /bits/ 8 <3>;
	sdhci-caps-mask = <0x7 0x0>;
	sdhci,wp-inverted;
	vmmc-supply = <&vcc_sdhci0>;
	vqmmc-supply = <&vccq_sdhci0>;
	clk-phase-sd-hs = <7>, <200>;
};

&sdhci1 {
	status = "okay";
	bus-width = <4>;
	max-frequency = <100000000>;
	sdhci-drive-type = /bits/ 8 <3>;
	sdhci-caps-mask = <0x7 0x0>;
	sdhci,wp-inverted;
	vmmc-supply = <&vcc_sdhci1>;
	vqmmc-supply = <&vccq_sdhci1>;
	clk-phase-sd-hs = <7>, <200>;
};

&vhub {
	status = "okay";
	pinctrl-names = "default";
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&gfx {
	status = "okay";
	memory-region = <&gfx_memory>;
};
