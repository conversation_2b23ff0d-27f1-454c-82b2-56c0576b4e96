/*
 * Copyright 2013 <PERSON>
 *
 * <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun7i-a20.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "Cubietech Cubietruck";
	compatible = "cubietech,cubietruck", "allwinner,sun7i-a20";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	hdmi-connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "cubietruck:blue:usr";
			gpios = <&pio 7 21 GPIO_ACTIVE_HIGH>;
		};

		led-1 {
			label = "cubietruck:orange:usr";
			gpios = <&pio 7 20 GPIO_ACTIVE_HIGH>;
		};

		led-2 {
			label = "cubietruck:white:usr";
			gpios = <&pio 7 11 GPIO_ACTIVE_HIGH>;
		};

		led-3 {
			label = "cubietruck:green:usr";
			gpios = <&pio 7 7 GPIO_ACTIVE_HIGH>;
		};
	};

	mmc3_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 7 9 GPIO_ACTIVE_LOW>; /* PH9 WIFI_EN */
		clocks = <&ccu CLK_OUT_A>;
		clock-names = "ext_clock";
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "On-board SPDIF";

		simple-audio-card,cpu {
			sound-dai = <&spdif>;
		};

		simple-audio-card,codec {
			sound-dai = <&spdif_out>;
		};
	};

	spdif_out: spdif-out {
		#sound-dai-cells = <0>;
		compatible = "linux,spdif-dit";
	};
};

&ahci {
	target-supply = <&reg_ahci_5v>;
	status = "okay";
};

&codec {
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "rgmii-id";
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&ir0 {
	pinctrl-names = "default";
	pinctrl-0 = <&ir0_rx_pin>;
	status = "okay";
};

&gmac_mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&mmc3 {
	vmmc-supply = <&reg_vcc3v3>;
	mmc-pwrseq = <&mmc3_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	brcmf: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&pio>;
		interrupts = <7 10 IRQ_TYPE_LEVEL_LOW>; /* PH10 / EINT10 */
		interrupt-names = "host-wake";
	};
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pio {
	/* Pin outputs low power clock for WiFi and BT */
	pinctrl-0 = <&clk_out_a_pin>;
	pinctrl-names = "default";
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pin>, <&pwm1_pin>;
	status = "okay";
};

&reg_ahci_5v {
	gpio = <&pio 7 12 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1450000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_usb0_vbus {
	gpio = <&pio 7 17 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&reg_usb1_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&spdif {
	pinctrl-names = "default";
	pinctrl-0 = <&spdif_tx_pin>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pi_pins>, <&uart2_cts_rts_pi_pins>;
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm20702a1";
		clocks = <&ccu CLK_OUT_A>;
		clock-names = "lpo";
		device-wakeup-gpios = <&pio 7 24 GPIO_ACTIVE_LOW>; /* PH24 */
		host-wakeup-gpios = <&pio 7 25 GPIO_ACTIVE_LOW>; /* PH25 */
		shutdown-gpios = <&pio 7 18 GPIO_ACTIVE_HIGH>; /* PH18 */
		max-speed = <1500000>;
	};
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 19 GPIO_ACTIVE_HIGH>; /* PH19 */
	usb0_vbus_det-gpios = <&pio 7 22 GPIO_ACTIVE_HIGH>; /* PH22 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
