// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2022 IBM Corp.
/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "Bonnell";
	compatible = "ibm,bonnell-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
		i2c16 = &i2c11mux0chn0;
		i2c17 = &i2c11mux0chn1;
		i2c18 = &i2c11mux0chn2;
		i2c19 = &i2c11mux0chn3;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200n8 earlycon";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		event_log: tcg_event_log@b3d00000 {
			no-map;
			reg = <0xb3d00000 0x100000>;
		};

		ramoops@b3e00000 {
			compatible = "ramoops";
			reg = <0xb3e00000 0x200000>; /* 16 * (4 * 0x8000) */
			record-size = <0x8000>;
			console-size = <0x8000>;
			ftrace-size = <0x8000>;
			pmsg-size = <0x8000>;
			max-reason = <3>; /* KMSG_DUMP_EMERG */
		};

		/* LPC FW cycle bridge region requires natural alignment */
		flash_memory: region@b4000000 {
			no-map;
			reg = <0xb4000000 0x04000000>; /* 64M */
		};

		/* VGA region is dictated by hardware strapping */
		vga_memory: region@bf000000 {
			no-map;
			compatible = "shared-dma-pool";
			reg = <0xbf000000 0x01000000>;  /* 16M */
		};
	};

	leds {
		compatible = "gpio-leds";

		fan0 {
			gpios = <&gpio0 ASPEED_GPIO(G, 0) GPIO_ACTIVE_LOW>;
		};

		fan1 {
			gpios = <&gpio0 ASPEED_GPIO(G, 1) GPIO_ACTIVE_LOW>;
		};

		rear-enc-id0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 2) GPIO_ACTIVE_LOW>;
		};

		rear-enc-fault0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 3) GPIO_ACTIVE_LOW>;
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		#address-cells = <1>;
		#size-cells = <0>;
		poll-interval = <1000>;

		fan0-presence {
			label = "fan0-presence";
			gpios = <&gpio0 ASPEED_GPIO(F, 4) GPIO_ACTIVE_LOW>;
			linux,code = <6>;
		};

		fan1-presence {
			label = "fan1-presence";
			gpios = <&gpio0 ASPEED_GPIO(F, 5) GPIO_ACTIVE_LOW>;
			linux,code = <7>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc1 7>;
	};
};

&adc1 {
	status = "okay";
	aspeed,int-vref-microvolt = <2500000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc9_default
		&pinctrl_adc10_default &pinctrl_adc11_default
		&pinctrl_adc12_default &pinctrl_adc13_default
		&pinctrl_adc14_default &pinctrl_adc15_default>;
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&gpio0 {
	gpio-line-names =
		/*A0-A7*/	"","","","","","","","",
		/*B0-B7*/	"","","","","","","checkstop","",
		/*C0-C7*/	"","","","","","","","",
		/*D0-D7*/	"","","","","","","","",
		/*E0-E7*/	"","","","","","","","",
		/*F0-F7*/	"","","rtc-battery-voltage-read-enable","reset-cause-pinhole","","","","",
		/*G0-G7*/	"fan0","fan1","","","","","","",
		/*H0-H7*/	"","","rear-enc-id0","rear-enc-fault0","","","","",
		/*I0-I7*/	"","","","","","","bmc-secure-boot","",
		/*J0-J7*/	"","","","","","","","",
		/*K0-K7*/	"","","","","","","","",
		/*L0-L7*/	"","","","","","","","",
		/*M0-M7*/	"","","","","","","","",
		/*N0-N7*/	"","","","","","","","",
		/*O0-O7*/	"","","","usb-power","","","","",
		/*P0-P7*/	"","","","","","","","",
		/*Q0-Q7*/	"cfam-reset","","regulator-standby-faulted","","","","","",
		/*R0-R7*/	"bmc-tpm-reset","power-chassis-control","power-chassis-good","","","","","",
		/*S0-S7*/	"presence-ps0","presence-ps1","","","power-ffs-sync-history","","","",
		/*T0-T7*/	"","","","","","","","",
		/*U0-U7*/	"","","","","","","","",
		/*V0-V7*/	"","","","","","","","",
		/*W0-W7*/	"","","","","","","","",
		/*X0-X7*/	"","","","","","","","",
		/*Y0-Y7*/	"","","","","","","","",
		/*Z0-Z7*/	"","","","","","","","";

	usb_power {
		gpio-hog;
		gpios = <ASPEED_GPIO(O, 3) GPIO_ACTIVE_LOW>;
		output-high;
	};
};

&emmc_controller {
	status = "okay";
};

&pinctrl_emmc_default {
	bias-disable;
};

&emmc {
	status = "okay";
	clk-phase-mmc-hs200 = <180>, <180>;
};

&ibt {
	status = "okay";
};

&i2c0 {
	status = "okay";

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	tca9554@20 {
		compatible = "ti,tca9554";
		reg = <0x20>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names = "",
			"RUSSEL_FW_I2C_ENABLE_N",
			"RUSSEL_OPPANEL_PRESENCE_N",
			"BLYTH_OPPANEL_PRESENCE_N",
			"CPU_TPM_CARD_PRESENT_N",
			"",
			"",
			"DASD_BP_PRESENT_N";
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	ucd90160@64 {
		compatible = "ti,ucd90160";
		reg = <0x64>;
	};
};

&i2c3 {
	status = "okay";

	power-supply@5a {
		compatible = "acbel,fsg032";
		reg = <0x5a>;
	};

	power-supply@5b {
		compatible = "acbel,fsg032";
		reg = <0x5b>;
	};
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	multi-master;
	status = "okay";

	si7021-a20@40 {
		compatible = "silabs,si7020";
		reg = <0x40>;
	};

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	max31785@52 {
		compatible = "maxim,max31785a";
		reg = <0x52>;
		#address-cells = <1>;
		#size-cells = <0>;

		fan0: fan@0 {
			compatible = "pmbus-fan";
			reg = <0>;
			tach-pulses = <2>;
		};

		fan1: fan@1 {
			compatible = "pmbus-fan";
			reg = <1>;
			tach-pulses = <2>;
		};
	};

	pca9551@60 {
		compatible = "nxp,pca9551";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "front-sys-id0";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "front-check-log0";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "front-enc-fault1";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "front-sys-pwron0";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	ibm-panel@62 {
		compatible = "ibm,op-panel";
		reg = <(0x62 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	dps: dps310@76 {
		compatible = "infineon,dps310";
		reg = <0x76>;
		#io-channel-cells = <0>;
	};
};

&i2c8 {
	status = "okay";

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};

	pca9551@60 {
		compatible = "nxp,pca9551";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names = "",
			"APSS_RESET_N",
			"",
			"N_MODE_CPU_N",
			"",
			"",
			"P10_DCM_PRESENT",
			"";
	};
};

&i2c9 {
	status = "okay";

	tmp423a@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";

	tca9554@20 {
		compatible = "ti,tca9554";
		reg = <0x20>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names = "BOOT_RCVRY_TWI",
			"BOOT_RCVRY_UART",
			"",
			"",
			"",
			"",
			"",
			"PE_SWITCH_RSTB_N";
	};

	tmp435@4c {
		compatible = "ti,tmp435";
		reg = <0x4c>;
	};

	pca9849@75 {
		compatible = "nxp,pca9849";
		reg = <0x75>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c11mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c11mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c11mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c11mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c12 {
	status = "okay";

	tpm@2e {
		compatible = "nuvoton,npct75x", "tcg,tpm-tis-i2c";
		reg = <0x2e>;
		memory-region = <&event_log>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};
};

&i2c13 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	pca9551@60 {
		compatible = "nxp,pca9551";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "nvme3";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "nvme2";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "nvme1";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "nvme0";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&i2c14 {
	status = "okay";
};

&i2c15 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&vuart1 {
	status = "okay";
};

&vuart2 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC3CLK>,
		 <&syscon ASPEED_CLK_MAC3RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	status = "okay";
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8 0xcac>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
	aspeed,lpc-interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
};

#include "ibm-power10-dual.dtsi"

&cfam0_i2c10 {
	eeprom@50 {
		compatible = "atmel,at30tse004a";
		reg = <0x50>;
	};
};

&cfam0_i2c11 {
	eeprom@50 {
		compatible = "atmel,at30tse004a";
		reg = <0x50>;
	};
};

&cfam0_i2c12 {
	eeprom@50 {
		compatible = "atmel,at30tse004a";
		reg = <0x50>;
	};
};

&cfam0_i2c13 {
	eeprom@50 {
		compatible = "atmel,at30tse004a";
		reg = <0x50>;
	};
};
