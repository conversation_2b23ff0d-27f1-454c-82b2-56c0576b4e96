// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Ampere Mt. Jade BMC";
	compatible = "ampere,mtjade-bmc", "aspeed,ast2500";

	aliases {
		/*
		 *  i2c bus 50-57 assigned to NVMe slot 0-7
		 */
		i2c50 = &nvmeslot_0;
		i2c51 = &nvmeslot_1;
		i2c52 = &nvmeslot_2;
		i2c53 = &nvmeslot_3;
		i2c54 = &nvmeslot_4;
		i2c55 = &nvmeslot_5;
		i2c56 = &nvmeslot_6;
		i2c57 = &nvmeslot_7;

		/*
		 *  i2c bus 60-67 assigned to NVMe slot 8-15
		 */
		i2c60 = &nvmeslot_8;
		i2c61 = &nvmeslot_9;
		i2c62 = &nvmeslot_10;
		i2c63 = &nvmeslot_11;
		i2c64 = &nvmeslot_12;
		i2c65 = &nvmeslot_13;
		i2c66 = &nvmeslot_14;
		i2c67 = &nvmeslot_15;

		/*
		 *  i2c bus 70-77 assigned to NVMe slot 16-23
		 */
		i2c70 = &nvmeslot_16;
		i2c71 = &nvmeslot_17;
		i2c72 = &nvmeslot_18;
		i2c73 = &nvmeslot_19;
		i2c74 = &nvmeslot_20;
		i2c75 = &nvmeslot_21;
		i2c76 = &nvmeslot_22;
		i2c77 = &nvmeslot_23;

		/*
		 *  i2c bus 80-81 assigned to NVMe M2 slot 0-1
		 */
		i2c80 = &nvme_m2_0;
		i2c81 = &nvme_m2_1;

		/*
		 *  i2c bus 82 assigned to OCP slot
		 */
		i2c82 = &ocpslot;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@9f000000 {
			no-map;
			reg = <0x9f000000 0x01000000>; /* 16M */
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	leds {
		compatible = "gpio-leds";

		fault {
			gpios = <&gpio ASPEED_GPIO(B, 6) GPIO_ACTIVE_HIGH>;
		};

		identify {
			gpios = <&gpio ASPEED_GPIO(Q, 6) GPIO_ACTIVE_HIGH>;
		};
	};

	gpioA0mux: mux-controller {
		compatible = "gpio-mux";
		#mux-control-cells = <0>;
		mux-gpios = <&gpio ASPEED_GPIO(A, 0) GPIO_ACTIVE_LOW>;
	};

	adc0mux: adc0mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 0>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc1mux: adc1mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 1>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc2mux: adc2mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 2>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc3mux: adc3mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 3>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc4mux: adc4mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 4>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc5mux: adc5mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 5>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc6mux: adc6mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 6>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc7mux: adc7mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 7>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc8mux: adc8mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 8>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc9mux: adc9mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 9>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc10mux: adc10mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 10>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc11mux: adc11mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 11>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc12mux: adc12mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 12>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	adc13mux: adc13mux {
		compatible = "io-channel-mux";
		io-channels = <&adc 13>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioA0mux>;
		channels = "s0", "s1";
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc0mux 0>, <&adc0mux 1>,
			<&adc1mux 0>, <&adc1mux 1>,
			<&adc2mux 0>, <&adc2mux 1>,
			<&adc3mux 0>, <&adc3mux 1>,
			<&adc4mux 0>, <&adc4mux 1>,
			<&adc5mux 0>, <&adc5mux 1>,
			<&adc6mux 0>, <&adc6mux 1>,
			<&adc7mux 0>, <&adc7mux 1>,
			<&adc8mux 0>, <&adc8mux 1>,
			<&adc9mux 0>, <&adc9mux 1>,
			<&adc10mux 0>, <&adc10mux 1>,
			<&adc11mux 0>, <&adc11mux 1>,
			<&adc12mux 0>, <&adc12mux 1>,
			<&adc13mux 0>, <&adc13mux 1>,
			<&adc 14>, <&adc 15>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		/* spi-max-frequency = <50000000>; */
#include "openbmc-flash-layout-64.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
#include "openbmc-flash-layout-64-alt.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		/* spi-max-frequency = <100000000>; */
		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;
			uefi@400000 {
				reg = <0x400000 0x1C00000>;
				label = "pnor-uefi";
			};
		};
	};
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			 &pinctrl_rxd1_default
			 &pinctrl_ncts1_default
			 &pinctrl_nrts1_default>;
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default
			 &pinctrl_rxd2_default>;
};

&uart3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd3_default
			 &pinctrl_rxd3_default>;
};

&uart4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd4_default
			 &pinctrl_rxd4_default>;
};

/* The BMC's uart */
&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c0 {
	status = "okay";
	ssif-bmc@10 {
		compatible = "ssif-bmc";
		reg = <0x10>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
	smpro@4f {
		compatible = "ampere,smpro";
		reg = <0x4f>;
	};
	smpro@4e {
		compatible = "ampere,smpro";
		reg = <0x4e>;
	};
};

&i2c3 {
	status = "okay";
	eeprom@50 {
		compatible = "microchip,24c64", "atmel,24c64";
		reg = <0x50>;
		pagesize = <32>;
	};

	inlet_mem2: tmp175@28 {
		compatible = "ti,tmp175";
		reg = <0x28>;
	};

	inlet_cpu: tmp175@29 {
		compatible = "ti,tmp175";
		reg = <0x29>;
	};

	inlet_mem1: tmp175@2a {
		compatible = "ti,tmp175";
		reg = <0x2a>;
	};

	outlet_cpu: tmp175@2b {
		compatible = "ti,tmp175";
		reg = <0x2b>;
	};

	outlet1: tmp175@2c {
		compatible = "ti,tmp175";
		reg = <0x2c>;
	};

	outlet2: tmp175@2d {
		compatible = "ti,tmp175";
		reg = <0x2d>;
	};
};

&i2c4 {
	status = "okay";
	rtc@51 {
		compatible = "nxp,pcf85063a";
		reg = <0x51>;
	};
};

&i2c5 {
	status = "okay";
	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		ocpslot: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;

			ocpslot_temp: temperature-sensor@1f {
				compatible = "ti,tmp421";
				reg = <0x1f>;
			};
		};

		nvmeslot_0_7: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;
		};
	};

	i2c-mux@71 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x71>;
		i2c-mux-idle-disconnect;

		nvmeslot_8_15: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x4>;
		};

		nvmeslot_16_23: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;
		};

	};

	i2c-mux@72 {
		compatible = "nxp,pca9545";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x72>;
		i2c-mux-idle-disconnect;

		nvme_m2_0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;
		};

		nvme_m2_1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x1>;
		};
	};
};

&nvmeslot_0_7 {
	status = "okay";

	i2c-mux@75 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x75>;
		i2c-mux-idle-disconnect;

		nvmeslot_0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;
		};
		nvmeslot_1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x1>;
		};
		nvmeslot_2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;
		};
		nvmeslot_3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;
		};
		nvmeslot_4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x4>;
		};
		nvmeslot_5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x5>;
		};
		nvmeslot_6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x6>;
		};
		nvmeslot_7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x7>;
		};

	};
};

&nvmeslot_8_15 {
	status = "okay";

	i2c-mux@75 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x75>;
		i2c-mux-idle-disconnect;

		nvmeslot_8: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;
		};
		nvmeslot_9: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x1>;
		};
		nvmeslot_10: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;
		};
		nvmeslot_11: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;
		};
		nvmeslot_12: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x4>;
		};
		nvmeslot_13: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x5>;
		};
		nvmeslot_14: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x6>;
		};
		nvmeslot_15: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x7>;
		};
	};
};

&nvmeslot_16_23 {
	status = "okay";

	i2c-mux@75 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x75>;
		i2c-mux-idle-disconnect;

		nvmeslot_16: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;
		};
		nvmeslot_17: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x1>;
		};
		nvmeslot_18: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;
		};
		nvmeslot_19: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;
		};
		nvmeslot_20: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x4>;
		};
		nvmeslot_21: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x5>;
		};
		nvmeslot_22: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x6>;
		};
		nvmeslot_23: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x7>;
		};
	};
};

&i2c6 {
	status = "okay";
	psu@58 {
		compatible = "pmbus";
		reg = <0x58>;
	};

	psu@59 {
		compatible = "pmbus";
		reg = <0x59>;
	};
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
	adm1278@10 {
		compatible = "adi,adm1278";
		reg = <0x10>;
	};

	adm1278@11 {
		compatible = "adi,adm1278";
		reg = <0x11>;
	};
};

&gfx {
	status = "okay";
	memory-region = <&gfx_memory>;
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm2_default &pinctrl_pwm3_default
			&pinctrl_pwm4_default &pinctrl_pwm5_default
			&pinctrl_pwm6_default &pinctrl_pwm7_default>;

	fan@0 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};

	fan@1 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05>;
	};

	fan@2 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06>;
	};

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x07>;
	};

	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x08>;
	};

	fan@5 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x09>;
	};

	fan@6 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0a>;
	};

	fan@7 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0b>;
	};

	fan@8 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0c>;
	};

	fan@9 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0d>;
	};

	fan@10 {
		reg = <0x07>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0e>;
	};

	fan@11 {
		reg = <0x07>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0f>;
	};

};

&vhub {
	status = "okay";
};

&adc {
	status = "okay";
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&gpio {
	gpio-line-names =
	/*A0-A7*/	"","","","host0-special-boot","","","","",
	/*B0-B7*/	"i2c-backup-sel","","","",
			"power-button","presence-cpu0","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"ps0-pgood","ps1-pgood","power-chassis-control","s0-ddr-save",
			"power-chassis-good", "s1-ddr-save","","",
	/*G0-G7*/	"host0-ready","host0-shd-req-n","host0-shd-ack-n",
			"s0-overtemp-n","","","","",
	/*H0-H7*/	"uart1-mode1","uart2-mode1","uart3-mode1","uart4-mode1",
			"ps0-vin-good","ps1-vin-good","","i2c6-reset-n",
	/*I0-I7*/	"presence-ps0","presence-ps1","s1-special-boot","","","","","",
	/*J0-J7*/	"s0-hightemp-n","s0-fault-alert","s0-sys-auth-failure-n",
			"host0-reboot-ack-n","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","host0-sysreset-n","s0-spi-auth-fail-n","","","",
	/*M0-M7*/	"","","","","s0-i2c9-alert-n","s1-i2c9-alert-n","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"","","","","","","","",
	/*Q0-Q7*/	"","","","","","identify-button","led-identify","",
	/*R0-R7*/	"","","ext-hightemp-n","","ocp-main-pwren","reset-button","","",
	/*S0-S7*/	"s0-vr-hot-n","s1-vr-hot-n","","",
			"rtc-battery-voltage-read-enable","vr-pmbus-sel-n","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","bmc-vga-en-n","","","","",
	/*Z0-Z7*/	"s0-plimit","s1-fault-alert","s1-fw-boot-ok","s0-rtc-lock","",
			"s1-sys-auth-failure-n","s1-overtemp-n","",
	/*AA0-AA7*/	"","","","","","","","",
	/*AB0-AB7*/	"s1-hightemp-n","s1-plimit","s0-ddr-addr","s1-ddr-addr","","",
			"","",
	/*AC0-AC7*/	"sys-pwr-gd","","spi0-program-sel","spi0-backup-sel","bmc-ok",
			"","presence-cpu1","ocp-pgood";

	i2c4-o-en-hog {
		gpio-hog;
		gpios = <ASPEED_GPIO(Y, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "i2c4-o-en";
	};

	ocp-aux-pwren-hog {
		gpio-hog;
		gpios = <ASPEED_GPIO(R, 3) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "ocp-aux-pwren";
	};

	bmc-ready {
		gpio-hog;
		gpios = <ASPEED_GPIO(AC, 5) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "bmc-ready";
	};
};
