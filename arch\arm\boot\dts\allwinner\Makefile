# SPDX-License-Identifier: GPL-2.0
dtb-$(CONFIG_MACH_SUN4I) += \
	sun4i-a10-a1000.dtb \
	sun4i-a10-ba10-tvbox.dtb \
	sun4i-a10-chuwi-v7-cw0825.dtb \
	sun4i-a10-cubieboard.dtb \
	sun4i-a10-dserve-dsrv9703c.dtb \
	sun4i-a10-gemei-g9.dtb \
	sun4i-a10-hackberry.dtb \
	sun4i-a10-hyundai-a7hd.dtb \
	sun4i-a10-inet1.dtb \
	sun4i-a10-inet97fv2.dtb \
	sun4i-a10-inet9f-rev03.dtb \
	sun4i-a10-itead-iteaduino-plus.dtb \
	sun4i-a10-jesurun-q5.dtb \
	sun4i-a10-marsboard.dtb \
	sun4i-a10-mini-xplus.dtb \
	sun4i-a10-mk802.dtb \
	sun4i-a10-mk802ii.dtb \
	sun4i-a10-olinuxino-lime.dtb \
	sun4i-a10-pcduino.dtb \
	sun4i-a10-pcduino2.dtb \
	sun4i-a10-pov-protab2-ips9.dtb \
	sun4i-a10-topwise-a721.dtb
dtb-$(CONFIG_MACH_SUN4I) += \
	sun4i-a10-a1000.dtb \
	sun4i-a10-ba10-tvbox.dtb \
	sun4i-a10-chuwi-v7-cw0825.dtb \
	sun4i-a10-cubieboard.dtb \
	sun4i-a10-dserve-dsrv9703c.dtb \
	sun4i-a10-gemei-g9.dtb \
	sun4i-a10-hackberry.dtb \
	sun4i-a10-hyundai-a7hd.dtb \
	sun4i-a10-inet1.dtb \
	sun4i-a10-inet97fv2.dtb \
	sun4i-a10-inet9f-rev03.dtb \
	sun4i-a10-itead-iteaduino-plus.dtb \
	sun4i-a10-jesurun-q5.dtb \
	sun4i-a10-marsboard.dtb \
	sun4i-a10-mini-xplus.dtb \
	sun4i-a10-mk802.dtb \
	sun4i-a10-mk802ii.dtb \
	sun4i-a10-olinuxino-lime.dtb \
	sun4i-a10-pcduino.dtb \
	sun4i-a10-pcduino2.dtb \
	sun4i-a10-pov-protab2-ips9.dtb \
	sun4i-a10-topwise-a721.dtb
dtb-$(CONFIG_MACH_SUN5I) += \
	sun5i-a10s-auxtek-t003.dtb \
	sun5i-a10s-auxtek-t004.dtb \
	sun5i-a10s-mk802.dtb \
	sun5i-a10s-olinuxino-micro.dtb \
	sun5i-a10s-r7-tv-dongle.dtb \
	sun5i-a10s-wobo-i5.dtb \
	sun5i-a13-difrnce-dit4350.dtb \
	sun5i-a13-empire-electronix-d709.dtb \
	sun5i-a13-empire-electronix-m712.dtb \
	sun5i-a13-hsg-h702.dtb \
	sun5i-a13-inet-98v-rev2.dtb \
	sun5i-a13-licheepi-one.dtb \
	sun5i-a13-olinuxino.dtb \
	sun5i-a13-olinuxino-micro.dtb \
	sun5i-a13-pocketbook-touch-lux-3.dtb \
	sun5i-a13-pocketbook-614-plus.dtb \
	sun5i-a13-q8-tablet.dtb \
	sun5i-a13-utoo-p66.dtb \
	sun5i-gr8-chip-pro.dtb \
	sun5i-gr8-evb.dtb \
	sun5i-r8-chip.dtb
dtb-$(CONFIG_MACH_SUN5I) += \
	sun5i-a10s-auxtek-t003.dtb \
	sun5i-a10s-auxtek-t004.dtb \
	sun5i-a10s-mk802.dtb \
	sun5i-a10s-olinuxino-micro.dtb \
	sun5i-a10s-r7-tv-dongle.dtb \
	sun5i-a10s-wobo-i5.dtb \
	sun5i-a13-difrnce-dit4350.dtb \
	sun5i-a13-empire-electronix-d709.dtb \
	sun5i-a13-empire-electronix-m712.dtb \
	sun5i-a13-hsg-h702.dtb \
	sun5i-a13-inet-98v-rev2.dtb \
	sun5i-a13-licheepi-one.dtb \
	sun5i-a13-olinuxino.dtb \
	sun5i-a13-olinuxino-micro.dtb \
	sun5i-a13-pocketbook-touch-lux-3.dtb \
	sun5i-a13-q8-tablet.dtb \
	sun5i-a13-utoo-p66.dtb \
	sun5i-gr8-chip-pro.dtb \
	sun5i-gr8-evb.dtb \
	sun5i-r8-chip.dtb
dtb-$(CONFIG_MACH_SUN6I) += \
	sun6i-a31-app4-evb1.dtb \
	sun6i-a31-colombus.dtb \
	sun6i-a31-hummingbird.dtb \
	sun6i-a31-i7.dtb \
	sun6i-a31-m9.dtb \
	sun6i-a31-mele-a1000g-quad.dtb \
	sun6i-a31s-colorfly-e708-q1.dtb \
	sun6i-a31s-cs908.dtb \
	sun6i-a31s-inet-q972.dtb \
	sun6i-a31s-primo81.dtb \
	sun6i-a31s-sina31s.dtb \
	sun6i-a31s-sinovoip-bpi-m2.dtb \
	sun6i-a31s-yones-toptech-bs1078-v2.dtb
dtb-$(CONFIG_MACH_SUN6I) += \
	sun6i-a31-app4-evb1.dtb \
	sun6i-a31-colombus.dtb \
	sun6i-a31-hummingbird.dtb \
	sun6i-a31-i7.dtb \
	sun6i-a31-m9.dtb \
	sun6i-a31-mele-a1000g-quad.dtb \
	sun6i-a31s-colorfly-e708-q1.dtb \
	sun6i-a31s-cs908.dtb \
	sun6i-a31s-inet-q972.dtb \
	sun6i-a31s-primo81.dtb \
	sun6i-a31s-sina31s.dtb \
	sun6i-a31s-sinovoip-bpi-m2.dtb \
	sun6i-a31s-yones-toptech-bs1078-v2.dtb
dtb-$(CONFIG_MACH_SUN7I) += \
	sun7i-a20-bananapi.dtb \
	sun7i-a20-bananapi-m1-plus.dtb \
	sun7i-a20-bananapro.dtb \
	sun7i-a20-cubieboard2.dtb \
	sun7i-a20-cubietruck.dtb \
	sun7i-a20-haoyu-marsboard.dtb \
	sun7i-a20-hummingbird.dtb \
	sun7i-a20-itead-ibox.dtb \
	sun7i-a20-i12-tvbox.dtb \
	sun7i-a20-icnova-a20-adb4006.dtb \
	sun7i-a20-icnova-swac.dtb \
	sun7i-a20-lamobo-r1.dtb \
	sun7i-a20-linutronix-testbox-v2.dtb \
	sun7i-a20-m3.dtb \
	sun7i-a20-mk808c.dtb \
	sun7i-a20-olimex-som-evb.dtb \
	sun7i-a20-olimex-som-evb-emmc.dtb \
	sun7i-a20-olimex-som204-evb.dtb \
	sun7i-a20-olimex-som204-evb-emmc.dtb \
	sun7i-a20-olinuxino-lime.dtb \
	sun7i-a20-olinuxino-lime-emmc.dtb \
	sun7i-a20-olinuxino-lime2.dtb \
	sun7i-a20-olinuxino-lime2-emmc.dtb \
	sun7i-a20-olinuxino-micro.dtb \
	sun7i-a20-olinuxino-micro-emmc.dtb \
	sun7i-a20-orangepi.dtb \
	sun7i-a20-orangepi-mini.dtb \
	sun7i-a20-pcduino3.dtb \
	sun7i-a20-pcduino3-nano.dtb \
	sun7i-a20-wexler-tab7200.dtb \
	sun7i-a20-wits-pro-a20-dkt.dtb
dtb-$(CONFIG_MACH_SUN7I) += \
	sun7i-a20-bananapi.dtb \
	sun7i-a20-bananapi-m1-plus.dtb \
	sun7i-a20-bananapro.dtb \
	sun7i-a20-cubieboard2.dtb \
	sun7i-a20-cubietruck.dtb \
	sun7i-a20-haoyu-marsboard.dtb \
	sun7i-a20-hummingbird.dtb \
	sun7i-a20-itead-ibox.dtb \
	sun7i-a20-i12-tvbox.dtb \
	sun7i-a20-icnova-a20-adb4006.dtb \
	sun7i-a20-icnova-swac.dtb \
	sun7i-a20-lamobo-r1.dtb \
	sun7i-a20-linutronix-testbox-v2.dtb \
	sun7i-a20-m3.dtb \
	sun7i-a20-mk808c.dtb \
	sun7i-a20-olimex-som-evb.dtb \
	sun7i-a20-olimex-som-evb-emmc.dtb \
	sun7i-a20-olimex-som204-evb.dtb \
	sun7i-a20-olimex-som204-evb-emmc.dtb \
	sun7i-a20-olinuxino-lime.dtb \
	sun7i-a20-olinuxino-lime-emmc.dtb \
	sun7i-a20-olinuxino-lime2.dtb \
	sun7i-a20-olinuxino-lime2-emmc.dtb \
	sun7i-a20-olinuxino-micro.dtb \
	sun7i-a20-olinuxino-micro-emmc.dtb \
	sun7i-a20-orangepi.dtb \
	sun7i-a20-orangepi-mini.dtb \
	sun7i-a20-pcduino3.dtb \
	sun7i-a20-pcduino3-nano.dtb \
	sun7i-a20-wexler-tab7200.dtb \
	sun7i-a20-wits-pro-a20-dkt.dtb

# Enables support for device-tree overlays for all pis
DTC_FLAGS_sun8i-h3-orangepi-lite := -@
DTC_FLAGS_sun8i-h3-bananapi-m2-plus := -@
DTC_FLAGS_sun8i-h3-nanopi-m1-plus := -@
DTC_FLAGS_sun8i-h3-nanopi-m1 := -@
DTC_FLAGS_sun8i-h3-nanopi-duo2 := -@
DTC_FLAGS_sun8i-h3-orangepi-plus2e := -@
DTC_FLAGS_sun8i-h3-orangepi-one := -@
DTC_FLAGS_sun8i-h3-orangepi-plus := -@
DTC_FLAGS_sun8i-h3-orangepi-2 := -@
DTC_FLAGS_sun8i-h3-orangepi-zero-plus2 := -@
DTC_FLAGS_sun8i-h3-nanopi-neo-air := -@
DTC_FLAGS_sun8i-h3-zeropi := -@
DTC_FLAGS_sun8i-h3-nanopi-neo := -@
DTC_FLAGS_sun8i-h3-nanopi-r1 := -@
DTC_FLAGS_sun8i-h3-orangepi-pc := -@
DTC_FLAGS_sun8i-h3-bananapi-m2-plus-v1.2 := -@
DTC_FLAGS_sun8i-h3-orangepi-pc-plus := -@
DTC_FLAGS_sun8i-v3s-netcube-kumquat := -@
dtb-$(CONFIG_MACH_SUN8I) += \
	sun8i-a23-evb.dtb \
	sun8i-a23-gt90h-v4.dtb \
	sun8i-a23-inet86dz.dtb \
	sun8i-a23-ippo-q8h-v5.dtb \
	sun8i-a23-ippo-q8h-v1.2.dtb \
	sun8i-a23-polaroid-mid2407pxe03.dtb \
	sun8i-a23-polaroid-mid2809pxe04.dtb \
	sun8i-a23-q8-tablet.dtb \
	sun8i-a33-et-q8-v1.6.dtb \
	sun8i-a33-ga10h-v1.1.dtb \
	sun8i-a33-inet-d978-rev2.dtb \
	sun8i-a33-ippo-q8h-v1.2.dtb \
	sun8i-a33-olinuxino.dtb \
	sun8i-a33-q8-tablet.dtb \
	sun8i-a33-sinlinx-sina33.dtb \
	sun8i-a33-vstar.dtb \
	sun8i-a83t-allwinner-h8homlet-v2.dtb \
	sun8i-a83t-bananapi-m3.dtb \
	sun8i-a83t-cubietruck-plus.dtb \
	sun8i-a83t-tbs-a711.dtb \
	sun8i-h2-plus-bananapi-m2-zero.dtb \
	sun8i-h2-plus-libretech-all-h3-cc.dtb \
	sun8i-h2-plus-orangepi-r1.dtb \
	sun8i-h2-plus-orangepi-zero.dtb \
	sun8i-h3-bananapi-m2-plus.dtb \
	sun8i-h3-bananapi-m2-plus-v1.2.dtb \
	sun8i-h3-beelink-x2.dtb \
	sun8i-h3-libretech-all-h3-cc.dtb \
	sun8i-h3-mapleboard-mp130.dtb \
	sun8i-h3-nanopi-duo2.dtb \
	sun8i-h3-nanopi-m1.dtb \
	sun8i-h3-nanopi-m1-plus.dtb \
	sun8i-h3-nanopi-neo.dtb \
	sun8i-h3-nanopi-neo-air.dtb \
	sun8i-h3-nanopi-r1.dtb \
	sun8i-h3-orangepi-2.dtb \
	sun8i-h3-orangepi-lite.dtb \
	sun8i-h3-orangepi-one.dtb \
	sun8i-h3-orangepi-pc.dtb \
	sun8i-h3-orangepi-pc-plus.dtb \
	sun8i-h3-orangepi-plus.dtb \
	sun8i-h3-orangepi-plus2e.dtb \
	sun8i-h3-orangepi-zero-plus2.dtb \
	sun8i-h3-rervision-dvk.dtb \
	sun8i-h3-zeropi.dtb \
	sun8i-h3-emlid-neutis-n5h3-devboard.dtb \
	sun8i-r16-bananapi-m2m.dtb \
	sun8i-r16-nintendo-nes-classic.dtb \
	sun8i-r16-nintendo-super-nes-classic.dtb \
	sun8i-r16-parrot.dtb \
	sun8i-r40-bananapi-m2-ultra.dtb \
	sun8i-r40-oka40i-c.dtb \
	sun8i-s3-elimo-initium.dtb \
	sun8i-s3-lichee-zero-plus.dtb \
	sun8i-s3-pinecube.dtb \
	sun8i-t113s-mangopi-mq-r-t113.dtb \
	sun8i-t3-cqa3t-bv3.dtb \
	sun8i-v3-sl631-imx179.dtb \
	sun8i-v3s-anbernic-rg-nano.dtb \
	sun8i-v3s-licheepi-zero.dtb \
	sun8i-v3s-licheepi-zero-dock.dtb \
	sun8i-v3s-netcube-kumquat.dtb \
	sun8i-v40-bananapi-m2-berry.dtb
dtb-$(CONFIG_MACH_SUN9I) += \
	sun9i-a80-optimus.dtb \
	sun9i-a80-cubieboard4.dtb
dtb-$(CONFIG_MACH_SUNIV) += \
	suniv-f1c100s-licheepi-nano.dtb \
	suniv-f1c200s-lctech-pi.dtb \
	suniv-f1c200s-popstick-v1.1.dtb
