// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2023 Inventec Corp.

/dts-v1/;

#include "aspeed-g6.dtsi"
#include "aspeed-g6-pinctrl.dtsi"
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "STARSCREAM BMC";
	compatible = "inventec,starscream-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		video_engine_memory: video {
			size = <0x04000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-uid {
			label = "UID_LED";
			gpios = <&gpio0 186 GPIO_ACTIVE_LOW>;
		};

		led-heartbeat {
			label = "HB_LED";
			gpios = <&gpio0 127 GPIO_ACTIVE_LOW>;
		};
	};
};

&mdio0 {
	status = "okay";

	ethphy0: ethernet-phy@0 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <1>;
	};
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	phy-mode = "rmii";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	use-ncsi;
};

&mac3 {
	status = "okay";
	phy-mode = "rgmii";
	phy-handle = <&ethphy0>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii4_default>;
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <********>;
		spi-tx-bus-width = <4>;
		spi-rx-bus-width = <4>;
#include "openbmc-flash-layout.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "bmc2";
		spi-max-frequency = <********>;
		spi-tx-bus-width = <4>;
		spi-rx-bus-width = <4>;
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bios";
		spi-max-frequency = <********>;
		spi-tx-bus-width = <4>;
		spi-rx-bus-width = <4>;
	};
};

&vuart1 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&kcs3 {
	aspeed,lpc-io-reg = <0xca2>;
	status = "okay";
};

&uart_routing {
	status = "okay";
};

&i2c0 {
	status = "okay";
};
&i2c1 {
	status = "okay";
};
&i2c2 {
	status = "okay";
};
&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";

	// I2C EXPANDER
	i2c-mux@71 {
		compatible = "nxp,pca9546";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x71>;

		i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			// AMD SB-TSI CPU1
			sbtsi@4c {
				compatible = "amd,sbtsi";
				reg = <0x4c>;
			};
		};

		i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			// AMD SB-TSI CPU2
			sbtsi@48 {
				compatible = "amd,sbtsi";
				reg = <0x48>;
			};
		};
	};
};

&i2c5 {
	status = "okay";

	// I2C EXPANDER U153
	i2c-mux@70 {
		compatible = "nxp,pca9546";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;

		usb_hub: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		riser1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		riser2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c6 {
	status = "okay";

	// Motherboard Temp_U89
	temperature-sensor@4e {
		compatible = "ti,tmp421";
		reg = <0x4e>;
	};

	// RunBMC Temp_U6
	temperature-sensor@49 {
		compatible = "ti,tmp75";
		reg = <0x49>;
	};
};

&i2c7 {
	status = "okay";
	// I2C EXPANDER U40
	i2c-mux@70 {
		compatible = "nxp,pca9545";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;

		i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c8 {
	status = "okay";
	// FRU RunBMC
	eeprom@51 {
		compatible = "atmel,24c512";
		reg = <0x51>;
		pagesize = <128>;
	};
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&i2c12 {
	status = "okay";
	// FRU SCM
	eeprom@51 {
		compatible = "atmel,24c512";
		reg = <0x51>;
		pagesize = <128>;
	};

	// SCM Temp_U17
	temperature-sensor@4f {
		compatible = "ti,tmp75";
		reg = <0x4f>;
	};
};

&gpio0 {
	status = "okay";
	gpio-line-names =
	/*A0-A7*/   "","","","","","","","",
	/*B0-B7*/   "alert-psu0-smb-r-n","bmc-ready","","assert-cpu0-prochot-r-n",
	"","","","",
	/*C0-C7*/   "","","","","","","","",
	/*D0-D7*/   "","","","","","","","",
	/*E0-E7*/   "","","","","","","","",
	/*F0-F7*/   "","","","","reset-sgpio-r-n","","","",
	/*G0-G7*/   "","","scm-jtag-mux-select","","","","","",
	/*H0-H7*/   "","","","","reset-out","power-out","","",
	/*I0-I7*/   "","","","","","","irq-bmc-cpu0-buf-nmi-n","",
	/*J0-J7*/   "","","","","","","","",
	/*K0-K7*/   "","","","","","","","",
	/*L0-L7*/   "","","","","","","","",
	/*M0-M7*/   "","","","","","","","",
	/*N0-N7*/   "","","ncsi-ocp-clk-en-n","","","","","",
	/*O0-O7*/   "","","","","","","cpu1-thermal-trip-n","",
	/*P0-P7*/   "","","","","","","","",
	/*Q0-Q7*/   "cpu0-prochot-n","","cpu1-prochot-n","","cpu0-pe-rst0","","","",
	/*R0-R7*/   "","","","","","","","",
	/*S0-S7*/   "","","","",
	"","PCH_SLP_S4_BMC_N","cpu0-thermtrip-n","alert-psu1-smb-r-n",
	/*T0-T7*/   "","","","","","","","",
	/*U0-U7*/   "","","","","","","","",
	/*V0-V7*/   "bios-recovery-buf-n","","assert-cpu1-prochot-r-n","",
	"power-chassis-good","","","",
	/*W0-W7*/   "","","","","","","","",
	/*X0-X7*/   "","","","","platform-type","","","",
	/*Y0-Y7*/   "","","","","","","","",
	/*Z0-Z7*/   "","cpld-power-break-n","","","","","","",
	/*AA0-AA7*/ "","","","","","","","",
	/*AB0-AB7*/ "","","","","","","","",
	/*AC0-AC7*/ "","","","","","","","";
};

&sgpiom0 {
	status = "okay";
	ngpios = <64>;
	bus-frequency = <1000000>;
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&emmc_controller {
	status = "okay";
};

&emmc {
	status = "okay";
	non-removable;
	max-frequency = <52000000>;
	bus-width = <8>;
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&vhub {
	status = "okay";
	aspeed,vhub-downstream-ports = <7>;
	aspeed,vhub-generic-endpoints = <21>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_usb2ad_default>;
};

&rtc {
	status = "okay";
};
