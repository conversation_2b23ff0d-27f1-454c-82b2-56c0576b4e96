// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2021 Facebook Inc.
/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/usb/pd.h>
#include <dt-bindings/leds/leds-pca955x.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/i2c/i2c.h>

/ {
	model = "Facebook Bletchley BMC";
	compatible = "facebook,bletchley-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		bootargs = "console=ttyS4,57600n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc0 0>, <&adc0 1>, <&adc0 2>, <&adc0 3>,
			<&adc0 4>, <&adc0 5>, <&adc0 6>, <&adc0 7>,
			<&adc1 0>, <&adc1 1>, <&adc1 2>, <&adc1 3>,
			<&adc1 4>, <&adc1 5>, <&adc1 6>, <&adc1 7>;
	};

	spi1_gpio: spi1-gpio {
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-sck = <&gpio0 ASPEED_GPIO(Z, 3) GPIO_ACTIVE_HIGH>;
		gpio-mosi = <&gpio0 ASPEED_GPIO(Z, 4) GPIO_ACTIVE_HIGH>;
		gpio-miso = <&gpio0 ASPEED_GPIO(Z, 5) GPIO_ACTIVE_HIGH>;
		num-chipselects = <1>;
		cs-gpios = <&gpio0 ASPEED_GPIO(Z, 0) GPIO_ACTIVE_LOW>;

		tpm@0 {
			compatible = "infineon,slb9670", "tcg,tpm_tis-spi";
			spi-max-frequency = <33000000>;
			reg = <0>;
		};
	};

	switchphy: ethernet-phy@0 {
		// Fixed link
	};

	front_gpio_leds {
		compatible = "gpio-leds";
		sys_log_id {
			default-state = "off";
			gpios = <&front_leds 0 GPIO_ACTIVE_LOW>;
		};
	};

	fan_gpio_leds {
		compatible = "gpio-leds";
		fan0_blue {
			retain-state-shutdown;
			default-state = "on";
			gpios = <&fan_leds 8 GPIO_ACTIVE_HIGH>;
		};
		fan1_blue {
			retain-state-shutdown;
			default-state = "on";
			gpios = <&fan_leds 9 GPIO_ACTIVE_HIGH>;
		};
		fan2_blue {
			retain-state-shutdown;
			default-state = "on";
			gpios = <&fan_leds 10 GPIO_ACTIVE_HIGH>;
		};
		fan3_blue {
			retain-state-shutdown;
			default-state = "on";
			gpios = <&fan_leds 11 GPIO_ACTIVE_HIGH>;
		};
		fan0_amber {
			retain-state-shutdown;
			default-state = "off";
			gpios = <&fan_leds 12 GPIO_ACTIVE_HIGH>;
		};
		fan1_amber {
			retain-state-shutdown;
			default-state = "off";
			gpios = <&fan_leds 13 GPIO_ACTIVE_HIGH>;
		};
		fan2_amber {
			retain-state-shutdown;
			default-state = "off";
			gpios = <&fan_leds 14 GPIO_ACTIVE_HIGH>;
		};
		fan3_amber {
			retain-state-shutdown;
			default-state = "off";
			gpios = <&fan_leds 15 GPIO_ACTIVE_HIGH>;
		};
	};

	sled1_gpio_leds {
		compatible = "gpio-leds";
		sled1_amber {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled1_leds 0 GPIO_ACTIVE_LOW>;
		};
		sled1_blue {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled1_leds 1 GPIO_ACTIVE_LOW>;
		};
	};

	sled2_gpio_leds {
		compatible = "gpio-leds";
		sled2_amber {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled2_leds 0 GPIO_ACTIVE_LOW>;
		};
		sled2_blue {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled2_leds 1 GPIO_ACTIVE_LOW>;
		};
	};

	sled3_gpio_leds {
		compatible = "gpio-leds";
		sled3_amber {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled3_leds 0 GPIO_ACTIVE_LOW>;
		};
		sled3_blue {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled3_leds 1 GPIO_ACTIVE_LOW>;
		};
	};

	sled4_gpio_leds {
		compatible = "gpio-leds";
		sled4_amber {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled4_leds 0 GPIO_ACTIVE_LOW>;
		};
		sled4_blue {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled4_leds 1 GPIO_ACTIVE_LOW>;
		};
	};

	sled5_gpio_leds {
		compatible = "gpio-leds";
		sled5_amber {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled5_leds 0 GPIO_ACTIVE_LOW>;
		};
		sled5_blue {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled5_leds 1 GPIO_ACTIVE_LOW>;
		};
	};

	sled6_gpio_leds {
		compatible = "gpio-leds";
		sled6_amber {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled6_leds 0 GPIO_ACTIVE_LOW>;
		};
		sled6_blue {
			retain-state-shutdown;
			default-state = "keep";
			gpios = <&sled6_leds 1 GPIO_ACTIVE_LOW>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		presence-sled1 {
			label = "presence-sled1";
			gpios = <&gpio0 ASPEED_GPIO(H, 2) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(H, 2)>;
		};
		presence-sled2 {
			label = "presence-sled2";
			gpios = <&gpio0 ASPEED_GPIO(H, 3) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(H, 3)>;
		};
		presence-sled3 {
			label = "presence-sled3";
			gpios = <&gpio0 ASPEED_GPIO(H, 4) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(H, 4)>;
		};
		presence-sled4 {
			label = "presence-sled4";
			gpios = <&gpio0 ASPEED_GPIO(H, 5) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(H, 5)>;
		};
		presence-sled5 {
			label = "presence-sled5";
			gpios = <&gpio0 ASPEED_GPIO(H, 6) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(H, 6)>;
		};
		presence-sled6 {
			label = "presence-sled6";
			gpios = <&gpio0 ASPEED_GPIO(H, 7) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(H, 7)>;
		};
	};

	vbus_sled1: vbus_sled1 {
		compatible = "regulator-fixed";
		regulator-name = "vbus_sled1";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&sled1_ioexp 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vbus_sled2: vbus_sled2 {
		compatible = "regulator-fixed";
		regulator-name = "vbus_sled2";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&sled2_ioexp 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vbus_sled3: vbus_sled3 {
		compatible = "regulator-fixed";
		regulator-name = "vbus_sled3";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&sled3_ioexp 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vbus_sled4: vbus_sled4 {
		compatible = "regulator-fixed";
		regulator-name = "vbus_sled4";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&sled4_ioexp 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vbus_sled5: vbus_sled5 {
		compatible = "regulator-fixed";
		regulator-name = "vbus_sled5";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&sled5_ioexp 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vbus_sled6: vbus_sled6 {
		compatible = "regulator-fixed";
		regulator-name = "vbus_sled6";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&sled6_ioexp 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};
};

&mac2 {
	status = "okay";
	phy-mode = "rgmii";
	phy-handle = <&switchphy>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii3_default>;

	fixed-link {
		speed = <1000>;
		full-duplex;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-128.dtsi"
	};
	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
		spi-max-frequency = <50000000>;
	};
};

&spi2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi2_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-max-frequency = <50000000>;
	};
};

&i2c0 {
	status = "okay";
	ina230@45 {
		compatible = "ti,ina230";
		reg = <0x45>;
		shunt-resistor = <2000>;
	};

	mp5023@40 {
		compatible = "mps,mp5023";
		reg = <0x40>;
	};

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};

	sled1_ioexp41: pca9536@41 {
		compatible = "nxp,pca9536";
		reg = <0x41>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"SLED1_SWD_MUX", "SLED1_XRES_SWD_N",
		"SLED1_CLKREQ_N", "SLED1_PCIE_PWR_EN";
	};

	sled1_ioexp: pca9539@76 {
		compatible = "nxp,pca9539";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(M, 0) IRQ_TYPE_LEVEL_LOW>;

		gpio-line-names =
		"SLED1_MS_DETECT1","SLED1_VBUS_BMC_EN","SLED1_INA230_ALERT","SLED1_P12V_STBY_ALERT",
		"SLED1_SSD_ALERT","SLED1_MS_DETECT0","SLED1_RST_CCG5","SLED1_FUSB302_INT",
		"SLED1_MD_STBY_RESET","SLED1_MD_IOEXP_EN_FAULT","SLED1_MD_DIR","SLED1_MD_DECAY",
		"SLED1_MD_MODE1","SLED1_MD_MODE2","SLED1_MD_MODE3","power-host1";
	};

	sled1_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"led-sled1-amber","led-sled1-blue","SLED1_RST_IOEXP","SLED1_MD_REF_PWM",
		"","","","",
		"","","","",
		"","","","";
	};

	sled1_fusb302: typec-portc@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(B, 0) IRQ_TYPE_LEVEL_LOW>;
		vbus-supply = <&vbus_sled1>;

		connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			power-role = "source";
			data-role = "host";
			pd-disable;
			typec-power-opmode = "default";
		};
	};

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
	};
};

&i2c1 {
	status = "okay";
	ina230@45 {
		compatible = "ti,ina230";
		reg = <0x45>;
		shunt-resistor = <2000>;
	};

	mp5023@40 {
		compatible = "mps,mp5023";
		reg = <0x40>;
	};

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};

	sled2_ioexp41: pca9536@41 {
		compatible = "nxp,pca9536";
		reg = <0x41>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"SLED2_SWD_MUX", "SLED2_XRES_SWD_N",
		"SLED2_CLKREQ_N", "SLED2_PCIE_PWR_EN";
	};

	sled2_ioexp: pca9539@76 {
		compatible = "nxp,pca9539";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(M, 1) IRQ_TYPE_LEVEL_LOW>;

		gpio-line-names =
		"SLED2_MS_DETECT1","SLED2_VBUS_BMC_EN","SLED2_INA230_ALERT","SLED2_P12V_STBY_ALERT",
		"SLED2_SSD_ALERT","SLED2_MS_DETECT0","SLED2_RST_CCG5","SLED2_FUSB302_INT",
		"SLED2_MD_STBY_RESET","SLED2_MD_IOEXP_EN_FAULT","SLED2_MD_DIR","SLED2_MD_DECAY",
		"SLED2_MD_MODE1","SLED2_MD_MODE2","SLED2_MD_MODE3","power-host2";
	};

	sled2_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"led-sled2-amber","led-sled2-blue","SLED2_RST_IOEXP","SLED2_MD_REF_PWM",
		"","","","",
		"","","","",
		"","","","";
	};

	sled2_fusb302: typec-portc@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(B, 1) IRQ_TYPE_LEVEL_LOW>;
		vbus-supply = <&vbus_sled2>;

		connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			power-role = "source";
			data-role = "host";
			pd-disable;
			typec-power-opmode = "default";
		};
	};

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
	};
};

&i2c2 {
	status = "okay";
	ina230@45 {
		compatible = "ti,ina230";
		reg = <0x45>;
		shunt-resistor = <2000>;
	};

	mp5023@40 {
		compatible = "mps,mp5023";
		reg = <0x40>;
	};

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};

	sled3_ioexp41: pca9536@41 {
		compatible = "nxp,pca9536";
		reg = <0x41>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"SLED3_SWD_MUX", "SLED3_XRES_SWD_N",
		"SLED3_CLKREQ_N", "SLED3_PCIE_PWR_EN";
	};

	sled3_ioexp: pca9539@76 {
		compatible = "nxp,pca9539";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(M, 2) IRQ_TYPE_LEVEL_LOW>;

		gpio-line-names =
		"SLED3_MS_DETECT1","SLED3_VBUS_BMC_EN","SLED3_INA230_ALERT","SLED3_P12V_STBY_ALERT",
		"SLED3_SSD_ALERT","SLED3_MS_DETECT0","SLED3_RST_CCG5","SLED3_FUSB302_INT",
		"SLED3_MD_STBY_RESET","SLED3_MD_IOEXP_EN_FAULT","SLED3_MD_DIR","SLED3_MD_DECAY",
		"SLED3_MD_MODE1","SLED3_MD_MODE2","SLED3_MD_MODE3","power-host3";
	};

	sled3_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"led-sled3-amber","led-sled3-blue","SLED3_RST_IOEXP","SLED3_MD_REF_PWM",
		"","","","",
		"","","","",
		"","","","";
	};

	sled3_fusb302: typec-portc@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(B, 7) IRQ_TYPE_LEVEL_LOW>;
		vbus-supply = <&vbus_sled3>;

		connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			power-role = "source";
			data-role = "host";
			pd-disable;
			typec-power-opmode = "default";
		};
	};

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
	};
};

&i2c3 {
	status = "okay";
	ina230@45 {
		compatible = "ti,ina230";
		reg = <0x45>;
		shunt-resistor = <2000>;
	};

	mp5023@40 {
		compatible = "mps,mp5023";
		reg = <0x40>;
	};

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};

	sled4_ioexp41: pca9536@41 {
		compatible = "nxp,pca9536";
		reg = <0x41>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"SLED4_SWD_MUX", "SLED4_XRES_SWD_N",
		"SLED4_CLKREQ_N", "SLED4_PCIE_PWR_EN";
	};

	sled4_ioexp: pca9539@76 {
		compatible = "nxp,pca9539";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(M, 3) IRQ_TYPE_LEVEL_LOW>;

		gpio-line-names =
		"SLED4_MS_DETECT1","SLED4_VBUS_BMC_EN","SLED4_INA230_ALERT","SLED4_P12V_STBY_ALERT",
		"SLED4_SSD_ALERT","SLED4_MS_DETECT0","SLED4_RST_CCG5","SLED4_FUSB302_INT",
		"SLED4_MD_STBY_RESET","SLED4_MD_IOEXP_EN_FAULT","SLED4_MD_DIR","SLED4_MD_DECAY",
		"SLED4_MD_MODE1","SLED4_MD_MODE2","SLED4_MD_MODE3","power-host4";
	};

	sled4_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"led-sled4-amber","led-sled4-blue","SLED4_RST_IOEXP","SLED4_MD_REF_PWM",
		"","","","",
		"","","","",
		"","","","";
	};

	sled4_fusb302: typec-portc@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(S, 7) IRQ_TYPE_LEVEL_LOW>;
		vbus-supply = <&vbus_sled4>;

		connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			power-role = "source";
			data-role = "host";
			pd-disable;
			typec-power-opmode = "default";
		};
	};

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
	};
};

&i2c4 {
	status = "okay";
	ina230@45 {
		compatible = "ti,ina230";
		reg = <0x45>;
		shunt-resistor = <2000>;
	};

	mp5023@40 {
		compatible = "mps,mp5023";
		reg = <0x40>;
	};

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};

	sled5_ioexp41: pca9536@41 {
		compatible = "nxp,pca9536";
		reg = <0x41>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"SLED5_SWD_MUX", "SLED5_XRES_SWD_N",
		"SLED5_CLKREQ_N", "SLED5_PCIE_PWR_EN";
	};

	sled5_ioexp: pca9539@76 {
		compatible = "nxp,pca9539";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(M, 4) IRQ_TYPE_LEVEL_LOW>;

		gpio-line-names =
		"SLED5_MS_DETECT1","SLED5_VBUS_BMC_EN","SLED5_INA230_ALERT","SLED5_P12V_STBY_ALERT",
		"SLED5_SSD_ALERT","SLED5_MS_DETECT0","SLED5_RST_CCG5","SLED5_FUSB302_INT",
		"SLED5_MD_STBY_RESET","SLED5_MD_IOEXP_EN_FAULT","SLED5_MD_DIR","SLED5_MD_DECAY",
		"SLED5_MD_MODE1","SLED5_MD_MODE2","SLED5_MD_MODE3","power-host5";
	};

	sled5_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"led-sled5-amber","led-sled5-blue","SLED5_RST_IOEXP","SLED5_MD_REF_PWM",
		"","","","",
		"","","","",
		"","","","";
	};

	sled5_fusb302: typec-portc@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(Y, 3) IRQ_TYPE_LEVEL_LOW>;
		vbus-supply = <&vbus_sled5>;

		connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			power-role = "source";
			data-role = "host";
			pd-disable;
			typec-power-opmode = "default";
		};
	};

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
	};
};

&i2c5 {
	status = "okay";
	ina230@45 {
		compatible = "ti,ina230";
		reg = <0x45>;
		shunt-resistor = <2000>;
	};

	mp5023@40 {
		compatible = "mps,mp5023";
		reg = <0x40>;
	};

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};

	sled6_ioexp41: pca9536@41 {
		compatible = "nxp,pca9536";
		reg = <0x41>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"SLED6_SWD_MUX", "SLED6_XRES_SWD_N",
		"SLED6_CLKREQ_N", "SLED6_PCIE_PWR_EN";
	};

	sled6_ioexp: pca9539@76 {
		compatible = "nxp,pca9539";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(M, 5) IRQ_TYPE_LEVEL_LOW>;

		gpio-line-names =
		"SLED6_MS_DETECT1","SLED6_VBUS_BMC_EN","SLED6_INA230_ALERT","SLED6_P12V_STBY_ALERT",
		"SLED6_SSD_ALERT","SLED6_MS_DETECT0","SLED6_RST_CCG5","SLED6_FUSB302_INT",
		"SLED6_MD_STBY_RESET","SLED6_MD_IOEXP_EN_FAULT","SLED6_MD_DIR","SLED6_MD_DECAY",
		"SLED6_MD_MODE1","SLED6_MD_MODE2","SLED6_MD_MODE3","power-host6";
	};

	sled6_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"led-sled6-amber","led-sled6-blue","SLED6_RST_IOEXP","SLED6_MD_REF_PWM",
		"","","","",
		"","","","",
		"","","","";
	};

	sled6_fusb302: typec-portc@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;

		interrupt-parent = <&gpio0>;
		interrupts = <ASPEED_GPIO(I, 7) IRQ_TYPE_LEVEL_LOW>;
		vbus-supply = <&vbus_sled6>;

		connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			power-role = "source";
			data-role = "host";
			pd-disable;
			typec-power-opmode = "default";
		};
	};

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
	};
};

&i2c6 {
	status = "okay";

	eeprom@56 {
		compatible = "atmel,24c64";
		reg = <0x56>;
	};

	rtc@51 {
		/* in-chip rtc disabled, use external rtc (battery-backed) */
		compatible = "nxp,pcf85263";
		reg = <0x51>;
	};
};

&i2c7 {
	status = "okay";

	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
	};
};

&i2c9 {
	status = "okay";

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};
};

&i2c10 {
	status = "okay";

	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};

	front_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"led-fault-identify","power-p5v-stby-good",
		"power-p1v0-dvdd-good","power-p1v0-avdd-good",
		"","","","",
		"","","","",
		"","","","";
	};
};

&i2c12 {
	status = "okay";

	adm1278@11 {
		compatible = "adi,adm1278";
		reg = <0x11>;
		shunt-resistor-micro-ohms = <300>;
		adi,volt-curr-sample-average = <128>;
		adi,power-sample-average = <128>;
	};

	tmp421@4c {
		compatible = "ti,tmp421";
		reg = <0x4c>;
	};

	tmp421@4d {
		compatible = "ti,tmp421";
		reg = <0x4d>;
	};

	fan_leds: pca9552@67 {
		compatible = "nxp,pca9552";
		reg = <0x67>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
		"presence-fan0","presence-fan1",
		"presence-fan2","presence-fan3",
		"power-fan0-good","power-fan1-good",
		"power-fan2-good","power-fan3-good",
		"","","","",
		"","","","";
	};
};

&i2c13 {
	multi-master;
	aspeed,hw-timeout-ms = <1000>;
	status = "okay";

	//USB Debug Connector
	ipmb13@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&gpio0 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_gpiov2_unbiased_default>;

	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"FUSB302_SLED1_INT_N","FUSB302_SLED2_INT_N",
			"SEL_SPI2_MUX","SPI2_MUX1",
			"SPI2_MUX2","SPI2_MUX3",
			"","FUSB302_SLED3_INT_N",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"BMC_SLED1_STCK","BMC_SLED2_STCK",
			"BMC_SLED3_STCK","BMC_SLED4_STCK",
			"BMC_SLED5_STCK","BMC_SLED6_STCK",
			"","",
	/*G0-G7*/	"BSM_FRU_WP","SWITCH_FRU_MUX","","FM_SOL_UART_CH_SEL",
			"PWRGD_P1V05_VDDCORE","PWRGD_P1V5_VDD","","",
	/*H0-H7*/	"presence-riser1","presence-riser2",
			"presence-sled1","presence-sled2",
			"presence-sled3","presence-sled4",
			"presence-sled5","presence-sled6",
	/*I0-I7*/	"REV_ID0","",
			"REV_ID1","REV_ID2",
			"","BSM_FLASH_WP_STATUS",
			"BMC_TPM_PRES_N","FUSB302_SLED6_INT_N",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","BMC_RTC_INT","","",
	/*M0-M7*/	"ALERT_SLED1_N","ALERT_SLED2_N",
			"ALERT_SLED3_N","ALERT_SLED4_N",
			"ALERT_SLED5_N","ALERT_SLED6_N",
			"","USB_DEBUG_PWR_BTN_N",
	/*N0-N7*/	"LED_POSTCODE_0","LED_POSTCODE_1",
			"LED_POSTCODE_2","LED_POSTCODE_3",
			"LED_POSTCODE_4","LED_POSTCODE_5",
			"LED_POSTCODE_6","LED_POSTCODE_7",
	/*O0-O7*/	"","","","",
			"","BOARD_ID0","BOARD_ID1","BOARD_ID2",
	/*P0-P7*/	"","","","","","","","BMC_HEARTBEAT",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","BAT_DETECT",
			"BMC_BT_WP0_N","BMC_BT_WP1_N","","FUSB302_SLED4_INT_N",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"PWRGD_CNS_PSU","RST_BMC_MVL_N",
			"P12V_AUX_ALERT1_N","PSU_PRSNT",
			"USB2_SEL0_A","USB2_SEL1_A",
			"USB2_SEL0_B","USB2_SEL1_B",
	/*W0-W7*/	"RST_FRONT_IOEXP_N","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"BMC_SELF_HW_RST","BSM_PRSNT_N",
			"BSM_FLASH_LATCH_N","FUSB302_SLED5_INT_N",
			"","","","",
	/*Z0-Z7*/	"","","","","","","","";
};

&adc0 {
	vref = <1800>;
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default &pinctrl_adc1_default
		&pinctrl_adc2_default &pinctrl_adc3_default
		&pinctrl_adc4_default &pinctrl_adc5_default
		&pinctrl_adc6_default &pinctrl_adc7_default>;
};

&adc1 {
	vref = <2500>;
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc9_default
		&pinctrl_adc10_default &pinctrl_adc11_default
		&pinctrl_adc12_default &pinctrl_adc13_default
		&pinctrl_adc14_default &pinctrl_adc15_default>;
};

&mdio0 {
	status = "okay";
	/* TODO: Add Marvell 88E6191X */
};

&mdio3 {
	status = "okay";
	/* TODO: Add Marvell 88X3310 */
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&emmc_controller {
	status = "okay";
};

&emmc {
	status = "okay";
};

&pinctrl {
	pinctrl_gpiov2_unbiased_default: gpiov2 {
		pins = "AD14";
		bias-disable;
	};
};

&wdt1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
	aspeed,reset-type = "soc";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;
	aspeed,ext-pulse-duration = <256>;
};
