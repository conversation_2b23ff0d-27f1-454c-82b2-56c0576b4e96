// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2018 <PERSON><PERSON> <<EMAIL>>
 * Copyright (c) 2018 <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "meson8m2.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "Tronsmart MXIII Plus";
	compatible = "tronsmart,mxiii-plus", "amlogic,meson8m2";

	aliases {
		ethernet0 = &ethmac;
		i2c0 = &i2c_AO;
		serial0 = &uart_AO;
		mmc0 = &sd_card_slot;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x80000000>;
	};

	adc-keys {
		compatible = "adc-keys";
		io-channels = <&saradc 0>;
		io-channel-names = "buttons";
		keyup-threshold-microvolt = <1710000>;

		button-function {
			label = "Function";
			linux,code = <KEY_FN>;
			press-threshold-microvolt = <10000>;
		};
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";

		pinctrl-0 = <&xtal_32k_out_pins>;
		pinctrl-names = "default";

		reset-gpios = <&gpio GPIOX_11 GPIO_ACTIVE_LOW>,
			      <&gpio_ao GPIOAO_6 GPIO_ACTIVE_LOW>;

		clocks = <&xtal_32k_out>;
		clock-names = "ext_clock";
	};

	vcc_3v3: regulator-vcc3v3 {
		compatible = "regulator-fixed";
		regulator-name = "VCC3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	xtal_32k_out: xtal-32k-out-clk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "xtal_32k_out";
	};
};

&cpu0 {
	cpu-supply = <&vcck>;
};

&ethmac {
	status = "okay";

	pinctrl-0 = <&eth_rgmii_pins>;
	pinctrl-names = "default";

	phy-handle = <&eth_phy0>;
	phy-mode = "rgmii-id";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		eth_phy0: ethernet-phy@0 {
			/* Realtek RTL8211F (0x001cc916) */
			reg = <0>;

			reset-assert-us = <10000>;
			reset-deassert-us = <80000>;
			reset-gpios = <&gpio GPIOH_4 GPIO_ACTIVE_LOW>;
		};
	};
};

&ir_receiver {
	status = "okay";
	pinctrl-0 = <&ir_recv_pins>;
	pinctrl-names = "default";
};

&i2c_AO {
	status = "okay";
	pinctrl-0 = <&i2c_ao_pins>;
	pinctrl-names = "default";

	pmic@32 {
		compatible = "ricoh,rn5t618";
		reg = <0x32>;
		system-power-controller;

		regulators {
			vcck: DCDC1 {
				regulator-name = "VCCK";
				regulator-min-microvolt = <825000>;
				regulator-max-microvolt = <1150000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vddee: DCDC2 {
				/* the output is also used as VDDAO */
				regulator-name = "VDD_EE";
				regulator-min-microvolt = <950000>;
				regulator-max-microvolt = <1150000>;
				regulator-boot-on;
				regulator-always-on;
			};

			DCDC3 {
				regulator-name = "VDD_DDR";
				regulator-min-microvolt = <1500000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDO1 {
				regulator-name = "VDDIO_AO28";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vddio_ao1v8: LDO2 {
				regulator-name = "VDDIO_AO18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDO3 {
				regulator-name = "VCC1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDO4 {
				regulator-name = "VCC2V8";
				regulator-min-microvolt = <2850000>;
				regulator-max-microvolt = <2850000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDO5 {
				regulator-name = "AVDD1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDORTC1 {
				regulator-name = "VDD_LDO";
				regulator-min-microvolt = <2700000>;
				regulator-max-microvolt = <2700000>;
				regulator-boot-on;
				regulator-always-on;
			};

			LDORTC2 {
				regulator-name = "RTC_0V9";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <900000>;
				regulator-boot-on;
				regulator-always-on;
			};
		};
	};
};

&mali {
	mali-supply = <&vddee>;
};

&saradc {
	status = "okay";
	vref-supply = <&vddio_ao1v8>;
};

/* SDIO wifi */
&sdhc {
	status = "okay";

	pinctrl-0 = <&sdxc_a_pins>;
	pinctrl-names = "default";

	bus-width = <4>;
	max-frequency = <50000000>;

	disable-wp;
	non-removable;
	cap-mmc-highspeed;
	cap-sd-highspeed;

	mmc-pwrseq = <&sdio_pwrseq>;

	vmmc-supply = <&vcc_3v3>;
	vqmmc-supply = <&vcc_3v3>;
};

&sdio {
	status = "okay";

	pinctrl-0 = <&sd_b_pins>;
	pinctrl-names = "default";

	/* SD card */
	sd_card_slot: slot@1 {
		compatible = "mmc-slot";
		reg = <1>;
		status = "okay";

		bus-width = <4>;
		no-sdio;
		cap-mmc-highspeed;
		cap-sd-highspeed;
		disable-wp;

		cd-gpios = <&gpio CARD_6 GPIO_ACTIVE_LOW>;

		vmmc-supply = <&vcc_3v3>;
	};
};

/* connected to the Bluetooth module */
&uart_A {
	status = "okay";
	pinctrl-0 = <&uart_a1_pins>, <&uart_a1_cts_rts_pins>;
	pinctrl-names = "default";
	uart-has-rtscts;

	bluetooth {
		compatible = "brcm,bcm20702a1";
		shutdown-gpios = <&gpio GPIOX_20 GPIO_ACTIVE_HIGH>;
		max-speed = <2000000>;
	};
};

&uart_AO {
	status = "okay";
	pinctrl-0 = <&uart_ao_a_pins>;
	pinctrl-names = "default";
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&usb0_phy {
	status = "okay";
};

&usb1_phy {
	status = "okay";
};
