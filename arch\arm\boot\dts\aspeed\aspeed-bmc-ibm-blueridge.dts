// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2024 IBM Corp.
/dts-v1/;

#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/leds/leds-pca955x.h>
#include "aspeed-g6.dtsi"
#include "ibm-power11-quad.dtsi"

/ {
	model = "Blueridge 2U";
	compatible = "ibm,blueridge-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
		i2c16 = &i2c2mux0;
		i2c17 = &i2c2mux1;
		i2c18 = &i2c2mux2;
		i2c19 = &i2c2mux3;
		i2c20 = &i2c4mux0chn0;
		i2c21 = &i2c4mux0chn1;
		i2c22 = &i2c4mux0chn2;
		i2c23 = &i2c5mux0chn0;
		i2c24 = &i2c5mux0chn1;
		i2c25 = &i2c6mux0chn0;
		i2c26 = &i2c6mux0chn1;
		i2c27 = &i2c6mux0chn2;
		i2c28 = &i2c6mux0chn3;
		i2c29 = &i2c11mux0chn0;
		i2c30 = &i2c11mux0chn1;
	};

	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		event_log: region@b3d00000 {
			reg = <0xb3d00000 0x100000>;
			no-map;
		};

		ramoops@b3e00000 {
			compatible = "ramoops";
			reg = <0xb3e00000 0x200000>; /* 16 * (4 * 0x8000) */
			record-size = <0x8000>;
			console-size = <0x8000>;
			ftrace-size = <0x8000>;
			pmsg-size = <0x8000>;
			max-reason = <3>; /* KMSG_DUMP_EMERG */
		};

		/* LPC FW cycle bridge region requires natural alignment */
		flash_memory: region@b4000000 {
			reg = <0xb4000000 0x04000000>; /* 64M */
			no-map;
		};

		/* VGA region is dictated by hardware strapping */
		vga_memory: region@bf000000 {
			compatible = "shared-dma-pool";
			reg = <0xbf000000 0x01000000>;  /* 16M */
			no-map;
		};
	};

	i2c-mux {
		compatible = "i2c-mux-gpio";
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-parent = <&i2c2>;
		idle-state = <0>;
		mux-gpios = <&gpio0 ASPEED_GPIO(G, 4) GPIO_ACTIVE_HIGH>,
			    <&gpio0 ASPEED_GPIO(G, 5) GPIO_ACTIVE_HIGH>;

		i2c2mux0: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2mux1: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2mux2: i2c@2 {
			reg = <2>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2mux3: i2c@3 {
			reg = <3>;
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};

	leds {
		compatible = "gpio-leds";

		/* BMC Card fault LED at the back */
		led-bmc-ingraham0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 1) GPIO_ACTIVE_LOW>;
		};

		/* Enclosure ID LED at the back */
		led-rear-enc-id0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 2) GPIO_ACTIVE_LOW>;
		};

		/* Enclosure fault LED at the back */
		led-rear-enc-fault0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 3) GPIO_ACTIVE_LOW>;
		};

		/* PCIE slot power LED */
		led-pcieslot-power {
			gpios = <&gpio0 ASPEED_GPIO(P, 4) GPIO_ACTIVE_LOW>;
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-fan0-presence {
			gpios = <&pca0 6 GPIO_ACTIVE_LOW>;
			label = "fan0-presence";
			linux,code = <6>;
		};

		event-fan1-presence {
			gpios = <&pca0 7 GPIO_ACTIVE_LOW>;
			label = "fan1-presence";
			linux,code = <7>;
		};

		event-fan2-presence {
			gpios = <&pca0 8 GPIO_ACTIVE_LOW>;
			label = "fan2-presence";
			linux,code = <8>;
		};

		event-fan3-presence {
			gpios = <&pca0 9 GPIO_ACTIVE_LOW>;
			label = "fan3-presence";
			linux,code = <9>;
		};

		event-fan4-presence {
			gpios = <&pca0 10 GPIO_ACTIVE_LOW>;
			label = "fan4-presence";
			linux,code = <10>;
		};

		event-fan5-presence {
			gpios = <&pca0 11 GPIO_ACTIVE_LOW>;
			label = "fan5-presence";
			linux,code = <11>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc1 7>;
	};
};

&adc1 {
	status = "okay";
	aspeed,int-vref-microvolt = <2500000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc9_default
		&pinctrl_adc10_default &pinctrl_adc11_default
		&pinctrl_adc12_default &pinctrl_adc13_default
		&pinctrl_adc14_default &pinctrl_adc15_default>;
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"bmc-management-ready","","","","","","checkstop","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","rtc-battery-voltage-read-enable","reset-cause-pinhole","","",
			"factory-reset-toggle","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","led-bmc-ingraham0","led-rear-enc-id0","led-rear-enc-fault0","","","",
			"",
	/*I0-I7*/	"","","","","","","bmc-secure-boot","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","usb-power","","","","",
	/*P0-P7*/	"","","","","led-pcieslot-power","","","",
	/*Q0-Q7*/	"cfam-reset","","regulator-standby-faulted","","","","","",
	/*R0-R7*/	"bmc-tpm-reset","power-chassis-control","power-chassis-good","","","","",
			"",
	/*S0-S7*/	"presence-ps0","presence-ps1","presence-ps2","presence-ps3",
			"power-ffs-sync-history","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","";

	i2c3-mux-oe-n-hog {
		gpio-hog;
		gpios = <ASPEED_GPIO(G, 6) GPIO_ACTIVE_LOW>;
		line-name = "I2C3_MUX_OE_N";
		output-high;
	};

	usb-power-hog {
		gpio-hog;
		gpios = <ASPEED_GPIO(O, 3) GPIO_ACTIVE_LOW>;
		output-high;
	};
};

&emmc_controller {
	status = "okay";
};

&pinctrl_emmc_default {
	bias-disable;
};

&emmc {
	status = "okay";
	clk-phase-mmc-hs200 = <180>, <180>;
};

&ibt {
	status = "okay";
};

&i2c0 {
	status = "okay";

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	gpio@20 {
		compatible = "ti,tca9554";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names = "",
			"RUSSEL_FW_I2C_ENABLE_N",
			"RUSSEL_OPPANEL_PRESENCE_N",
			"BLYTH_OPPANEL_PRESENCE_N",
			"CPU_TPM_CARD_PRESENT_N",
			"DASD_BP2_PRESENT_N",
			"DASD_BP1_PRESENT_N",
			"DASD_BP0_PRESENT_N";
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";

	power-supply@68 {
		compatible = "ibm,cffps";
		reg = <0x68>;
	};

	power-supply@69 {
		compatible = "ibm,cffps";
		reg = <0x69>;
	};

	led-controller@61 {
		compatible = "nxp,pca9552";
		reg = <0x61>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"SLOT0_PRSNT_EN_RSVD", "SLOT1_PRSNT_EN_RSVD",
			"SLOT2_PRSNT_EN_RSVD", "SLOT3_PRSNT_EN_RSVD",
			"SLOT4_PRSNT_EN_RSVD", "SLOT0_EXPANDER_PRSNT_N",
			"SLOT1_EXPANDER_PRSNT_N", "SLOT2_EXPANDER_PRSNT_N",
			"SLOT3_EXPANDER_PRSNT_N", "SLOT4_EXPANDER_PRSNT_N",
			"", "", "", "", "", "";
	};
};

&i2c4 {
	status = "okay";

	temperature-sensor@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	temperature-sensor@49 {
		compatible = "ti,tmp275";
		reg = <0x49>;
	};

	temperature-sensor@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c4mux0chn0: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			led-controller@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;
				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					reg = <0>;
					default-state = "keep";
					label = "cablecard0-cxp-top";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					reg = <1>;
					default-state = "keep";
					label = "cablecard0-cxp-bot";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c4mux0chn1: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};
		};

		i2c4mux0chn2: i2c@2 {
			reg = <2>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};
		};
	};
};

&i2c5 {
	status = "okay";

	temperature-sensor@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	temperature-sensor@49 {
		compatible = "ti,tmp275";
		reg = <0x49>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c5mux0chn0: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			led-controller@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;
				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					reg = <0>;
					default-state = "keep";
					label = "cablecard3-cxp-top";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					reg = <1>;
					default-state = "keep";
					label = "cablecard3-cxp-bot";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c5mux0chn1: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};

			led-controller@61 {
				compatible = "nxp,pca9551";
				reg = <0x61>;
				#address-cells = <1>;
				#size-cells = <0>;
				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					reg = <0>;
					default-state = "keep";
					label = "cablecard4-cxp-top";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					reg = <1>;
					default-state = "keep";
					label = "cablecard4-cxp-bot";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};
			};
		};
	};
};

&i2c6 {
	status = "okay";

	temperature-sensor@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	temperature-sensor@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};

	temperature-sensor@4b {
		compatible = "ti,tmp275";
		reg = <0x4b>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c6mux0chn0: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c6mux0chn1: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};
		};

		i2c6mux0chn2: i2c@2 {
			reg = <2>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};

		i2c6mux0chn3: i2c@3 {
			reg = <3>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};
		};
	};
};

&i2c7 {
	multi-master;
	status = "okay";

	led-controller@30 {
		compatible = "ibm,pca9552";
		reg = <0x30>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "pcieslot0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "pcieslot1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "pcieslot2";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "pcieslot3";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "pcieslot4";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "cpu1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			reg = <6>;
			default-state = "keep";
			label = "cpu-vrm1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			reg = <8>;
			default-state = "keep";
			label = "lcd-russel";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};

	led-controller@31 {
		compatible = "ibm,pca9552";
		reg = <0x31>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "ddimm0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "ddimm1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "ddimm2";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "ddimm3";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "ddimm4";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "ddimm5";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			reg = <6>;
			default-state = "keep";
			label = "ddimm6";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			reg = <7>;
			default-state = "keep";
			label = "ddimm7";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			reg = <8>;
			default-state = "keep";
			label = "ddimm8";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			reg = <9>;
			default-state = "keep";
			label = "ddimm9";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@a {
			reg = <10>;
			default-state = "keep";
			label = "ddimm10";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@b {
			reg = <11>;
			default-state = "keep";
			label = "ddimm11";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@c {
			reg = <12>;
			default-state = "keep";
			label = "ddimm12";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@d {
			reg = <13>;
			default-state = "keep";
			label = "ddimm13";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@e {
			reg = <14>;
			default-state = "keep";
			label = "ddimm14";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@f {
			reg = <15>;
			default-state = "keep";
			label = "ddimm15";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};

	led-controller@32 {
		compatible = "ibm,pca9552";
		reg = <0x32>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "ddimm16";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "ddimm17";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "ddimm18";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "ddimm19";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "ddimm20";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "ddimm21";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			reg = <6>;
			default-state = "keep";
			label = "ddimm22";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			reg = <7>;
			default-state = "keep";
			label = "ddimm23";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			reg = <8>;
			default-state = "keep";
			label = "ddimm24";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			reg = <9>;
			default-state = "keep";
			label = "ddimm25";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@a {
			reg = <10>;
			default-state = "keep";
			label = "ddimm26";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@b {
			reg = <11>;
			default-state = "keep";
			label = "ddimm27";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@c {
			reg = <12>;
			default-state = "keep";
			label = "ddimm28";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@d {
			reg = <13>;
			default-state = "keep";
			label = "ddimm29";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@e {
			reg = <14>;
			default-state = "keep";
			label = "ddimm30";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@f {
			reg = <15>;
			default-state = "keep";
			label = "ddimm31";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};

	led-controller@33 {
		compatible = "ibm,pca9552";
		reg = <0x33>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "planar";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "cpu0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "dasd-pyramid0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "dasd-pyramid1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "dasd-pyramid2";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			reg = <6>;
			default-state = "keep";
			label = "cpu0-vrm0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			reg = <7>;
			default-state = "keep";
			label = "rtc-battery";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			reg = <8>;
			default-state = "keep";
			label = "base-blyth";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			reg = <9>;
			default-state = "keep";
			label = "pcieslot6";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@a {
			reg = <10>;
			default-state = "keep";
			label = "pcieslot7";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@b {
			reg = <11>;
			default-state = "keep";
			label = "pcieslot8";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@c {
			reg = <12>;
			default-state = "keep";
			label = "pcieslot9";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@d {
			reg = <13>;
			default-state = "keep";
			label = "pcieslot10";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@e {
			reg = <14>;
			default-state = "keep";
			label = "pcieslot11";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@f {
			reg = <15>;
			default-state = "keep";
			label = "tpm-wilson";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};

	humidity-sensor@40 {
		compatible = "silabs,si7020";
		reg = <0x40>;
	};

	temperature-sensor@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	pwm@52 {
		compatible = "maxim,max31785a";
		reg = <0x52>;
	};

	led-controller@60 {
		compatible = "nxp,pca9551";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "front-sys-id0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "front-check-log0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "front-enc-fault1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "front-sys-pwron0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};

	pca0: led-controller@61 {
		compatible = "nxp,pca9552";
		reg = <0x61>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "fan0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "fan1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "fan2";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "fan3";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "fan4";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "fan5";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};

	lcd-controller@62 {
		compatible = "ibm,op-panel";
		reg = <(0x62 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	pressure-sensor@76 {
		compatible = "infineon,dps310";
		reg = <0x76>;
		#io-channel-cells = <0>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};
};

&i2c8 {
	status = "okay";

	pmic@11 {
		compatible = "ti,ucd90320";
		reg = <0x11>;
	};

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};

	temperature-sensor@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	temperature-sensor@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	led-controller@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"", "", "", "", "", "", "P10_DCM0_PRES", "P10_DCM1_PRES",
			"", "", "", "", "PRESENT_VRM_DCM0_N", "PRESENT_VRM_DCM1_N",
			"power-config-full-load", "";
	};

	led-controller@61 {
		compatible = "nxp,pca9552";
		reg = <0x61>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"SLOT6_PRSNT_EN_RSVD", "SLOT7_PRSNT_EN_RSVD",
			"SLOT8_PRSNT_EN_RSVD", "SLOT9_PRSNT_EN_RSVD",
			"SLOT10_PRSNT_EN_RSVD", "SLOT11_PRSNT_EN_RSVD",
			"SLOT6_EXPANDER_PRSNT_N", "SLOT7_EXPANDER_PRSNT_N",
			"SLOT8_EXPANDER_PRSNT_N", "SLOT9_EXPANDER_PRSNT_N",
			"SLOT10_EXPANDER_PRSNT_N", "SLOT11_EXPANDER_PRSNT_N",
			"", "", "", "";
	};

};

&i2c9 {
	status = "okay";

	temperature-sensor@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	temperature-sensor@4d {
		compatible = "ti,tmp423";
		reg = <0x4d>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};
};

&i2c10 {
	status = "okay";

	temperature-sensor@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	temperature-sensor@4d {
		compatible = "ti,tmp423";
		reg = <0x4d>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};
};

&i2c11 {
	status = "okay";

	temperature-sensor@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	temperature-sensor@49 {
		compatible = "ti,tmp275";
		reg = <0x49>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		i2c11mux0chn0: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			led-controller@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;
				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					reg = <0>;
					default-state = "keep";
					label = "cablecard10-cxp-top";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					reg = <1>;
					default-state = "keep";
					label = "cablecard10-cxp-bot";
					retain-state-shutdown;
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c11mux0chn1: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};
		};
	};
};

&i2c12 {
	status = "okay";

	tpm@2e {
		compatible = "nuvoton,npct75x", "tcg,tpm-tis-i2c";
		reg = <0x2e>;
		memory-region = <&event_log>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};
};

&i2c13 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	led-controller@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "nvme0";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "nvme1";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "nvme2";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "nvme3";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "nvme4";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "nvme5";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			reg = <6>;
			default-state = "keep";
			label = "nvme6";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			reg = <7>;
			default-state = "keep";
			label = "nvme7";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&i2c14 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	led-controller@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "nvme8";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "nvme9";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "nvme10";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "nvme11";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "nvme12";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "nvme13";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			reg = <6>;
			default-state = "keep";
			label = "nvme14";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			reg = <7>;
			default-state = "keep";
			label = "nvme15";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&i2c15 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	led-controller@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			reg = <0>;
			default-state = "keep";
			label = "nvme16";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			reg = <1>;
			default-state = "keep";
			label = "nvme17";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			reg = <2>;
			default-state = "keep";
			label = "nvme18";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			reg = <3>;
			default-state = "keep";
			label = "nvme19";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			reg = <4>;
			default-state = "keep";
			label = "nvme20";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			reg = <5>;
			default-state = "keep";
			label = "nvme21";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			reg = <6>;
			default-state = "keep";
			label = "nvme22";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			reg = <7>;
			default-state = "keep";
			label = "nvme23";
			retain-state-shutdown;
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&uart2 {
	status = "okay";
};

&vuart1 {
	status = "okay";
};

&vuart2 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC3CLK>,
		 <&syscon ASPEED_CLK_MAC3RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC4CLK>,
		 <&syscon ASPEED_CLK_MAC4RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	status = "okay";
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8 0xcac>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
	aspeed,lpc-interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
};
