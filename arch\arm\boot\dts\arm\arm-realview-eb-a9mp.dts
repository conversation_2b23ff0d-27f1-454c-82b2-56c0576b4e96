/*
 * Copyright 2016 Linaro Ltd
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

/dts-v1/;
#include "arm-realview-eb-mp.dtsi"

/ {
	model = "ARM RealView EB Cortex A9 MPCore";

	/*
	 * This is the Cortex A9 MPCore tile used with the
	 * RealView EB.
	 */
	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "arm,realview-smp";

		A9_0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;
			next-level-cache = <&L2>;
		};

		A9_1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <1>;
			next-level-cache = <&L2>;
		};

		A9_2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <2>;
			next-level-cache = <&L2>;
		};

		A9_3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <3>;
			next-level-cache = <&L2>;
		};
	};
};

&pmu {
	interrupt-affinity = <&A9_0>, <&A9_1>, <&A9_2>, <&A9_3>;
};
