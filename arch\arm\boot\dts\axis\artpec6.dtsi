/*
 * Device Tree Source for the Axis ARTPEC-6 SoC
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/dma/nbpfaxi.h>
#include <dt-bindings/clock/axis,artpec6-clkctrl.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "axis,artpec6";
	interrupt-parent = <&intc>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;
			next-level-cache = <&pl310>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <1>;
			next-level-cache = <&pl310>;
		};
	};

	syscon: syscon@f8000000 {
		compatible = "axis,artpec6-syscon", "syscon";
		reg = <0xf8000000 0x48>;
	};

	psci {
		compatible = "arm,psci-0.2", "arm,psci";
		method = "smc";
		psci_version = <0x84000000>;
		cpu_on = <0x84000003>;
		system_reset = <0x84000009>;
	};

	scu@faf00000 {
		compatible = "arm,cortex-a9-scu";
		reg = <0xfaf00000 0x58>;
	};

	/* Main external clock driving CPU and peripherals */
	ext_clk: ext_clk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <50000000>;
	};

	eth_phy_ref_clk: eth_phy_ref_clk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <125000000>;
	};

	clkctrl: clkctrl@f8000000 {
		#clock-cells = <1>;
		compatible = "axis,artpec6-clkctrl";
		reg = <0xf8000000 0x48>;
		clocks = <&ext_clk>;
		clock-names = "sys_refclk";
	};

	gtimer@faf00200 {
		compatible = "arm,cortex-a9-global-timer";
		reg = <0xfaf00200 0x20>;
		interrupts = <GIC_PPI 11 0xf01>;
		clocks = <&clkctrl ARTPEC6_CLK_CPU_PERIPH>;
	};

	timer@faf00600 {
		compatible = "arm,cortex-a9-twd-timer";
		reg = <0xfaf00600 0x20>;
		interrupts = <GIC_PPI 13 0xf04>;
		clocks = <&clkctrl ARTPEC6_CLK_CPU_PERIPH>;
		status = "disabled";
	};

	intc: interrupt-controller@faf01000 {
		interrupt-controller;
		compatible = "arm,cortex-a9-gic";
		#interrupt-cells = <3>;
		reg = < 0xfaf01000 0x1000 >, < 0xfaf00100 0x0100 >;
	};

	pl310: cache-controller@faf10000 {
		compatible = "arm,pl310-cache";
		cache-unified;
		cache-level = <2>;
		reg = <0xfaf10000 0x1000>;
		interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
		arm,data-latency = <1 1 1>;
		arm,tag-latency = <1 1 1>;
		arm,filter-ranges = <0x0 0x80000000>;
		arm,double-linefill = <1>;
		arm,double-linefill-incr = <0>;
		arm,double-linefill-wrap = <0>;
		prefetch-data = <1>;
		prefetch-instr = <1>;
		arm,prefetch-offset = <0>;
		arm,prefetch-drop = <1>;
	};

	pmu {
		compatible = "arm,cortex-a9-pmu";
		interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>;
	};

	/*
	 * Both pci nodes cannot be enabled at the same time,
	 * leave the unwanted node as disabled.
	 */
	pcie: pcie@f8050000 {
		compatible = "axis,artpec6-pcie", "snps,dw-pcie";
		reg = <0xf8050000 0x2000
		       0xf8040000 0x1000
		       0xc0000000 0x2000>;
		reg-names = "dbi", "phy", "config";
		#address-cells = <3>;
		#size-cells = <2>;
		device_type = "pci";
			  /* downstream I/O */
		ranges = <0x81000000 0 0 0xc0002000 0 0x00010000
			  /* non-prefetchable memory */
			  0x82000000 0 0xc0012000 0xc0012000 0 0x1ffee000>;
		num-lanes = <2>;
		bus-range = <0x00 0xff>;
		interrupts = <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "msi";
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 0x7>;
		interrupt-map = <0 0 0 1 &intc GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>,
				<0 0 0 2 &intc GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>,
				<0 0 0 3 &intc GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>,
				<0 0 0 4 &intc GIC_SPI 147 IRQ_TYPE_LEVEL_HIGH>;
		axis,syscon-pcie = <&syscon>;
		status = "disabled";
	};

	pcie_ep: pcie_ep@f8050000 {
		compatible = "axis,artpec6-pcie-ep", "snps,dw-pcie";
		reg = <0xf8050000 0x2000
		       0xf8051000 0x2000
		       0xf8040000 0x1000
		       0xc0000000 0x20000000>;
		reg-names = "dbi", "dbi2", "phy", "addr_space";
		num-ib-windows = <6>;
		num-ob-windows = <2>;
		num-lanes = <2>;
		axis,syscon-pcie = <&syscon>;
		status = "disabled";
	};

	pinctrl: pinctrl@f801d000 {
		compatible = "axis,artpec6-pinctrl";
		reg = <0xf801d000 0x400>;

		pinctrl_uart0: uart0grp {
			function = "uart0";
			groups = "uart0grp2";
			bias-pull-up;
		};
		pinctrl_uart1: uart1grp {
			function = "uart1";
			groups = "uart1grp0";
			bias-pull-up;
		};
		pinctrl_uart2: uart2grp {
			function = "uart2";
			groups = "uart2grp1";
			bias-pull-up;
		};
		pinctrl_uart3: uart3grp {
			function = "uart3";
			groups = "uart3grp0";
			bias-pull-up;
		};
	};

	amba@0 {
		compatible = "simple-bus";
		#address-cells = <0x1>;
		#size-cells = <0x1>;
		ranges;
		dma-ranges;

		crypto@f4264000 {
			compatible = "axis,artpec6-crypto";
			reg = <0xf4264000 0x4000>;
			interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
		};

		dma0: dma@f8019000 {
			compatible = "renesas,nbpfaxi64dmac8b16";
			reg = <0xf8019000 0x400>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>, /* error */
				     <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "error",
					  "ch0", "ch1", "ch2", "ch3",
					  "ch4", "ch5", "ch6", "ch7",
					  "ch8", "ch9", "ch10", "ch12",
					  "ch12", "ch13", "ch14", "ch15";
			clocks = <&clkctrl ARTPEC6_CLK_DMA_ACLK>;
			#dma-cells = <2>;
			dma-channels = <8>;
			dma-requests = <8>;
		};
		dma1: dma@f8019400 {
			compatible = "renesas,nbpfaxi64dmac8b16";
			reg = <0xf8019400 0x400>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>, /* error */
				     <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 79 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "error",
					  "ch0", "ch1", "ch2", "ch3",
					  "ch4", "ch5", "ch6", "ch7",
					  "ch8", "ch9", "ch10", "ch12",
					  "ch12", "ch13", "ch14", "ch15";
			clocks = <&clkctrl ARTPEC6_CLK_DMA_ACLK>;
			#dma-cells = <2>;
			dma-channels = <8>;
			dma-requests = <8>;
		};

		ethernet: ethernet@f8010000 {
			clock-names = "stmmaceth", "ptp_ref";
			clocks = <&clkctrl ARTPEC6_CLK_ETH_ACLK>,
				<&clkctrl ARTPEC6_CLK_PTP_REF>;
			compatible = "snps,dwmac-4.10a", "snps,dwmac";
			interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq", "eth_lpi";
			reg = <0xf8010000 0x4000>;

			snps,axi-config = <&stmmac_axi_setup>;
			snps,mtl-rx-config = <&mtl_rx_setup>;
			snps,mtl-tx-config = <&mtl_tx_setup>;

			snps,txpbl = <8>;
			snps,rxpbl = <2>;
			snps,aal;
			snps,tso;

			status = "disabled";

			stmmac_axi_setup: stmmac-axi-config {
				snps,wr_osr_lmt = <1>;
				snps,rd_osr_lmt = <15>;
				/* If FB is disabled, the AXI master chooses
				 * a burst length of any value less than the
				 * maximum enabled burst length
				 * (all lesser burst length enables are redundant).
				 */
				snps,blen = <0 0 0 0 16 0 0>;
			};

			mtl_rx_setup: rx-queues-config {
				snps,rx-queues-to-use = <1>;
				queue0 {};
			};

			mtl_tx_setup: tx-queues-config {
				snps,tx-queues-to-use = <2>;
				queue0 {};
				queue1 {};
			};
		};

		uart0: serial@f8036000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0xf8036000 0x1000>;
			interrupts = <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkctrl ARTPEC6_CLK_UART_REFCLK>,
				<&clkctrl ARTPEC6_CLK_UART_PCLK>;
			clock-names = "uart_clk", "apb_pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart0>;
			dmas = <&dma0 4 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>,
			       <&dma0 5 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>;
			dma-names = "rx", "tx";
			status = "disabled";
		};
		uart1: serial@f8037000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0xf8037000 0x1000>;
			interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkctrl ARTPEC6_CLK_UART_REFCLK>,
				<&clkctrl ARTPEC6_CLK_UART_PCLK>;
			clock-names = "uart_clk", "apb_pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart1>;
			dmas = <&dma0 6 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>,
			       <&dma0 7 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>;
			dma-names = "rx", "tx";
			status = "disabled";
		};
		uart2: serial@f8038000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0xf8038000 0x1000>;
			interrupts = <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkctrl ARTPEC6_CLK_UART_REFCLK>,
				<&clkctrl ARTPEC6_CLK_UART_PCLK>;
			clock-names = "uart_clk", "apb_pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart2>;
			dmas = <&dma1 0 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>,
			       <&dma1 1 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>;
			dma-names = "rx", "tx";
			status = "disabled";
		};
		uart3: serial@f8039000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0xf8039000 0x1000>;
			interrupts = <GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkctrl ARTPEC6_CLK_UART_REFCLK>,
				<&clkctrl ARTPEC6_CLK_UART_PCLK>;
			clock-names = "uart_clk", "apb_pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart3>;
			dmas = <&dma1 2 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>,
			       <&dma1 3 (NBPF_SLAVE_RQ_HIGH | NBPF_SLAVE_RQ_LEVEL)>;
			dma-names = "rx", "tx";
			status = "disabled";
		};
	};
};
