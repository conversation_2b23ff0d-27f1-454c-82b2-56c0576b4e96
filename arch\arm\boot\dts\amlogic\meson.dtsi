// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright 2014 <PERSON> <<EMAIL>>
 */

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/sound/meson-aiu.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&saradc 8>;
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		cbus: bus@c1100000 {
			compatible = "simple-bus";
			reg = <0xc1100000 0x200000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0xc1100000 0x200000>;

			hhi: system-controller@4000 {
				compatible = "amlogic,meson-hhi-sysctrl",
					     "simple-mfd",
					     "syscon";
				reg = <0x4000 0x400>;
			};

			aiu: audio-controller@5400 {
				compatible = "amlogic,aiu";
				#sound-dai-cells = <2>;
				sound-name-prefix = "AIU";
				reg = <0x5400 0x2ac>;
				interrupts = <GIC_SPI 48 IRQ_TYPE_EDGE_RISING>,
					     <GIC_SPI 50 IRQ_TYPE_EDGE_RISING>;
				interrupt-names = "i2s", "spdif";
				status = "disabled";
			};

			assist: assist@7c00 {
				compatible = "amlogic,meson-mx-assist", "syscon";
				reg = <0x7c00 0x200>;
			};

			hwrng: rng@8100 {
				compatible = "amlogic,meson-rng";
				reg = <0x8100 0x8>;
			};

			uart_A: serial@84c0 {
				compatible = "amlogic,meson6-uart";
				reg = <0x84c0 0x18>;
				interrupts = <GIC_SPI 26 IRQ_TYPE_EDGE_RISING>;
				fifo-size = <128>;
				status = "disabled";
			};

			uart_B: serial@84dc {
				compatible = "amlogic,meson6-uart";
				reg = <0x84dc 0x18>;
				interrupts = <GIC_SPI 75 IRQ_TYPE_EDGE_RISING>;
				status = "disabled";
			};

			i2c_A: i2c@8500 {
				compatible = "amlogic,meson6-i2c";
				reg = <0x8500 0x20>;
				interrupts = <GIC_SPI 21 IRQ_TYPE_EDGE_RISING>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			pwm_ab: pwm@8550 {
				compatible = "amlogic,meson-pwm";
				reg = <0x8550 0x10>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm_cd: pwm@8650 {
				compatible = "amlogic,meson-pwm";
				reg = <0x8650 0x10>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			saradc: adc@8680 {
				compatible = "amlogic,meson-saradc";
				reg = <0x8680 0x34>;
				#io-channel-cells = <1>;
				interrupts = <GIC_SPI 73 IRQ_TYPE_EDGE_RISING>;
				status = "disabled";
			};

			uart_C: serial@8700 {
				compatible = "amlogic,meson6-uart";
				reg = <0x8700 0x18>;
				interrupts = <GIC_SPI 93 IRQ_TYPE_EDGE_RISING>;
				status = "disabled";
			};

			i2c_B: i2c@87c0 {
				compatible = "amlogic,meson6-i2c";
				reg = <0x87c0 0x20>;
				interrupts = <GIC_SPI 128 IRQ_TYPE_EDGE_RISING>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			usb0_phy: phy@8800 {
				compatible = "amlogic,meson-mx-usb2-phy";
				#phy-cells = <0>;
				reg = <0x8800 0x20>;
				status = "disabled";
			};

			usb1_phy: phy@8820 {
				compatible = "amlogic,meson-mx-usb2-phy";
				#phy-cells = <0>;
				reg = <0x8820 0x20>;
				status = "disabled";
			};

			sdio: mmc@8c20 {
				compatible = "amlogic,meson-mx-sdio";
				reg = <0x8c20 0x20>;
				interrupts = <GIC_SPI 28 IRQ_TYPE_EDGE_RISING>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			spifc: spi@8c80 {
				compatible = "amlogic,meson6-spifc";
				reg = <0x8c80 0x80>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			sdhc: mmc@8e00 {
				compatible = "amlogic,meson-mx-sdhc";
				reg = <0x8e00 0x42>;
				interrupts = <GIC_SPI 78 IRQ_TYPE_EDGE_RISING>;
				status = "disabled";
			};

			gpio_intc: interrupt-controller@9880 {
				compatible = "amlogic,meson-gpio-intc";
				reg = <0x9880 0x10>;
				interrupt-controller;
				#interrupt-cells = <2>;
				amlogic,channel-interrupts = <64 65 66 67 68 69 70 71>;
				status = "disabled";
			};

			wdt: watchdog@9900 {
				compatible = "amlogic,meson6-wdt";
				reg = <0x9900 0x8>;
				interrupts = <GIC_SPI 0 IRQ_TYPE_EDGE_RISING>;
			};

			timer_abcde: timer@9940 {
				compatible = "amlogic,meson6-timer";
				reg = <0x9940 0x18>;
				interrupts = <GIC_SPI 10 IRQ_TYPE_EDGE_RISING>,
					     <GIC_SPI 11 IRQ_TYPE_EDGE_RISING>,
					     <GIC_SPI 6 IRQ_TYPE_EDGE_RISING>,
					     <GIC_SPI 29 IRQ_TYPE_EDGE_RISING>;
			};
		};

		L2: cache-controller@c4200000 {
			compatible = "arm,pl310-cache";
			reg = <0xc4200000 0x1000>;
			cache-unified;
			cache-level = <2>;
		};

		periph: bus@c4300000 {
			compatible = "simple-bus";
			reg = <0xc4300000 0x10000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0xc4300000 0x10000>;

			gic: interrupt-controller@1000 {
				compatible = "arm,cortex-a9-gic";
				reg = <0x1000 0x1000>,
				      <0x100 0x100>;
				interrupt-controller;
				#interrupt-cells = <3>;
			};
		};

		aobus: bus@c8100000 {
			compatible = "simple-bus";
			reg = <0xc8100000 0x100000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0xc8100000 0x100000>;

			ao_arc_rproc: remoteproc@1c {
				compatible = "amlogic,meson-mx-ao-arc";
				reg = <0x1c 0x8>, <0x38 0x8>;
				reg-names = "remap", "cpu";
				status = "disabled";
			};

			ir_receiver: ir-receiver@480 {
				compatible = "amlogic,meson6-ir";
				reg = <0x480 0x20>;
				interrupts = <GIC_SPI 15 IRQ_TYPE_EDGE_RISING>;
				status = "disabled";
			};

			uart_AO: serial@4c0 {
				compatible = "amlogic,meson6-uart", "amlogic,meson-ao-uart";
				reg = <0x4c0 0x18>;
				interrupts = <GIC_SPI 90 IRQ_TYPE_EDGE_RISING>;
				status = "disabled";
			};

			i2c_AO: i2c@500 {
				compatible = "amlogic,meson6-i2c";
				reg = <0x500 0x20>;
				interrupts = <GIC_SPI 92 IRQ_TYPE_EDGE_RISING>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			rtc: rtc@740 {
				compatible = "amlogic,meson6-rtc";
				reg = <0x740 0x14>;
				interrupts = <GIC_SPI 72 IRQ_TYPE_EDGE_RISING>;
				#address-cells = <1>;
				#size-cells = <1>;
				status = "disabled";
			};
		};

		usb0: usb@c9040000 {
			compatible = "snps,dwc2";
			reg = <0xc9040000 0x40000>;
			interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
			phys = <&usb0_phy>;
			phy-names = "usb2-phy";
			g-rx-fifo-size = <512>;
			g-np-tx-fifo-size = <500>;
			g-tx-fifo-size = <256 192 128 128 128>;
			dr_mode = "host";
			status = "disabled";
		};

		usb1: usb@c90c0000 {
			compatible = "snps,dwc2";
			reg = <0xc90c0000 0x40000>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			phys = <&usb1_phy>;
			phy-names = "usb2-phy";
			dr_mode = "host";
			status = "disabled";
		};

		ethmac: ethernet@c9410000 {
			compatible = "amlogic,meson6-dwmac", "snps,dwmac";
			reg = <0xc9410000 0x10000
			       0xc1108108 0x4>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq";
			status = "disabled";
		};

		ahb_sram: sram@d9000000 {
			compatible = "mmio-sram";
			reg = <0xd9000000 0x20000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0xd9000000 0x20000>;
		};

		bootrom: bootrom@d9040000 {
			compatible = "amlogic,meson-mx-bootrom", "syscon";
			reg = <0xd9040000 0x10000>;
		};

		secbus: bus@da000000 {
			compatible = "simple-bus";
			reg = <0xda000000 0x6000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0xda000000 0x6000>;

			efuse: nvmem@0 {
				compatible = "amlogic,meson6-efuse";
				reg = <0x0 0x2000>;
				#address-cells = <1>;
				#size-cells = <1>;
			};
		};
	};

	thermal_sensor: thermal-sensor {
		compatible = "generic-adc-thermal";
		#thermal-sensor-cells = <0>;
		io-channels = <&saradc 8>;
		io-channel-names = "sensor-channel";
	};

	xtal: xtal-clk {
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
		clock-output-names = "xtal";
		#clock-cells = <0>;
	};
}; /* end of / */
