// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Copyright 2017 Luxul Inc.
 */

/dts-v1/;

#include "bcm53573.dtsi"

/ {
	compatible = "luxul,xap-1440-v1", "brcm,bcm47189", "brcm,bcm53573";
	model = "Luxul XAP-1440 V1";

	chosen {
		bootargs = "earlycon";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x08000000>;
	};

	leds {
		compatible = "gpio-leds";

		led-wlan {
			label = "bcm53xx:blue:wlan";
			gpios = <&chipcommon 10 GPIO_ACTIVE_LOW>;
		};

		led-system {
			label = "bcm53xx:green:system";
			gpios = <&chipcommon 11 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "timer";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-restart {
			label = "Reset";
			linux,code = <KEY_RESTART>;
			gpios = <&chipcommon 7 GPIO_ACTIVE_LOW>;
		};
	};
};

&gmac0 {
	phy-mode = "rgmii";
	phy-handle = <&bcm54210e>;

	/delete-node/ fixed-link;

	mdio {
		/delete-node/ switch@1e;

		bcm54210e: ethernet-phy@0 {
			reg = <0>;
		};
	};
};

&gmac1 {
	status = "disabled";
};
