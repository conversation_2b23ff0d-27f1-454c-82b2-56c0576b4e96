// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Copyright 2017 Luxul Inc.
 */

/dts-v1/;

#include "bcm53573.dtsi"

/ {
	compatible = "luxul,xap-810-v1", "brcm,bcm47189", "brcm,bcm53573";
	model = "Luxul XAP-810 V1";

	chosen {
		bootargs = "earlycon";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x08000000>;
	};

	leds-0 {
		compatible = "gpio-leds";

		led-5ghz {
			label = "bcm53xx:blue:5ghz";
			gpios = <&chipcommon 11 GPIO_ACTIVE_HIGH>;
		};

		led-system {
			label = "bcm53xx:green:system";
			gpios = <&chipcommon 15 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "timer";
		};
	};

	leds-1 {
		compatible = "gpio-leds";

		led-2ghz {
			label = "bcm53xx:blue:2ghz";
			gpios = <&pcie0_chipcommon 3 GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-restart {
			label = "Reset";
			linux,code = <KEY_RESTART>;
			gpios = <&chipcommon 7 GPIO_ACTIVE_LOW>;
		};
	};
};

&pcie0 {
	ranges = <0x00000000 0 0 0 0 0x00100000>;
	#address-cells = <3>;
	#size-cells = <2>;

	bridge@0,0,0 {
		reg = <0x0000 0 0 0 0>;
		ranges = <0x00000000 0 0 0 0 0 0 0x00100000>;
		#address-cells = <3>;
		#size-cells = <2>;

		wifi@0,1,0 {
			reg = <0x0000 0 0 0 0>;
			ranges = <0x00000000 0 0 0 0x00100000>;
			#address-cells = <1>;
			#size-cells = <1>;

			pcie0_chipcommon: chipcommon@0 {
				reg = <0 0x1000>;

				gpio-controller;
				#gpio-cells = <2>;
			};
		};
	};
};

&gmac0 {
	phy-mode = "rgmii";
	phy-handle = <&bcm54210e>;

	/delete-node/ fixed-link;

	mdio {
		/delete-node/ switch@1e;

		bcm54210e: ethernet-phy@0 {
			reg = <0>;
		};
	};
};

&gmac1 {
	status = "disabled";
};
