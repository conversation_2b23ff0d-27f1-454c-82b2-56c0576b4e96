// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2019 IBM Corp.
/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "Rainier 2U";
	compatible = "ibm,rainier-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
		i2c16 = &i2c2mux0;
		i2c17 = &i2c2mux1;
		i2c18 = &i2c2mux2;
		i2c19 = &i2c2mux3;
		i2c20 = &i2c4mux0chn0;
		i2c21 = &i2c4mux0chn1;
		i2c22 = &i2c4mux0chn2;
		i2c23 = &i2c5mux0chn0;
		i2c24 = &i2c5mux0chn1;
		i2c25 = &i2c6mux0chn0;
		i2c26 = &i2c6mux0chn1;
		i2c27 = &i2c6mux0chn2;
		i2c28 = &i2c6mux0chn3;
		i2c29 = &i2c11mux0chn0;
		i2c30 = &i2c11mux0chn1;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200n8 earlycon";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		ramoops@b3e00000 {
			compatible = "ramoops";
			reg = <0xb3e00000 0x200000>; /* 16 * (4 * 0x8000) */
			record-size = <0x8000>;
			console-size = <0x8000>;
			ftrace-size = <0x8000>;
			pmsg-size = <0x8000>;
			max-reason = <3>; /* KMSG_DUMP_EMERG */
		};

		/* LPC FW cycle bridge region requires natural alignment */
		flash_memory: region@b4000000 {
			no-map;
			reg = <0xb4000000 0x04000000>; /* 64M */
		};

		/* VGA region is dictated by hardware strapping */
		vga_memory: region@bf000000 {
			no-map;
			compatible = "shared-dma-pool";
			reg = <0xbf000000 0x01000000>;  /* 16M */
		};
	};

	i2c2mux: i2cmux {
		compatible = "i2c-mux-gpio";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";

		i2c-parent = <&i2c2>;
		mux-gpios = <&gpio0 ASPEED_GPIO(G, 4) GPIO_ACTIVE_HIGH>,
			    <&gpio0 ASPEED_GPIO(G, 5) GPIO_ACTIVE_HIGH>;
		idle-state = <0>;

		i2c2mux0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c2mux1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c2mux2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c2mux3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	leds {
		compatible = "gpio-leds";

		/* BMC Card fault LED at the back */
		led-bmc-ingraham0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 1) GPIO_ACTIVE_LOW>;
		};

		/* Enclosure ID LED at the back */
		led-rear-enc-id0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 2) GPIO_ACTIVE_LOW>;
		};

		/* Enclosure fault LED at the back */
		led-rear-enc-fault0 {
			gpios = <&gpio0 ASPEED_GPIO(H, 3) GPIO_ACTIVE_LOW>;
		};

		/* PCIE slot power LED */
		led-pcieslot-power {
			gpios = <&gpio0 ASPEED_GPIO(P, 4) GPIO_ACTIVE_LOW>;
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-fan0-presence {
			label = "fan0-presence";
			gpios = <&pca0 6 GPIO_ACTIVE_LOW>;
			linux,code = <6>;
		};

		event-fan1-presence {
			label = "fan1-presence";
			gpios = <&pca0 7 GPIO_ACTIVE_LOW>;
			linux,code = <7>;
		};

		event-fan2-presence {
			label = "fan2-presence";
			gpios = <&pca0 8 GPIO_ACTIVE_LOW>;
			linux,code = <8>;
		};

		event-fan3-presence {
			label = "fan3-presence";
			gpios = <&pca0 9 GPIO_ACTIVE_LOW>;
			linux,code = <9>;
		};

		event-fan4-presence {
			label = "fan4-presence";
			gpios = <&pca0 10 GPIO_ACTIVE_LOW>;
			linux,code = <10>;
		};

		event-fan5-presence {
			label = "fan5-presence";
			gpios = <&pca0 11 GPIO_ACTIVE_LOW>;
			linux,code = <11>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc1 7>;
	};
};

&adc1 {
	status = "okay";
	aspeed,int-vref-microvolt = <2500000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc9_default
		&pinctrl_adc10_default &pinctrl_adc11_default
		&pinctrl_adc12_default &pinctrl_adc13_default
		&pinctrl_adc14_default &pinctrl_adc15_default>;
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"","","","","","","checkstop","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","rtc-battery-voltage-read-enable","reset-cause-pinhole","","","factory-reset-toggle","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","led-bmc-ingraham0","led-rear-enc-id0","led-rear-enc-fault0","","","","",
	/*I0-I7*/	"","","","","","","bmc-secure-boot","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","usb-power","","","","",
	/*P0-P7*/	"","","","","led-pcieslot-power","","","",
	/*Q0-Q7*/	"cfam-reset","","regulator-standby-faulted","","","","","",
	/*R0-R7*/	"bmc-tpm-reset","power-chassis-control","power-chassis-good","","","","","",
	/*S0-S7*/	"presence-ps0","presence-ps1","presence-ps2","presence-ps3",
	"power-ffs-sync-history","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","";

	i2c3_mux_oe_n {
		gpio-hog;
		gpios = <ASPEED_GPIO(G, 6) GPIO_ACTIVE_LOW>;
		output-high;
		line-name = "I2C3_MUX_OE_N";
	};

	usb_power {
		gpio-hog;
		gpios = <ASPEED_GPIO(O, 3) GPIO_ACTIVE_LOW>;
		output-high;
	};
};

&emmc_controller {
	status = "okay";
};

&pinctrl_emmc_default {
	bias-disable;
};

&emmc {
	status = "okay";
	clk-phase-mmc-hs200 = <180>, <180>;
};

&ibt {
	status = "okay";
};

&i2c0 {
	status = "okay";

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	tca_pres1: tca9554@20{
		compatible = "ti,tca9554";
		reg = <0x20>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names = "",
			"RUSSEL_FW_I2C_ENABLE_N",
			"RUSSEL_OPPANEL_PRESENCE_N",
			"BLYTH_OPPANEL_PRESENCE_N",
			"CPU_TPM_CARD_PRESENT_N",
			"DASD_BP2_PRESENT_N",
			"DASD_BP1_PRESENT_N",
			"DASD_BP0_PRESENT_N";
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";

	power-supply@68 {
		compatible = "ibm,cffps";
		reg = <0x68>;
	};

	power-supply@69 {
		compatible = "ibm,cffps";
		reg = <0x69>;
	};

	pca_pres1: pca9552@61 {
		compatible = "nxp,pca9552";
		reg = <0x61>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"SLOT0_PRSNT_EN_RSVD", "SLOT1_PRSNT_EN_RSVD",
			"SLOT2_PRSNT_EN_RSVD", "SLOT3_PRSNT_EN_RSVD",
			"SLOT4_PRSNT_EN_RSVD", "SLOT0_EXPANDER_PRSNT_N",
			"SLOT1_EXPANDER_PRSNT_N", "SLOT2_EXPANDER_PRSNT_N",
			"SLOT3_EXPANDER_PRSNT_N", "SLOT4_EXPANDER_PRSNT_N",
			"", "", "", "", "", "";
	};
};

&i2c4 {
	status = "okay";

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	tmp275@49 {
		compatible = "ti,tmp275";
		reg = <0x49>;
	};

	tmp275@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c4mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			pca9551@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard0-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard0-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c4mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};
		};

		i2c4mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;

			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};
		};
	};
};

&i2c5 {
	status = "okay";

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	tmp275@49 {
		compatible = "ti,tmp275";
		reg = <0x49>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c5mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			pca9551@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard3-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard3-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c5mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};

			pca9551@61 {
				compatible = "nxp,pca9551";
				reg = <0x61>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard4-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard4-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};
	};
};

&i2c6 {
	status = "okay";

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	tmp275@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};

	tmp275@4b {
		compatible = "ti,tmp275";
		reg = <0x4b>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c6mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			eeprom@53 {
				compatible = "atmel,24c64";
				reg = <0x53>;
			};
		};

		i2c6mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			eeprom@52 {
				compatible = "atmel,24c64";
				reg = <0x52>;
			};
		};

		i2c6mux0chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};

		i2c6mux0chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};
		};
	};
};

&i2c7 {
	multi-master;
	status = "okay";

	pca9552@30 {
		compatible = "ibm,pca9552";
		reg = <0x30>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "pcieslot0";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "pcieslot1";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "pcieslot2";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "pcieslot3";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "pcieslot4";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "cpu1";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "cpu-vrm1";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "lcd-russel";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pca9552@31 {
		compatible = "ibm,pca9552";
		reg = <0x31>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "ddimm0";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "ddimm1";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "ddimm2";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "ddimm3";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "ddimm4";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "ddimm5";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "ddimm6";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "ddimm7";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "ddimm8";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "ddimm9";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "ddimm10";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "ddimm11";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "ddimm12";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "ddimm13";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "ddimm14";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "ddimm15";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pca9552@32 {
		compatible = "ibm,pca9552";
		reg = <0x32>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "ddimm16";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "ddimm17";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "ddimm18";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "ddimm19";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "ddimm20";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "ddimm21";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "ddimm22";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "ddimm23";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "ddimm24";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "ddimm25";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "ddimm26";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "ddimm27";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "ddimm28";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "ddimm29";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "ddimm30";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "ddimm31";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pca9552@33 {
		compatible = "ibm,pca9552";
		reg = <0x33>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "planar";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "cpu0";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "dasd-pyramid0";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "dasd-pyramid1";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "dasd-pyramid2";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "cpu0-vrm0";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "rtc-battery";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@8 {
			label = "base-blyth";
			reg = <8>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@9 {
			label = "pcieslot6";
			reg = <9>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@10 {
			label = "pcieslot7";
			reg = <10>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@11 {
			label = "pcieslot8";
			reg = <11>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@12 {
			label = "pcieslot9";
			reg = <12>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@13 {
			label = "pcieslot10";
			reg = <13>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@14 {
			label = "pcieslot11";
			reg = <14>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@15 {
			label = "tpm-wilson";
			reg = <15>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	si7021-a20@40 {
		compatible = "silabs,si7020";
		reg = <0x40>;
	};

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	max: max31785@52 {
		compatible = "maxim,max31785a";
		reg = <0x52>;
		#address-cells = <1>;
		#size-cells = <0>;

		fan0: fan@0 {
			compatible = "pmbus-fan";
			reg = <0>;
			tach-pulses = <2>;
		};

		fan1: fan@1 {
			compatible = "pmbus-fan";
			reg = <1>;
			tach-pulses = <2>;
		};

		fan2: fan@2 {
			compatible = "pmbus-fan";
			reg = <2>;
			tach-pulses = <2>;
		};

		fan3: fan@3 {
			compatible = "pmbus-fan";
			reg = <3>;
			tach-pulses = <2>;
		};

		fan4: fan@4 {
			compatible = "pmbus-fan";
			reg = <4>;
			tach-pulses = <2>;
		};

		fan5: fan@5 {
			compatible = "pmbus-fan";
			reg = <5>;
			tach-pulses = <2>;
		};
	};

	pca9551@60 {
		compatible = "nxp,pca9551";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "front-sys-id0";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "front-check-log0";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "front-enc-fault1";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "front-sys-pwron0";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	pca0: pca9552@61 {
		compatible = "nxp,pca9552";
		reg = <0x61>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "fan0";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "fan1";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "fan2";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "fan3";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "fan4";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "fan5";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};

	ibm-panel@62 {
		compatible = "ibm,op-panel";
		reg = <(0x62 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	dps: dps310@76 {
		compatible = "infineon,dps310";
		reg = <0x76>;
		#io-channel-cells = <0>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};
};

&i2c8 {
	status = "okay";

	ucd90320@11 {
		compatible = "ti,ucd90320";
		reg = <0x11>;
	};

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	tmp275@4a {
		compatible = "ti,tmp275";
		reg = <0x4a>;
	};

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
	};

	pca_pres3: pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"", "", "", "", "", "", "P10_DCM0_PRES", "P10_DCM1_PRES",
			"", "", "", "", "PRESENT_VRM_DCM0_N", "PRESENT_VRM_DCM1_N",
			"power-config-full-load", "";
	};

	pca_pres2: pca9552@61 {
		compatible = "nxp,pca9552";
		reg = <0x61>;
		#address-cells = <1>;
		#size-cells = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		gpio-line-names =
			"SLOT6_PRSNT_EN_RSVD", "SLOT7_PRSNT_EN_RSVD",
			"SLOT8_PRSNT_EN_RSVD", "SLOT9_PRSNT_EN_RSVD",
			"SLOT10_PRSNT_EN_RSVD", "SLOT11_PRSNT_EN_RSVD",
			"SLOT6_EXPANDER_PRSNT_N", "SLOT7_EXPANDER_PRSNT_N",
			"SLOT8_EXPANDER_PRSNT_N", "SLOT9_EXPANDER_PRSNT_N",
			"SLOT10_EXPANDER_PRSNT_N", "SLOT11_EXPANDER_PRSNT_N",
			"", "", "", "";
	};

};

&i2c9 {
	status = "okay";

	tmp423a@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	tmp423b@4d {
		compatible = "ti,tmp423";
		reg = <0x4d>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};
};

&i2c10 {
	status = "okay";

	tmp423a@4c {
		compatible = "ti,tmp423";
		reg = <0x4c>;
	};

	tmp423b@4d {
		compatible = "ti,tmp423";
		reg = <0x4d>;
	};

	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
	};
};

&i2c11 {
	status = "okay";

	tmp275@48 {
		compatible = "ti,tmp275";
		reg = <0x48>;
	};

	tmp275@49 {
		compatible = "ti,tmp275";
		reg = <0x49>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
		i2c-mux-idle-disconnect;

		i2c11mux0chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};

			pca9551@60 {
				compatible = "nxp,pca9551";
				reg = <0x60>;
				#address-cells = <1>;
				#size-cells = <0>;

				gpio-controller;
				#gpio-cells = <2>;

				led@0 {
					label = "cablecard10-cxp-top";
					reg = <0>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};

				led@1 {
					label = "cablecard10-cxp-bot";
					reg = <1>;
					retain-state-shutdown;
					default-state = "keep";
					type = <PCA955X_TYPE_LED>;
				};
			};
		};

		i2c11mux0chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			eeprom@51 {
				compatible = "atmel,24c64";
				reg = <0x51>;
			};
		};
	};
};

&i2c12 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};
};

&i2c13 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "nvme0";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "nvme1";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "nvme2";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "nvme3";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "nvme4";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "nvme5";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "nvme6";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "nvme7";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&i2c14 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "nvme8";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "nvme9";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "nvme10";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "nvme11";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "nvme12";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "nvme13";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "nvme14";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "nvme15";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&i2c15 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	pca9552@60 {
		compatible = "nxp,pca9552";
		reg = <0x60>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		led@0 {
			label = "nvme16";
			reg = <0>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@1 {
			label = "nvme17";
			reg = <1>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@2 {
			label = "nvme18";
			reg = <2>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@3 {
			label = "nvme19";
			reg = <3>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@4 {
			label = "nvme20";
			reg = <4>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@5 {
			label = "nvme21";
			reg = <5>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@6 {
			label = "nvme22";
			reg = <6>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};

		led@7 {
			label = "nvme23";
			reg = <7>;
			retain-state-shutdown;
			default-state = "keep";
			type = <PCA955X_TYPE_LED>;
		};
	};
};

&uart2 {
	status = "okay";
};

&vuart1 {
	status = "okay";
};

&vuart2 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC3CLK>,
		 <&syscon ASPEED_CLK_MAC3RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC4CLK>,
		 <&syscon ASPEED_CLK_MAC4RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&wdt1 {
	aspeed,reset-type = "none";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
};

&wdt2 {
	status = "okay";
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8 0xcac>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
	aspeed,lpc-interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
};

#include "ibm-power10-quad.dtsi"
