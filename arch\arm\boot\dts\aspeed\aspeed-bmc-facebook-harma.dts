// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2023 Facebook Inc.

/dts-v1/;
#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>

/ {
	model = "Facebook Harma";
	compatible = "facebook,harma-bmc", "aspeed,ast2600";

	aliases {
		serial0 = &uart1;
		serial1 = &uart2;
		serial2 = &uart4;
		serial4 = &uart5;

		i2c20 = &imux20;
		i2c21 = &imux21;
		i2c22 = &imux22;
		i2c23 = &imux23;
		i2c28 = &imux28;
		i2c29 = &imux29;
		i2c30 = &imux30;
		i2c31 = &imux31;

		spi1 = &spi_gpio;
	};

	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc0 0>, <&adc0 1>, <&adc0 2>, <&adc0 3>,
			      <&adc0 4>, <&adc0 5>, <&adc0 6>, <&adc0 7>,
			      <&adc1 2>;
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "bmc_heartbeat_amber";
			gpios = <&gpio0 ASPEED_GPIO(P, 7) GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};

		led-1 {
			label = "fp_id_amber";
			default-state = "off";
			gpios = <&gpio0 13 GPIO_ACTIVE_HIGH>;
		};

		led-2 {
			label = "power_blue";
			default-state = "off";
			gpios = <&gpio0 124 GPIO_ACTIVE_HIGH>;
		};
	};

	spi_gpio: spi {
		status = "okay";
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		sck-gpios = <&gpio0 ASPEED_GPIO(Z, 3) GPIO_ACTIVE_HIGH>;
		mosi-gpios = <&gpio0 ASPEED_GPIO(Z, 4) GPIO_ACTIVE_HIGH>;
		miso-gpios = <&gpio0 ASPEED_GPIO(Z, 5) GPIO_ACTIVE_HIGH>;
		num-chipselects = <1>;
		cs-gpios = <&gpio0 ASPEED_GPIO(Z, 0) GPIO_ACTIVE_LOW>;

		tpm@0 {
			compatible = "infineon,slb9670", "tcg,tpm_tis-spi";
			spi-max-frequency = <********>;
			reg = <0>;
		};
	};
};

// HOST BIOS Debug
&uart1 {
	status = "okay";
};

// SOL Host Console
&uart2 {
	status = "okay";
	pinctrl-0 = <>;
};

// SOL BMC Console
&uart4 {
	status = "okay";
	pinctrl-0 = <>;
};

// BMC Debug Console
&uart5 {
	status = "okay";
};

// MTIA
&uart6 {
	status = "okay";
};

&uart_routing {
	status = "okay";
};

&wdt1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
	aspeed,reset-type = "soc";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;
	aspeed,ext-pulse-duration = <256>;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	use-ncsi;
};

&fmc {
	status = "okay";

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <********>;
#include "openbmc-flash-layout-128.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
		spi-max-frequency = <********>;
	};
};

// BIOS Flash
&spi2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi2_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-max-frequency = <********>;
		spi-tx-bus-width = <2>;
		spi-rx-bus-width = <2>;
	};
};

&kcs2 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca8>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&i2c0 {
	status = "okay";

	pwm@5e{
		compatible = "max31790";
		reg = <0x5e>;
		#address-cells = <1>;
		#size-cells = <0>;
	};

	power-sensor@40 {
		compatible = "ti,ina238";
		reg = <0x40>;
		shunt-resistor = <1000>;
	};

	power-sensor@41 {
		compatible = "ti,ina238";
		reg = <0x41>;
		shunt-resistor = <1000>;
	};

	power-sensor@44 {
		compatible = "ti,ina238";
		reg = <0x44>;
		shunt-resistor = <1000>;
	};

	power-sensor@45 {
		compatible = "ti,ina238";
		reg = <0x45>;
		shunt-resistor = <1000>;
	};

	temperature-sensor@4b {
		compatible = "ti,tmp75";
		reg = <0x4b>;
	};
};

&i2c1 {
	status = "okay";

	temperature-sensor@4b {
		compatible = "ti,tmp75";
		reg = <0x4b>;
	};

	// MB NIC FRU
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};
};

&i2c2 {
	status = "okay";

	pwm@5e{
		compatible = "max31790";
		reg = <0x5e>;
		#address-cells = <1>;
		#size-cells = <0>;
	};

	power-sensor@40 {
		compatible = "ti,ina238";
		reg = <0x40>;
		shunt-resistor = <1000>;
	};

	power-sensor@41 {
		compatible = "ti,ina238";
		reg = <0x41>;
		shunt-resistor = <1000>;
	};

	power-sensor@44 {
		compatible = "ti,ina238";
		reg = <0x44>;
		shunt-resistor = <1000>;
	};

	power-sensor@45 {
		compatible = "ti,ina238";
		reg = <0x45>;
		shunt-resistor = <1000>;
	};

	temperature-sensor@4b {
		compatible = "ti,tmp75";
		reg = <0x4b>;
	};
};

&i2c3 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9543";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;

		imux20: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			//Retimer Flash
			eeprom@50 {
				compatible = "atmel,24c2048";
				reg = <0x50>;
				pagesize = <128>;
			};
		};
		imux21: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			retimer@24 {
				compatible = "asteralabs,pt5161l";
				reg = <0x24>;
			};
		};
	};
};

&i2c4 {
	status = "okay";
	// PDB FRU
	eeprom@52 {
		compatible = "atmel,24c64";
		reg = <0x52>;
	};

	power-monitor@69 {
		compatible = "pmbus";
		reg = <0x69>;
	};

	temperature-sensor@49 {
		compatible = "ti,tmp75";
		reg = <0x49>;
	};

	power-monitor@44 {
		compatible = "lltc,ltc4287";
		reg = <0x44>;
		shunt-resistor-micro-ohms = <250>;
	};

	power-monitor@40 {
		compatible = "infineon,xdp710";
		reg = <0x40>;
	};
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9543";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;

		imux22: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};
		imux23: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
	};
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";

	mctp-controller;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@30 {
		compatible = "nxp,pca9555";
		reg = <0x30>;
		gpio-controller;
		#gpio-cells = <2>;
	};
	gpio@31 {
		compatible = "nxp,pca9555";
		reg = <0x31>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// PTTV FRU
	eeprom@52 {
		compatible = "atmel,24c64";
		reg = <0x52>;
	};
};

&i2c11 {
	status = "okay";

	gpio@30 {
		compatible = "nxp,pca9555";
		reg = <0x30>;
		gpio-controller;
		#gpio-cells = <2>;
	};
	gpio@31 {
		compatible = "nxp,pca9555";
		reg = <0x31>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	// Aegis FRU
	eeprom@52 {
		compatible = "atmel,24c64";
		reg = <0x52>;
	};
};

&i2c12 {
	status = "okay";
	retimer@24 {
		compatible = "asteralabs,pt5161l";
		reg = <0x24>;
	};
};

&i2c13 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9545";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;

		imux28: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			power-monitor@61 {
				compatible = "isil,isl69260";
				reg = <0x61>;
			};
			power-monitor@62 {
				compatible = "isil,isl69260";
				reg = <0x62>;
			};
			power-monitor@63 {
				compatible = "isil,isl69260";
				reg = <0x63>;
			};
			power-monitor@64 {
				compatible = "infineon,xdpe152c4";
				reg = <0x64>;
			};
			power-monitor@66 {
				compatible = "infineon,xdpe152c4";
				reg = <0x66>;
			};
			power-monitor@68 {
				compatible = "infineon,xdpe152c4";
				reg = <0x68>;
			};
		};
		imux29: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			//MB FRU
			eeprom@54 {
				compatible = "atmel,24c64";
				reg = <0x54>;
			};
		};
		imux30: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};
		imux31: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			rtc@51 {
				compatible = "nxp,pcf8563";
				reg = <0x51>;
			};
		};
	};
};

// To Debug card
&i2c14 {
	status = "okay";
	multi-master;

	ipmb@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c15 {
	status = "okay";

	// SCM FRU
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
	};

	// BSM FRU
	eeprom@56 {
		compatible = "atmel,24c64";
		reg = <0x56>;
	};
};

&adc0 {
	aspeed,int-vref-microvolt = <2500000>;
	status = "okay";
	pinctrl-0 = <&pinctrl_adc0_default &pinctrl_adc1_default
		&pinctrl_adc2_default &pinctrl_adc3_default
		&pinctrl_adc4_default &pinctrl_adc5_default
		&pinctrl_adc6_default &pinctrl_adc7_default>;
};

&adc1 {
	aspeed,int-vref-microvolt = <2500000>;
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc10_default>;
};

&ehci0 {
	status = "okay";
};

&gpio0 {
	pinctrl-names = "default";
	gpio-line-names =
	/*A0-A7*/	"","","","","","","","",
	/*B0-B7*/	"","","","",
			"bmc-spi-mux-select-0","led-identify","","",
	/*C0-C7*/	"reset-cause-platrst","","","","",
			"power-hsc-good","power-chassis-good","",
	/*D0-D7*/	"","","sol-uart-select","","","","","",
	/*E0-E7*/	"","","","","","","","",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","","","","","",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","",
			"leakage-detect-alert","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"led-postcode-0","led-postcode-1",
			"led-postcode-2","led-postcode-3",
			"led-postcode-4","led-postcode-5",
			"led-postcode-6","led-postcode-7",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"power-button","power-host-control",
			"reset-button","","led-power","","","",
	/*Q0-Q7*/
			"","","","",
			"","power-chassis-control","","uart-switch-button",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","","","","","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","led-identify-gate","",
	/*V0-V7*/	"","","","",
			"rtc-battery-voltage-read-enable","",
			"","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","presence-post-card","";
};

&gpio1 {
	gpio-line-names =
	/*18A0-18A7*/ "ac-power-button","","","","","","","",
	/*18B0-18B7*/ "","","","","","","","",
	/*18C0-18C7*/ "","","","","","","","",
	/*18D0-18D7*/ "","","","","","","","",
	/*18E0-18E3*/ "","","","","","","","";
};

&sgpiom0 {
	status = "okay";
	ngpios = <128>;
	bus-frequency = <2000000>;
	gpio-line-names =
	/*in - out - in - out */
	/*A0-A3 line 0-7*/
	"presence-scm-cable","power-config-disable-e1s-0",
	"","",
	"","power-config-disable-e1s-1",
	"","",
	/*A4-A7 line 8-15*/
	"","power-config-asic-module-enable",
	"","power-config-asic-power-good",
	"","power-config-pdb-power-good",
	"presence-cpu","smi-control-n",
	/*B0-B3 line 16-23*/
	"","nmi-control-n",
	"","nmi-control-sync-flood-n",
	"","",
	"","",
	/*B4-B7 line 24-31*/
	"","FM_CPU_SP5R1",
	"reset-cause-rsmrst","FM_CPU_SP5R2",
	"","FM_CPU_SP5R3",
	"","FM_CPU_SP5R4",
	/*C0-C3 line 32-39*/
	"","FM_CPU0_SA0",
	"","FM_CPU0_SA1",
	"","rt-cpu0-p0-enable",
	"","rt-cpu0-p1-enable",
	/*C4-C7 line 40-47*/
	"","smb-rt-rom-p0-select",
	"","smb-rt-rom-p1-select",
	"","i3c-cpu-mux0-oe-n",
	"","i3c-cpu-mux0-select",
	/*D0-D3 line 48-55*/
	"","i3c-cpu-mux1-oe-n",
	"","i3c-cpu-mux1-select",
	"","reset-control-bmc",
	"","reset-control-cpu0-p0-mux",
	/*D4-D7 line 56-63*/
	"","reset-control-cpu0-p1-mux",
	"","reset-control-e1s-mux",
	"power-host-good","reset-control-mb-mux",
	"host0-ready","reset-control-smb-e1s-0",
	/*E0-E3 line 64-71*/
	"","reset-control-smb-e1s-1",
	"post-end-n","reset-control-srst",
	"presence-e1s-0","reset-control-usb-hub",
	"","reset-control",
	/*E4-E7 line 72-79*/
	"presence-e1s-1","reset-control-cpu-kbrst",
	"","reset-control-platrst",
	"","bmc-jtag-mux-select-0",
	"","bmc-jtag-mux-select-1",
	/*F0-F3 line 80-87*/
	"","bmc-jtag-select",
	"","bmc-ready-n",
	"","bmc-ready-sgpio",
	"","rt-cpu0-p0-force-enable",
	/*F4-F7 line 88-95*/
	"presence-asic-modules-0","rt-cpu0-p1-force-enable",
	"presence-asic-modules-1","bios-debug-msg-disable",
	"","uart-control-buffer-select",
	"presence-cmm","ac-control-n",
	/*G0-G3 line 96-103*/
	"FM_CPU_CORETYPE2","",
	"FM_CPU_CORETYPE1","",
	"FM_CPU_CORETYPE0","",
	"FM_BOARD_REV_ID5","",
	/*G4-G7 line 104-111*/
	"FM_BOARD_REV_ID4","",
	"FM_BOARD_REV_ID3","",
	"FM_BOARD_REV_ID2","",
	"FM_BOARD_REV_ID1","",
	/*H0-H3 line 112-119*/
	"FM_BOARD_REV_ID0","reset-control-cmos-clear",
	"","","","","","",
	/*H4-H7 line 120-127*/
	"","",
	"reset-control-pcie-expansion-3","",
	"reset-control-pcie-expansion-2","",
	"reset-control-pcie-expansion-1","",
	/*I0-I3 line 128-135*/
	"reset-control-pcie-expansion-0","",
	"FM_EXP_SLOT_ID1","",
	"FM_EXP_SLOT_ID0","",
	"","",
	/*I4-I7 line 136-143*/
	"","","","","","","","",
	/*J0-J3 line 144-151*/
	"","","power-card-enable","","","","","",
	/*J4-J7 line 152-159*/
	"SLOT_ID_BCB_0","",
	"SLOT_ID_BCB_1","",
	"SLOT_ID_BCB_2","",
	"SLOT_ID_BCB_3","",
	/*K0-K3 line 160-167*/
	"","","","","","","P0_I3C_APML_ALERT_L","",
	/*K4-K7 line 168-175*/
	"","","","","","","irq-uv-detect-alert","",
	/*L0-L3 line 176-183*/
	"irq-hsc-alert","",
	"cpu0-prochot-alert","",
	"cpu0-thermtrip-alert","",
	"reset-cause-pcie","",
	/*L4-L7 line 184-191*/
	"pvdd11-ocp-alert","",
	"power-fault-n","",
	"asic0-card-type-detection0-n","",
	"asic0-card-type-detection1-n","",
	/*M0-M3 line 192-199*/
	"asic0-card-type-detection2-n","",
	"uart-switch-lsb","",
	"uart-switch-msb","",
	"","",
	/*M4-M7 line 200-207*/
	"","","","","","","","",
	/*N0-N3 line 208-215*/
	"","","","","","","","",
	/*N4-N7 line 216-223*/
	"","","","","","","","",
	/*O0-O3 line 224-231*/
	"","","","","","","","",
	/*O4-O7 line 232-239*/
	"","","","","","","","",
	/*P0-P3 line 240-247*/
	"","","","","","","","",
	/*P4-P7 line 248-255*/
	"","","","","","","","";
};
