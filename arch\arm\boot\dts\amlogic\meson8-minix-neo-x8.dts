// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright 2014 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include "meson8.dtsi"

/ {
	model = "MINIX NEO-X8";
	compatible = "minix,neo-x8", "amlogic,meson8";

	aliases {
		serial0 = &uart_AO;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x80000000>;
	};

	gpio-leds {
		compatible = "gpio-leds";

		led-blue {
			label = "x8:blue:power";
			gpios = <&gpio_ao GPIO_TEST_N GPIO_ACTIVE_HIGH>;
		};
	};
};

&uart_AO {
	status = "okay";
	pinctrl-0 = <&uart_ao_a_pins>;
	pinctrl-names = "default";
};

&i2c_AO {
	status = "okay";
	pinctrl-0 = <&i2c_ao_pins>;
	pinctrl-names = "default";

	pmic@32 {
		compatible = "ricoh,rn5t618";
		reg = <0x32>;
		system-power-controller;

		regulators {
		};
	};

	rtc@51 {
		compatible = "nxp,pcf8563";
		reg = <0x51>;
	};
};

&spifc {
	status = "okay";
	pinctrl-0 = <&spi_nor_pins>;
	pinctrl-names = "default";

	flash@0 {
		compatible = "mxicy,mx25l1606e";
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0>;
		spi-max-frequency = <30000000>;

		partition@0 {
			label = "boot";
			reg = <0x0 0x100000>;
		};

		partition@100000 {
			label = "env";
			reg = <0x100000 0x10000>;
		};
	};
};

&ir_receiver {
	status = "okay";
	pinctrl-0 = <&ir_recv_pins>;
	pinctrl-names = "default";
};

&ethmac {
	status = "okay";
	pinctrl-0 = <&eth_pins>;
	pinctrl-names = "default";
	phy-mode = "rmii";
};
