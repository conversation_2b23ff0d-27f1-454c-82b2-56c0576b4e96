// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2024 Ice<PERSON><PERSON> Zheng <<EMAIL>>
 */

/dts-v1/;
#include "sun8i-a33-vstar-core1.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "Rervision A33-Vstar";
	compatible = "rervision,a33-vstar",
		     "rervision,a33-core1",
		     "allwinner,sun8i-a33";

	aliases {
		serial0 = &uart0;
		ethernet0 = &r8152;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	reg_usb1_vbus: regulator-usb1-vbus {
		compatible = "regulator-fixed";
		regulator-name = "usb1-vbus";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
		enable-active-high;
		gpio = <&pio 1 2 GPIO_ACTIVE_HIGH>; /* PB2 */
	};

	wifi_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&r_pio 0 6 GPIO_ACTIVE_LOW>; /* PL6 */
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "ext_clock";
	};
};

&ac_power_supply {
	status = "okay";
};

&codec {
	status = "okay";
};

&dai {
	status = "okay";
};

&ehci0 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	hub@1 {
		/* Onboard GL850G hub which needs no extra power sequence */
		compatible = "usb5e3,608";
		reg = <1>;
		#address-cells = <1>;
		#size-cells = <0>;

		r8152: ethernet@4 {
			/*
			 * Onboard Realtek RTL8152 USB Ethernet,
			 * with no MAC address programmed
			 */
			compatible = "usbbda,8152";
			reg = <4>;
		};
	};
};

&lradc {
	vref-supply = <&reg_aldo3>;
	status = "okay";

	button-191 {
		label = "V+";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <191011>;
	};

	button-391 {
		label = "V-";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <391304>;
	};

	button-600 {
		label = "BACK";
		linux,code = <KEY_BACK>;
		channel = <0>;
		voltage = <600000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 1 4 GPIO_ACTIVE_LOW>; /* PB4 */
	status = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pg_pins>;
	vmmc-supply = <&reg_dldo1>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	brcmf: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&r_pio>;
		interrupts = <0 7 IRQ_TYPE_LEVEL_LOW>; /* PL7 */
		interrupt-names = "host-wake";
	};
};

/*
 * Our WiFi chip needs both DLDO1 and DLDO2 to be powered at the same
 * time, with the two being in sync. Since this is not really
 * supported right now, just use the two as always on, and we will fix
 * it later.
 */
&reg_dldo1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi0";
};

&reg_dldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi1";
};

&reg_drivevbus {
	regulator-name = "usb0-vbus";
	status = "okay";
};

&sound {
	/* TODO: on-board microphone */

	simple-audio-card,widgets = "Headphone", "Headphone Jack";
	simple-audio-card,routing =
		"Left DAC", "DACL",
		"Right DAC", "DACR",
		"Headphone Jack", "HP";
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>, <&uart1_cts_rts_pg_pins>;
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "lpo";
		vbat-supply = <&reg_dldo1>;
		device-wakeup-gpios = <&r_pio 0 10 GPIO_ACTIVE_HIGH>; /* PL10 */
		host-wakeup-gpios = <&r_pio 0 9 GPIO_ACTIVE_HIGH>; /* PL9 */
		shutdown-gpios = <&r_pio 0 8 GPIO_ACTIVE_HIGH>; /* PL8 */
	};
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 8 GPIO_ACTIVE_HIGH>; /* PH8 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_drivevbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};
