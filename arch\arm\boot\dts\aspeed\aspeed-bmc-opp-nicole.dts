// SPDX-License-Identifier: GPL-2.0+
// Copyright 2019 YADRO
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "<PERSON> BMC";
	compatible = "yadro,nicole-bmc", "aspeed,ast2500";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@9f000000 {
			no-map;
			reg = <0x9f000000 0x01000000>; /* 16M */
		};

		flash_memory: region@98000000 {
			no-map;
			reg = <0x98000000 0x04000000>; /* 64M */
		};

		coldfire_memory: codefire_memory@9ef00000 {
			reg = <0x9ef00000 0x00100000>;
			no-map;
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	leds {
		compatible = "gpio-leds";

		power {
			label = "platform:green:power";
			gpios = <&gpio ASPEED_GPIO(AA, 4) GPIO_ACTIVE_HIGH>;
		};

		identify {
			label = "platform:blue:indicator";
			gpios = <&gpio ASPEED_GPIO(AA, 7) GPIO_ACTIVE_HIGH>;
		};

		fault {
			label = "platform:red:fault";
			gpios = <&gpio ASPEED_GPIO(AA, 3) GPIO_ACTIVE_HIGH>;
		};

		attention {
			label = "platform:yellow:alarm";
			gpios = <&gpio ASPEED_GPIO(AA, 1) GPIO_ACTIVE_HIGH>;
		};
	};

	fsi: gpio-fsi {
		compatible = "aspeed,ast2500-cf-fsi-master", "fsi-master";
		#address-cells = <2>;
		#size-cells = <0>;
		no-gpio-delays;

		memory-region = <&coldfire_memory>;
		aspeed,sram = <&sram>;
		aspeed,cvic = <&cvic>;

		clock-gpios = <&gpio ASPEED_GPIO(AA, 0) GPIO_ACTIVE_HIGH>;
		data-gpios = <&gpio ASPEED_GPIO(AA, 2) GPIO_ACTIVE_HIGH>;
		mux-gpios = <&gpio ASPEED_GPIO(A, 6) GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio ASPEED_GPIO(D, 0) GPIO_ACTIVE_HIGH>;
		trans-gpios = <&gpio ASPEED_GPIO(P, 1) GPIO_ACTIVE_HIGH>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		event-checkstop {
			label = "checkstop";
			gpios = <&gpio ASPEED_GPIO(J, 2) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(J, 2)>;
		};
	};

	iio-hwmon-battery {
		compatible = "iio-hwmon";
		io-channels = <&adc 12>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-max-frequency = <100000000>;
	};
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&uart1 {
	/* Rear RS-232 connector */
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default
			&pinctrl_nrts1_default
			&pinctrl_ndtr1_default
			&pinctrl_ndsr1_default
			&pinctrl_ncts1_default
			&pinctrl_ndcd1_default
			&pinctrl_nri1_default>;
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	use-ncsi;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
};

&i2c0 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c256";
		reg = <0x50>;
		pagesize = <64>;
	};
};

&i2c2 {
	status = "okay";
	/* CPU0 characterization connector */
};

&i2c3 {
	status = "okay";
	/* CLK GEN SI5338 */
};

&i2c4 {
	status = "okay";
	/* Voltage regulators for CPU0 */
};

&i2c5 {
	status = "okay";
	/* Voltage regulators for CPU1 */
};

&i2c6 {
	status = "okay";

	rtc@32 {
		compatible = "epson,rx8900";
		reg = <0x32>;
	};
};

&i2c7 {
	status = "okay";
	/* CPLD */
};

&gpio {
	gpio-line-names =
	/*A0-A7*/	"","cfam-reset","","","","","fsi-mux","",
	/*B0-B7*/	"","","","","","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"fsi-enable","bmc_power_up","sys_pwrok_buf",
			"func_mode0","func_mode1","func_mode2","","",
	/*E0-E7*/	"","ncsi_cfg","","","","","","",
	/*F0-F7*/	"","","","","","","","",
	/*G0-G7*/	"","","","","","","","",
	/*H0-H7*/	"","","","","","","","",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","checkstop","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","power-button","","","","","",
	/*P0-P7*/	"","fsi-trans","pm_rtc_adc_en","","","","","",
	/*Q0-Q7*/	"","","","","","","","id-button",
	/*R0-R7*/	"","software_pwrgood","","","","","","",
	/*S0-S7*/	"","","","","","","","seq_cont",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","","","","","","","",
	/*Z0-Z7*/	"","","","","","","","",
	/*AA0-AA7*/	"fsi-clock","led-attention","fsi-data","led-fault",
			"led-power","","","led-identify",
	/*AB0-AB7*/	"","","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";

	func_mode0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(D, 3) GPIO_ACTIVE_HIGH>;
		output-low;
	};
	func_mode1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(D, 4) GPIO_ACTIVE_HIGH>;
		output-low;
	};
	func_mode2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(D, 5) GPIO_ACTIVE_HIGH>;
		output-low;
	};
	seq_cont {
		gpio-hog;
		gpios = <ASPEED_GPIO(S, 7) GPIO_ACTIVE_HIGH>;
		output-low;
	};
	ncsi_cfg {
		gpio-hog;
		input;
		gpios = <ASPEED_GPIO(E, 1) GPIO_ACTIVE_HIGH>;
	};
};

&vuart {
	status = "okay";
};

&gfx {
	status = "okay";
	memory-region = <&gfx_memory>;
};

&ibt {
	status = "okay";
};

&vhub {
	status = "okay";
};

&adc {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
			&pinctrl_adc1_default
			&pinctrl_adc2_default
			&pinctrl_adc3_default
			&pinctrl_adc4_default
			&pinctrl_adc5_default
			&pinctrl_adc6_default
			&pinctrl_adc7_default
			&pinctrl_adc8_default
			&pinctrl_adc9_default
			&pinctrl_adc10_default
			&pinctrl_adc11_default
			&pinctrl_adc12_default
			&pinctrl_adc13_default
			&pinctrl_adc14_default
			&pinctrl_adc15_default>;
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

#include "ibm-power9-dual.dtsi"
