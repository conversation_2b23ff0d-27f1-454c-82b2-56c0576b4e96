// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Common Bindings for Cisco Meraki MX64 (Kingpin) and MX65 (Alamo) devices.
 *
 * Copyright (C) 2020-2021 <PERSON> <<EMAIL>>
 */

#include "bcm-nsp.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>

/ {
	pwm-leds {
		compatible = "pwm-leds";

		led-1 {
			function = LED_FUNCTION_INDICATOR;
			color = <LED_COLOR_ID_RED>;
			pwms = <&pwm 1 50000>;
			max-brightness = <255>;
		};

		led-2 {
			function = LED_FUNCTION_POWER;
			color = <LED_COLOR_ID_GREEN>;
			pwms = <&pwm 2 50000>;
			max-brightness = <255>;
		};

		led-3 {
			function = LED_FUNCTION_INDICATOR;
			color = <LED_COLOR_ID_BLUE>;
			pwms = <&pwm 3 50000>;
			max-brightness = <255>;
		};
	};
};

&amac2 {
	status = "okay";
	nvmem-cells = <&mac_address>;
	nvmem-cell-names = "mac-address";
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
		pagesize = <32>;
		read-only;

		nvmem-layout {
			compatible = "fixed-layout";
			#address-cells = <1>;
			#size-cells = <1>;

			mac_address: mac-address@66 {
				reg = <0x66 0x6>;
			};
		};
	};
};

&nand_controller {
	nand@0 {
		compatible = "brcm,nandcs";
		reg = <0>;
		nand-on-flash-bbt;

		#address-cells = <1>;
		#size-cells = <1>;

		nand-ecc-strength = <24>;
		nand-ecc-step-size = <1024>;

		brcm,nand-oob-sector-size = <27>;

		partition@0 {
			label = "u-boot";
			reg = <0x0 0x80000>;
			read-only;
		};

		partition@80000 {
			label = "shmoo";
			reg = <0x80000 0x80000>;
			read-only;
		};

		partition@100000 {
			label = "bootkernel1";
			reg = <0x100000 0x300000>;
		};

		partition@400000 {
			label = "nvram";
			reg = <0x400000 0x100000>;
		};

		partition@500000 {
			label = "bootkernel2";
			reg = <0x500000 0x300000>;
		};

		partition@800000 {
			label = "ubi";
			reg = <0x800000 0x3f700000>;
		};
	};
};

&ohci0 {
	status = "okay";
};

&pinctrl {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm_leds>;

	pwm_leds: pwm_leds {
		function = "pwm";
		groups = "pwm1_grp", "pwm2_grp", "pwm3_grp";
	};
};

&pwm {
	status = "okay";
	#pwm-cells = <2>;
};

&uart0 {
	clock-frequency = <62500000>;
	status = "okay";
};
