/*
 * Copyright 2016 Linaro Ltd
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

#include "arm-realview-eb-11mp.dts"

/ {
	model = "ARM RealView Emulation Baseboard with ARM11MPCore Rev B";
};

/*
 * The revision B has a distinctly different layout of the syscon, so
 * append a specific compatible-string.
 */
&syscon {
	compatible = "arm,realview-eb11mp-revb-syscon", "arm,realview-eb-syscon", "syscon", "simple-mfd";
};

&intc {
	reg = <0x10101000 0x1000>,
	      <0x10100100 0x100>;
};

&L2 {
	reg = <0x10102000 0x1000>;
};

&scu {
	reg = <0x10100000 0x100>;
};

&twd_timer {
	reg = <0x10100600 0x20>;
};

&twd_wdog {
	reg = <0x10100620 0x20>;
};

/*
 * On revision B, we cannot reach the secondary interrupt
 * controller, as a result, some peripherals that are dependent
 * on their IRQ cannot be reached, so disable them.
 */
&intc_second {
	status = "disabled";
};

&gpio0 {
	status = "disabled";
};

&gpio1 {
	status = "disabled";
};

&gpio2 {
	status = "disabled";
};

&serial2 {
	status = "disabled";
};

&serial3 {
	status = "disabled";
};

&ssp {
	status = "disabled";
};

&wdog {
	status = "disabled";
};
