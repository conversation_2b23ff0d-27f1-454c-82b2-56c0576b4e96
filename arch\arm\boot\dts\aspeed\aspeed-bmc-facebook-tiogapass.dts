// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2018 Facebook Inc.
// Author: <PERSON> <vija<PERSON><PERSON><PERSON>@fb.com>
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>

/ {
	model = "Facebook TiogaPass BMC";
	compatible = "facebook,tiogapass-bmc", "aspeed,ast2500";
	aliases {
		serial0 = &uart1;
		serial4 = &uart5;

		/*
		 * Hardcode the bus number of i2c switches' channels to
		 * avoid breaking the legacy applications.
		 */
		i2c16 = &imux16;
		i2c17 = &imux17;
		i2c18 = &imux18;
		i2c19 = &imux19;
		i2c20 = &imux20;
		i2c21 = &imux21;
		i2c22 = &imux22;
		i2c23 = &imux23;
		i2c24 = &imux24;
		i2c25 = &imux25;
		i2c26 = &imux26;
		i2c27 = &imux27;
		i2c28 = &imux28;
		i2c29 = &imux29;
		i2c30 = &imux30;
		i2c31 = &imux31;
	};
	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			      <&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>;
	};

};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
	};
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&lpc_ctrl {
	// Enable lpc clock
	status = "okay";
};

&uart1 {
	// Host Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default>;
};

&uart2 {
	// SoL Host Console
	status = "okay";
};

&uart3 {
	// SoL BMC Console
	status = "okay";
};

&uart5 {
	// BMC Console
	status = "okay";
};

&kcs2 {
	// BMC KCS channel 2
	status = "okay";
	aspeed,lpc-io-reg = <0xca8>;
};

&kcs3 {
	// BMC KCS channel 3
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&gpio {
	status = "okay";
	gpio-line-names =
	/*A0-A7*/	"BMC_CPLD_FPGA_SEL","","","","","","","",
	/*B0-B7*/	"","BMC_DEBUG_EN","","","","BMC_PPIN","PS_PWROK",
			"IRQ_PVDDQ_GHJ_VRHOT_LVT3",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"BIOS_MRC_DEBUG_MSG_DIS","BOARD_REV_ID0","",
			"BOARD_REV_ID1","IRQ_DIMM_SAVE_LVT3","BOARD_REV_ID2",
			"CPU_ERR0_LVT3_BMC","CPU_ERR1_LVT3_BMC",
	/*E0-E7*/	"RESET_BUTTON","RESET_OUT","POWER_BUTTON",
			"POWER_OUT","NMI_BUTTON","","CPU0_PROCHOT_LVT3_ BMC",
			"CPU1_PROCHOT_LVT3_ BMC",
	/*F0-F7*/	"IRQ_PVDDQ_ABC_VRHOT_LVT3","",
			"IRQ_PVCCIN_CPU0_VRHOT_LVC3",
			"IRQ_PVCCIN_CPU1_VRHOT_LVC3",
			"IRQ_PVDDQ_KLM_VRHOT_LVT3","","P3VBAT_BRIDGE_EN","",
	/*G0-G7*/	"CPU_ERR2_LVT3","CPU_CATERR_LVT3","PCH_BMC_THERMTRIP",
			"CPU0_SKTOCC_LVT3","","","","BIOS_SMI_ACTIVE",
	/*H0-H7*/	"LED_POST_CODE_0","LED_POST_CODE_1","LED_POST_CODE_2",
			"LED_POST_CODE_3","LED_POST_CODE_4","LED_POST_CODE_5",
			"LED_POST_CODE_6","LED_POST_CODE_7",
	/*I0-I7*/	"CPU0_FIVR_FAULT_LVT3","CPU1_FIVR_FAULT_LVT3",
			"FORCE_ADR","UV_ADR_TRIGGER_EN","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"IRQ_UV_DETECT","IRQ_OC_DETECT","HSC_TIMER_EXP","",
			"MEM_THERM_EVENT_PCH","PMBUS_ALERT_BUF_EN","","",
	/*M0-M7*/	"CPU0_RC_ERROR","CPU1_RC_ERROR","","OC_DETECT_EN",
			"CPU0_THERMTRIP_LATCH_LVT3",
			"CPU1_THERMTRIP_LATCH_LVT3","","",
	/*N0-N7*/	"","","","CPU_MSMI_LVT3","","BIOS_SPI_BMC_CTRL","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"BOARD_SKU_ID0","BOARD_SKU_ID1","BOARD_SKU_ID2",
			"BOARD_SKU_ID3","BOARD_SKU_ID4","BMC_PREQ",
			"BMC_PWR_DEBUG","RST_RSMRST",
	/*Q0-Q7*/	"","","","","UARTSW_LSB","UARTSW_MSB",
			"POST_CARD_PRES_BMC","PE_BMC_WAKE",
	/*R0-R7*/	"","","BMC_TCK_MUX_SEL","BMC_PRDY",
			"BMC_XDP_PRSNT_IN","RST_BMC_PLTRST_BUF","SLT_CFG0",
			"SLT_CFG1",
	/*S0-S7*/	"THROTTLE","BMC_READY","","HSC_SMBUS_SWITCH_EN","",
			"","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","BMC_FAULT","","",
	/*V0-V7*/	"","","","FAST_PROCHOT_EN","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","GLOBAL_RST_WARN",
			"CPU0_MEMABC_MEMHOT_LVT3_BMC",
			"CPU0_MEMDEF_MEMHOT_LVT3_BMC",
			"CPU1_MEMGHJ_MEMHOT_LVT3_BMC",
			"CPU1_MEMKLM_MEMHOT_LVT3_BMC",
	/*Y0-Y7*/	"SIO_S3","SIO_S5","BMC_JTAG_SEL","SIO_ONCONTROL","",
			"","","",
	/*Z0-Z7*/	"","SIO_POWER_GOOD","IRQ_PVDDQ_DEF_VRHOT_LVT3","",
			"","","","",
	/*AA0-AA7*/	"CPU1_SKTOCC_LVT3","IRQ_SML1_PMBUS_ALERT",
			"SERVER_POWER_LED","","PECI_MUX_SELECT","UV_HIGH_SET",
			"","POST_COMPLETE",
	/*AB0-AB7*/	"IRQ_HSC_FAULT","OCP_MEZZA_PRES","","","","","","",
	/*AC0-AC7*/	"","","","","","","","";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii2_default>;
	use-ncsi;
};

&adc {
	status = "okay";
};

&i2c0 {
	status = "okay";
	//Airmax Conn B, CPU0 PIROM, CPU1 PIROM
};

&i2c1 {
	status = "okay";
	//X24 Riser
	i2c-mux@71 {
		compatible = "nxp,pca9544";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x71>;

		imux16: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;

			ina230@45 {
				compatible = "ti,ina230";
				reg = <0x45>;
			};

			tmp75@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};

			tmp421@49 {
				compatible = "ti,tmp75";
				reg = <0x49>;
			};

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
				pagesize = <32>;
			};

			i2c-mux@73 {
				compatible = "nxp,pca9546";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;

				imux20: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux21: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux22: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux23: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

			};

		};

		imux17: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;

			ina230@45 {
				compatible = "ti,ina230";
				reg = <0x45>;
			};

			tmp421@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};

			tmp421@49 {
				compatible = "ti,tmp75";
				reg = <0x49>;
			};

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
				pagesize = <32>;
			};

			i2c-mux@73 {
				compatible = "nxp,pca9546";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;

				imux24: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux25: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux26: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux27: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

			};

		};

		imux18: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;

			ina230@45 {
				compatible = "ti,ina230";
				reg = <0x45>;
			};

			tmp421@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};

			tmp421@49 {
				compatible = "ti,tmp75";
				reg = <0x49>;
			};

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
				pagesize = <32>;
			};

			i2c-mux@73 {
				compatible = "nxp,pca9546";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x73>;

				imux28: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				imux29: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				imux30: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				imux31: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};

			};

		};

		imux19: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;

			i2c-switch@40 {
				compatible = "ti,ina230";
				reg = <0x40>;
			};

			i2c-switch@41 {
				compatible = "ti,ina230";
				reg = <0x41>;
			};

			i2c-switch@45 {
				compatible = "ti,ina230";
				reg = <0x45>;
			};

		};

	};
};

&i2c2 {
	status = "okay";
	// Mezz Management SMBus
};

&i2c3 {
	status = "okay";
	// SMBus to Board ID EEPROM
};

&i2c4 {
	status = "okay";
	// BMC Debug Header
	ipmb0@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c5 {
	status = "okay";
	// CPU Voltage regulators
	regulator@48 {
		compatible = "infineon,pxe1610";
		reg = <0x48>;
	};
	regulator@4a {
		compatible = "infineon,pxe1610";
		reg = <0x4a>;
	};
	regulator@50 {
		compatible = "infineon,pxe1610";
		reg = <0x50>;
	};
	regulator@52 {
		compatible = "infineon,pxe1610";
		reg = <0x52>;
	};
	regulator@58 {
		compatible = "infineon,pxe1610";
		reg = <0x58>;
	};
	regulator@5a {
		compatible = "infineon,pxe1610";
		reg = <0x5a>;
	};
	regulator@68 {
		compatible = "infineon,pxe1610";
		reg = <0x68>;
	};
	regulator@70 {
		compatible = "infineon,pxe1610";
		reg = <0x70>;
	};
	regulator@72 {
		compatible = "infineon,pxe1610";
		reg = <0x72>;
	};
};

&i2c6 {
	status = "okay";
	tpm@20 {
		compatible = "infineon,slb9645tt";
		reg = <0x20>;
	};
	tmp421@4e {
		compatible = "ti,tmp421";
		reg = <0x4e>;
	};
	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};
	eeprom@54 {
		compatible = "atmel,24c64";
		reg = <0x54>;
		pagesize = <32>;
	};
};

&i2c7 {
	status = "okay";
	//HSC, AirMax Conn A
	adm1278@45 {
		compatible = "adm1275";
		reg = <0x45>;
		shunt-resistor-micro-ohms = <250>;
	};
};

&i2c8 {
	status = "okay";
	tmp421@1f {
		compatible = "ti,tmp421";
		reg = <0x1f>;
	};
	//Mezz Sensor SMBus
};

&i2c9 {
	status = "okay";
	//USB Debug Connector
	ipmb0@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default>;
	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};
};
