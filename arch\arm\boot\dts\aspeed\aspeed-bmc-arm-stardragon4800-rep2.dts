// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "HXT StarDragon 4800 REP2 AST2520";
	compatible = "hxt,stardragon4800-rep2-bmc", "aspeed,ast2500";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x40000000>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
						<&adc 4>, <&adc 5>, <&adc 6>, <&adc 8>;
	};

	iio-hwmon-battery {
		compatible = "iio-hwmon";
		io-channels = <&adc 7>;
	};

	leds {
		compatible = "gpio-leds";

		system_fault1 {
			label = "System_fault1";
			gpios = <&gpio ASPEED_GPIO(I, 3) GPIO_ACTIVE_LOW>;
		};

		system_fault2 {
			label = "System_fault2";
			gpios = <&gpio ASPEED_GPIO(I, 2) GPIO_ACTIVE_LOW>;
		};
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;
	flash@0 {
		status = "okay";
	};
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi2ck_default
			&pinctrl_spi2miso_default
			&pinctrl_spi2mosi_default
			&pinctrl_spi2cs0_default>;
};

&uart3 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd3_default &pinctrl_rxd3_default>;
	current-speed = <115200>;
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;
};

&mac1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii2_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC2CLK>,
		 <&syscon ASPEED_CLK_MAC2RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";

	tmp421@1e {
		compatible = "ti,tmp421";
		reg = <0x1e>;
	};
	tmp421@2a {
		compatible = "ti,tmp421";
		reg = <0x2a>;
	};
	tmp421@1c {
		compatible = "ti,tmp421";
		reg = <0x1c>;
	};
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";

	tmp421@1f {
		compatible = "ti,tmp421";
		reg = <0x1f>;
	};
	nvt210@4c {
		compatible = "nvt210";
		reg = <0x4c>;
	};
	eeprom@50 {
		compatible = "atmel,24c128";
		reg = <0x50>;
		pagesize = <128>;
	};
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";

	pca9641@70 {
		compatible = "nxp,pca9641";
		reg = <0x70>;
		i2c-arb {
			#address-cells = <1>;
			#size-cells = <0>;
			eeprom@50 {
				compatible = "atmel,24c02";
				reg = <0x50>;
			};
			dps650ab@58 {
				compatible = "dps650ab";
				reg = <0x58>;
			};
		};
	};

	dps650ab@58 {
		compatible = "delta,dps650ab";
		reg = <0x58>;
	};

	dps650ab@59 {
		compatible = "delta,dps650ab";
		reg = <0x59>;
	};
};

&i2c9 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&gfx {
	status = "okay";
};

&gpio {
	pin_gpio_c7 {
		gpio-hog;
		gpios = <ASPEED_GPIO(C, 7) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "BIOS_SPI_MUX_S";
	};
	pin_gpio_d1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(D, 1) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "PHY2_RESET_N";
	};
};
