// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/leds/common.h>

/{
	model = "ASRock SPC621D8HM3 BMC";
	compatible = "asrock,spc621d8hm3-bmc", "aspeed,ast2500";

	aliases {
		serial4 = &uart5;

		i2c20 = &i2c1mux0ch0;
		i2c21 = &i2c1mux0ch1;
	};

	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	leds {
		compatible = "gpio-leds";

		/* BMC heartbeat */
		led-0 {
			gpios = <&gpio ASPEED_GPIO(H, 6) GPIO_ACTIVE_LOW>;
			function = LED_FUNCTION_HEARTBEAT;
			color = <LED_COLOR_ID_GREEN>;
			linux,default-trigger = "timer";
		};

		/* system fault */
		led-1 {
			gpios = <&gpio ASPEED_GPIO(Z, 2) GPIO_ACTIVE_LOW>;
			function = LED_FUNCTION_FAULT;
			color = <LED_COLOR_ID_RED>;
			panic-indicator;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 12>, <&adc 13>, <&adc 14>, <&adc 15>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>; /* 50 MHz */
#include "openbmc-flash-layout-64.dtsi"
	};
};

&uart5 {
	status = "okay";
};

&vuart {
	status = "okay";
	aspeed,lpc-io-reg = <0x2f8>;
	aspeed,lpc-interrupts = <3 IRQ_TYPE_LEVEL_HIGH>;
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;

	nvmem-cells = <&eth0_macaddress>;
	nvmem-cell-names = "mac-address";
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";

	/* hardware monitor/thermal sensor */
	temperature-sensor@29 {
		compatible = "nuvoton,nct7802";
		reg = <0x29>;
	};

	/* motherboard temp sensor (TMP1, near BMC) */
	temperature-sensor@4c {
		compatible = "nuvoton,w83773g";
		reg = <0x4c>;
	};

	/* motherboard FRU eeprom */
	eeprom@50 {
		compatible = "st,24c128", "atmel,24c128";
		reg = <0x50>;
		pagesize = <16>;

		nvmem-layout {
			compatible = "fixed-layout";
			#address-cells = <1>;
			#size-cells = <1>;

			eth0_macaddress: macaddress@3f80 {
				reg = <0x3f80 6>;
			};
		};
	};

	/* M.2 slot smbus mux */
	i2c-mux@71 {
		compatible = "nxp,pca9545";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;

		i2c1mux0ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c1mux0ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};
	};
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&video {
	status = "okay";
};

&vhub {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&peci0 {
	status = "okay";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default
		&pinctrl_pwm2_default
		&pinctrl_pwm3_default
		&pinctrl_pwm4_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};

	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};
};

&gpio {
	status = "okay";
	gpio-line-names =
		/*  A */ "LOCATORLED_STATUS_N", "LOCATORBTN_N",
			"BMC_READY_N", "FM_SPD_DDRCPU_LVLSHFT_EN",
			"", "", "", "",
		/*  B */ "NODE_ID_1", "NODE_ID_2", "PSU_FAN_FAIL_N", "",
			"", "", "", "GPIO_RST",
		/*  C */ "", "", "", "", "", "", "", "",
		/*  D */ "FP_PWR_BTN_MUX_N", "FM_BMC_PWRBTN_OUT_N",
			"FP_RST_BTN_N", "RST_BMC_RSTBTN_OUT_N",
			"NMI_BTN_N", "BMC_NMI",
			"", "",
		/*  E */ "", "", "", "FM_ME_RCVR_N", "", "", "", "",
		/*  F */ "BMC_SMB_SEL_N", "FM_CPU2_DISABLE_COD_N",
			"FM_REMOTE_DEBUG_BMC_EN", "FM_CPU_ERR0_LVT3_EN",
			"FM_CPU_ERR1_LVT3_EN", "FM_CPU_ERR2_LVT3_EN",
			"FM_MEM_THERM_EVENT_CPU1_LVT3_N", "FM_MEM_THERM_EVENT_CPU2_LVT3_N",
		/*  G */ "HWM_BAT_EN", "", "BMC_PHYRST_N", "FM_BIOS_SPI_BMC_CTRL",
			"BMC_ALERT1_N", "BMC_ALERT2_N", "BMC_ALERT3_N", "IRQ_SML0_ALERT_N",
		/*  H */ "BMC_SMB_PRESENT_1_N", "FM_PCH_CORE_VID_0", "FM_PCH_CORE_VID_1", "",
			"FM_MFG_MODE", "BMC_RTCRST", "BMC_HB_LED_N", "BMC_CASEOPEN",
		/*  I */ "IRQ_PVDDQ_ABCD_CPU1_VRHOT_LVC3_N", "IRQ_PVDDQ_ABCD_CPU2_VRHOT_LVC3_N",
			"IRQ_PVDDQ_EFGH_CPU1_VRHOT_LVC3_N", "IRQ_PVDDQ_EFGH_CPU2_VRHOT_LVC3_N",
			"", "", "", "",
		/*  J */ "", "", "", "", "", "", "", "",
		/*  K */ "", "", "", "", "", "", "", "",
		/*  L */ "", "", "", "", "", "", "", "",
		/*  M */ "FM_PVCCIN_CPU1_PWR_IN_ALERT_N", "FM_PVCCIN_CPU2_PWR_IN_ALERT_N",
			"IRQ_PVCCIN_CPU1_VRHOT_LVC3_N", "IRQ_PVCCIN_CPU2_VRHOT_LVC3_N",
			"FM_CPU1_PROCHOT_BMC_LVC3_N", "",
			"FM_CPU1_MEMHOT_OUT_N", "FM_CPU2_MEMHOT_OUT_N",
		/*  N */ "", "", "", "", "", "", "", "",
		/*  O */ "", "", "", "", "", "", "", "",
		/*  P */ "", "", "", "", "", "", "", "",
		/*  Q */ "", "", "", "", "", "", "RST_GLB_RST_WARN_N", "PCIE_WAKE_N",
		/*  R */ "", "", "FM_BMC_SUSACK_N", "FM_BMC_EUP_LOT6_N",
			"", "FM_BMC_PCH_SCI_LPC_N", "", "",
		/*  S */ "FM_DBP_PRESENT_N", "FM_CPU2_SKTOCC_LCT3_N",
			"FM_CPU1_FIVR_FAULT_LVT3", "FM_CPU2_FIVR_FAULT_LVT3",
			 "", "", "", "",
		/*  T */ "", "", "", "", "", "", "", "",
		/*  U */ "", "", "", "", "", "", "", "",
		/*  V */ "", "", "", "", "", "", "", "",
		/*  W */ "", "", "", "", "", "", "", "",
		/*  X */ "", "", "", "", "", "", "", "",
		/*  Y */ "FM_SLPS3_N", "FM_SLPS4_N", "", "FM_BMC_ONCTL_N_PLD",
			"", "", "", "",
		/*  Z */ "FM_CPU_MSMI_CATERR_LVT3_N", "", "SYSTEM_FAULT_LED_N", "BMC_THROTTLE_N",
			"", "", "", "",
		/* AA */ "FM_CPU1_THERMTRIP_LATCH_LVT3_N", "FM_CPU2_THERMTRIP_LATCH_LVT3_N",
			"FM_BIOS_POST_COMPLT_N", "DBP_BMC_SYSPWROK",
			"", "IRQ_SML0_ALERT_MUX_N",
			"IRQ_SMI_ACTIVE_N", "IRQ_NMI_EVENT_N",
		/* AB */ "FM_PCH_BMC_THERMTRIP_N", "PWRGD_SYS_PWROK",
			"ME_OVERRIDE", "IRQ_BMC_PCH_SMI_LPC_N",
			"", "", "", "",
		/* AC */ "", "", "", "", "", "", "", "";
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default /* 3VSB */
		&pinctrl_adc1_default	   /* 5VSB */
		&pinctrl_adc2_default	   /* CPU1 */
		&pinctrl_adc3_default	   /* NC */
		&pinctrl_adc4_default	   /* VCCMABCD */
		&pinctrl_adc5_default	   /* VCCMEFGH */
		&pinctrl_adc6_default	   /* NC */
		&pinctrl_adc7_default	   /* NC */
		&pinctrl_adc8_default	   /* PVNN_PCH */
		&pinctrl_adc9_default	   /* 1P05PCH */
		&pinctrl_adc10_default	   /* 1P8PCH */
		&pinctrl_adc11_default	   /* BAT */
		&pinctrl_adc12_default	   /* 3V */
		&pinctrl_adc13_default	   /* 5V */
		&pinctrl_adc14_default	   /* 12V */
		&pinctrl_adc15_default>;   /* GND */
};
