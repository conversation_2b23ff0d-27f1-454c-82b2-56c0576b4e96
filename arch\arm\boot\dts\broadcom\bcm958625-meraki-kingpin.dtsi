// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Device Tree Bindings for Cisco Meraki MX64 series (Kingpin).
 *
 * Copyright (C) 2020-2021 <PERSON> <<EMAIL>>
 */

#include "bcm958625-meraki-mx6x-common.dtsi"

/ {

	keys {
		compatible = "gpio-keys-polled";
		autorepeat;
		poll-interval = <20>;

		button-reset {
			label = "reset";
			linux,code = <KEY_RESTART>;
			gpios = <&gpioa 6 GPIO_ACTIVE_LOW>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			/* green:lan1-left */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <0>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 19 GPIO_ACTIVE_LOW>;
		};

		led-1 {
			/* green:lan1-right */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <1>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 18 GPIO_ACTIVE_LOW>;
		};

		led-2 {
			/* green:lan2-left */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <2>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 24 GPIO_ACTIVE_LOW>;
		};

		led-3 {
			/* green:lan2-right */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <3>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 20 GPIO_ACTIVE_LOW>;
		};

		led-4 {
			/* green:lan3-left */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <4>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 26 GPIO_ACTIVE_LOW>;
		};

		led-5 {
			/* green:lan3-right */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <5>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 25 GPIO_ACTIVE_LOW>;
		};

		led-6 {
			/* green:lan4-left */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <6>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 28 GPIO_ACTIVE_LOW>;
		};

		led-7 {
			/* green:lan4-right */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <7>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 27 GPIO_ACTIVE_LOW>;
		};

		led-8 {
			/* green:wan-left */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <8>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 30 GPIO_ACTIVE_LOW>;
		};

		led-9 {
			/* green:wan-right */
			function = LED_FUNCTION_ACTIVITY;
			function-enumerator = <9>;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&gpioa 29 GPIO_ACTIVE_LOW>;
		};

		led-a {
			/* amber:power */
			function = LED_FUNCTION_FAULT;
			color = <LED_COLOR_ID_AMBER>;
			gpios = <&gpioa 0 GPIO_ACTIVE_LOW>;
		};

		led-b {
			/* white:status */
			function = LED_FUNCTION_STATUS;
			color = <LED_COLOR_ID_WHITE>;
			gpios = <&gpioa 31 GPIO_ACTIVE_HIGH>;
		};
	};
};

&srab {
	compatible = "brcm,bcm58625-srab", "brcm,nsp-srab";
	status = "okay";

	ports {
		port@0 {
			label = "lan1";
			reg = <0>;
		};

		port@1 {
			label = "lan2";
			reg = <1>;
		};

		port@2 {
			label = "lan3";
			reg = <2>;
		};

		port@3 {
			label = "lan4";
			reg = <3>;
		};

		port@4 {
			label = "wan";
			reg = <4>;
		};

		port@8 {
			ethernet = <&amac2>;
			reg = <8>;
			fixed-link {
				speed = <1000>;
				full-duplex;
			};
		};
	};
};
