/*
 * Broadcom BCM470X / BCM5301X ARM platform code.
 * Generic DTS part for all BCM53010, BCM53011, BCM53012, BCM53014, BCM53015,
 * BCM53016, BCM53017, BCM53018, BCM4707, BCM4708 and BCM4709 SoCs
 *
 * Licensed under the GNU/GPL. See COPYING for details.
 */

#include "bcm-ns.dtsi"

/ {
	mpcore-bus@19000000 {
		a9pll: arm_clk@0 {
			#clock-cells = <0>;
			compatible = "brcm,nsp-armpll";
			clocks = <&osc>;
			reg = <0x00000 0x1000>;
		};

		watchdog@20620 {
			compatible = "arm,cortex-a9-twd-wdt";
			reg = <0x20620 0x20>;
			interrupts = <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2) |
						  IRQ_TYPE_EDGE_RISING)>;
			clocks = <&periph_clk>;
		};
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc: oscillator {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <25000000>;
		};

		iprocmed: iprocmed {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clocks = <&genpll BCM_NSP_GENPLL_IPROCFAST_CLK>;
			clock-div = <2>;
			clock-mult = <1>;
		};

		iprocslow: iprocslow {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clocks = <&genpll BCM_NSP_GENPLL_IPROCFAST_CLK>;
			clock-div = <4>;
			clock-mult = <1>;
		};

		periph_clk: periph_clk {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clocks = <&a9pll>;
			clock-div = <2>;
			clock-mult = <1>;
		};
	};

	i2c0: i2c@18009000 {
		compatible = "brcm,iproc-i2c";
		reg = <0x18009000 0x50>;
		interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clock-frequency = <100000>;
		status = "disabled";
	};

	dmu-bus@1800c000 {
		cru-bus@100 {
			lcpll0: clock-controller@100 {
				#clock-cells = <1>;
				compatible = "brcm,nsp-lcpll0";
				reg = <0x100 0x14>;
				clocks = <&osc>;
				clock-output-names = "lcpll0", "pcie_phy",
						     "sdio", "ddr_phy";
			};

			genpll: clock-controller@140 {
				#clock-cells = <1>;
				compatible = "brcm,nsp-genpll";
				reg = <0x140 0x24>;
				clocks = <&osc>;
				clock-output-names = "genpll", "phy",
						     "ethernetclk",
						     "usbclk", "iprocfast",
						     "sata1", "sata2";
			};
		};
	};

	spi@18029200 {
		compatible = "brcm,spi-nsp-qspi", "brcm,spi-bcm-qspi";
		reg = <0x18029200 0x184>,
		      <0x18029000 0x124>,
		      <0x1811b408 0x004>,
		      <0x180293a0 0x01c>;
		reg-names = "mspi", "bspi", "intr_regs", "intr_status_reg";
		interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "mspi_done",
				  "mspi_halted",
				  "spi_lr_fullness_reached",
				  "spi_lr_session_aborted",
				  "spi_lr_impatient",
				  "spi_lr_session_done",
				  "spi_lr_overread";
		clocks = <&iprocmed>;
		num-cs = <2>;
		#address-cells = <1>;
		#size-cells = <0>;

		spi_nor: flash@0 {
			compatible = "jedec,spi-nor";
			reg = <0>;
			spi-max-frequency = <20000000>;
			status = "disabled";

			partitions {
				compatible = "brcm,bcm947xx-cfe-partitions";
			};
		};
	};
};
