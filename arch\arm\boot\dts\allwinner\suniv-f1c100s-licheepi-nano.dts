// SPDX-License-Identifier: (GPL-2.0+ OR X11)
/*
 * Copyright 2018 Icenowy Zheng <<EMAIL>>
 */

/dts-v1/;
#include "suniv-f1c100s.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Lichee Pi Nano";
	compatible = "licheepi,licheepi-nano", "allwinner,suniv-f1c100s";

	aliases {
		mmc0 = &mmc0;
		serial0 = &uart0;
		spi0 = &spi0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	reg_vcc3v3: vcc3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&mmc0 {
	broken-cd;
	bus-width = <4>;
	disable-wp;
	status = "okay";
	vmmc-supply = <&reg_vcc3v3>;
};

&spi0 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi0_pc_pins>;
	status = "okay";

	flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "winbond,w25q128", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <********>;
	};
};

&otg_sram {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pe_pins>;
	status = "okay";
};

&codec {
	allwinner,audio-routing =
		"Headphone", "HP",
		"Headphone", "HPCOM",
		"MIC", "Mic";
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 4 2 GPIO_ACTIVE_HIGH>; /* PE2 */
	status = "okay";
};
