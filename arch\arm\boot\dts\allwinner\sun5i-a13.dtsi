/*
 * Copyright 2012 <PERSON><PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This library is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This library is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "sun5i.dtsi"

#include <dt-bindings/thermal/thermal.h>

/ {
	thermal-zones {
		cpu-thermal {
			/* milliseconds */
			polling-delay-passive = <250>;
			polling-delay = <1000>;
			thermal-sensors = <&rtp>;

			cooling-maps {
				map0 {
					trip = <&cpu_alert0>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};

			trips {
				cpu_alert0: cpu-alert0 {
					/* milliCelsius */
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_crit: cpu-crit {
					/* milliCelsius */
					temperature = <100000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	display-engine {
		compatible = "allwinner,sun5i-a13-display-engine";
		allwinner,pipelines = <&fe0>;
	};

	soc {
		pwm: pwm@1c20e00 {
			compatible = "allwinner,sun5i-a13-pwm";
			reg = <0x01c20e00 0xc>;
			clocks = <&ccu CLK_HOSC>;
			#pwm-cells = <3>;
			status = "disabled";
		};

	};
};

&ccu {
	compatible = "allwinner,sun5i-a13-ccu";
};

&cpu0 {
	clock-latency = <244144>; /* 8 32k periods */
	operating-points =
		/* kHz	  uV */
		<1008000 1400000>,
		<912000 1350000>,
		<864000 1300000>,
		<624000 1200000>,
		<576000 1200000>,
		<432000 1200000>;
	#cooling-cells = <2>;
};

&pio {
	compatible = "allwinner,sun5i-a13-pinctrl";
};
