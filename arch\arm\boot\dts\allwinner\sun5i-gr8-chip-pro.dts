/*
 * Copyright 2016 Free Electrons
 * Copyright 2016 NextThing Co
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun5i-gr8.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "NextThing C.H.I.P. Pro";
	compatible = "nextthing,chip-pro", "nextthing,gr8";

	aliases {
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		serial0 = &uart1;
		serial1 = &uart2;
		serial2 = &uart3;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "chip-pro:white:status";
			gpios = <&axp_gpio 2 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

	mmc0_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 1 10 GPIO_ACTIVE_LOW>; /* PB10 */
	};
};

&codec {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;

		/*
		* The interrupt is routed through the "External Fast
		* Interrupt Request" pin (ball G13 of the module)
		* directly to the main interrupt controller, without
		* any other controller interfering.
		*/
		interrupts = <0>;
	};
};

#include "axp209.dtsi"

&i2c1 {
	status = "disabled";
};

&i2s0 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2s0_mclk_pin>, <&i2s0_data_pins>;
	status = "disabled";
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	mmc-pwrseq = <&mmc0_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&nfc {
	pinctrl-names = "default";
	pinctrl-0 = <&nand_pins &nand_cs0_pin &nand_rb0_pin>;
	status = "okay";

	nand@0 {
		reg = <0>;
		allwinner,rb = <0>;
		nand-ecc-mode = "hw";
	};
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pin>, <&pwm1_pins>;
	status = "disabled";
};

&reg_dcdc2 {
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
	regulator-always-on;
};

&reg_dcdc3 {
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1300000>;
	regulator-name = "vdd-sys";
	regulator-always-on;
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-min-microvolt = <2700000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "avcc";
	regulator-always-on;
};

/*
 * Both LDO3 and LDO4 are used in parallel to power up the
 * WiFi/BT chip.
 */
&reg_ldo3 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi-1";
	regulator-always-on;
};

&reg_ldo4 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi-2";
	regulator-always-on;
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>, <&uart1_cts_rts_pins>;
	status = "okay";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pd_pins>, <&uart2_cts_rts_pd_pins>;
	status = "disabled";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pg_pins>, <&uart3_cts_rts_pg_pins>;
	status = "okay";
};

&usb_otg {
	/*
	 * The CHIP Pro doesn't have a controllable VBUS, nor does it
	 * have any 5v rail on the board itself.
	 *
	 * If one wants to use it as a true OTG port, it should be
	 * done in the baseboard, and its DT / overlay will add it.
	 */
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 2 GPIO_ACTIVE_HIGH>; /* PG2 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb1_vbus-supply = <&reg_vcc5v0>;
	status = "okay";
};
