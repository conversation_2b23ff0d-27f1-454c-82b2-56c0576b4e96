// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2020 Facebook Inc.

partitions {
	compatible = "fixed-partitions";
	#address-cells = <1>;
	#size-cells = <1>;

	/*
	 * u-boot partition: 896KB.
	 */
	u-boot@0 {
		reg = <0x0 0xe0000>;
		label = "u-boot";
	};

	/*
	 * u-boot environment variables: 64KB.
	 */
	u-boot-env@e0000 {
		reg = <0xe0000 0x10000>;
		label = "env";
	};

	/*
	 * image metadata partition (64KB), used by Facebook internal
	 * tools.
	 */
	image-meta@f0000 {
		reg = <0xf0000 0x10000>;
		label = "meta";
	};

	/*
	 * FIT image: 119 MB.
	 */
	fit@100000 {
		reg = <0x100000 0x7700000>;
		label = "fit";
	};

	/*
	 * "data0" partition (8MB) is used by Facebook BMC platforms as
	 * persistent data store.
	 */
	data0@7800000 {
		reg = <0x7800000 0x800000>;
		label = "data0";
	};

	/*
	 * Although the master partition can be created by enabling
	 * MTD_PARTITIONED_MASTER option, below "flash0" partition is
	 * explicitly created to avoid breaking legacy applications.
	 */
	flash0@0 {
		reg = <0x0 0x8000000>;
		label = "flash0";
	};
};
