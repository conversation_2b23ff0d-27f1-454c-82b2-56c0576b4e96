// SPDX-License-Identifier: GPL-2.0+

partitions {
	compatible = "fixed-partitions";
	#address-cells = <1>;
	#size-cells = <1>;

	u-boot@0 {
		reg = <0x0 0x60000>;
		label = "u-boot";
	};

	u-boot-env@60000 {
		reg = <0x60000 0x20000>;
		label = "u-boot-env";
	};

	kernel@80000 {
		reg = <0x80000 0x440000>;
		label = "kernel";
	};

	rofs@4c0000 {
		reg = <0x4c0000 0x1740000>;
		label = "rofs";
	};

	rwfs@1c00000 {
		reg = <0x1c00000 0x400000>;
		label = "rwfs";
	};
};
