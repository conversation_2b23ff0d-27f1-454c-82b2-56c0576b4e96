# SPDX-License-Identifier: GPL-2.0
dtb-$(CONFIG_ARCH_INTEGRATOR) += \
	integratorap.dtb \
	integratorap-im-pd1.dtb \
	integratorcp.dtb
dtb-$(CONFIG_ARCH_MPS2) += \
	mps2-an385.dtb \
	mps2-an399.dtb
dtb-$(CONFIG_ARCH_REALVIEW) += \
	arm-realview-pb1176.dtb \
	arm-realview-pb11mp.dtb \
	arm-realview-eb.dtb \
	arm-realview-eb-bbrevd.dtb \
	arm-realview-eb-11mp.dtb \
	arm-realview-eb-11mp-bbrevd.dtb \
	arm-realview-eb-11mp-ctrevb.dtb \
	arm-realview-eb-11mp-bbrevd-ctrevb.dtb \
	arm-realview-eb-a9mp.dtb \
	arm-realview-eb-a9mp-bbrevd.dtb \
	arm-realview-pba8.dtb \
	arm-realview-pbx-a9.dtb
dtb-$(CONFIG_ARCH_VERSATILE) += \
	versatile-ab.dtb \
	versatile-ab-ib2.dtb \
	versatile-pb.dtb
dtb-$(CONFIG_ARCH_VEXPRESS) += \
	vexpress-v2p-ca5s.dtb \
	vexpress-v2p-ca9.dtb \
	vexpress-v2p-ca15-tc1.dtb \
	vexpress-v2p-ca15_a7.dtb
