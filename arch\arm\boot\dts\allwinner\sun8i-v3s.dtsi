/*
 * Copyright (C) 2016 Icenowy Zheng <<EMAIL>>
 * Copyright (C) 2021 <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/sun6i-rtc.h>
#include <dt-bindings/clock/sun8i-v3s-ccu.h>
#include <dt-bindings/reset/sun8i-v3s-ccu.h>
#include <dt-bindings/clock/sun8i-de2.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	chosen {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		framebuffer-lcd {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "mixer0-lcd0";
			clocks = <&display_clocks CLK_MIXER0>,
				 <&ccu CLK_TCON0>;
			status = "disabled";
		};
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clocks = <&ccu CLK_CPU>;
		};
	};

	de: display-engine {
		compatible = "allwinner,sun8i-v3s-display-engine";
		allwinner,pipelines = <&mixer0>;
		status = "disabled";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: osc24M-clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-accuracy = <50000>;
			clock-output-names = "osc24M";
		};

		osc32k: osc32k-clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-accuracy = <50000>;
			clock-output-names = "ext-osc32k";
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		display_clocks: clock@1000000 {
			compatible = "allwinner,sun8i-v3s-de2-clk";
			reg = <0x01000000 0x10000>;
			clocks = <&ccu CLK_BUS_DE>,
				 <&ccu CLK_DE>;
			clock-names = "bus",
				      "mod";
			resets = <&ccu RST_BUS_DE>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		mixer0: mixer@1100000 {
			compatible = "allwinner,sun8i-v3s-de2-mixer";
			reg = <0x01100000 0x100000>;
			clocks = <&display_clocks 0>,
				 <&display_clocks 6>;
			clock-names = "bus",
				      "mod";
			resets = <&display_clocks 0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				mixer0_out: port@1 {
					reg = <1>;

					mixer0_out_tcon0: endpoint {
						remote-endpoint = <&tcon0_in_mixer0>;
					};
				};
			};
		};

		syscon: system-control@1c00000 {
			compatible = "allwinner,sun8i-v3s-system-control",
				     "allwinner,sun8i-h3-system-control";
			reg = <0x01c00000 0xd0>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;
		};

		nmi_intc: interrupt-controller@1c000d0 {
			compatible = "allwinner,sun8i-v3s-nmi",
				     "allwinner,sun9i-a80-nmi";
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x01c000d0 0x0c>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun8i-v3s-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DMA>;
			resets = <&ccu RST_BUS_DMA>;
			#dma-cells = <1>;
		};

		tcon0: lcd-controller@1c0c000 {
			compatible = "allwinner,sun8i-v3s-tcon";
			reg = <0x01c0c000 0x1000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_TCON0>,
				 <&ccu CLK_TCON0>;
			clock-names = "ahb",
				      "tcon-ch0";
			clock-output-names = "tcon-data-clock";
			#clock-cells = <0>;
			resets = <&ccu RST_BUS_TCON0>;
			reset-names = "lcd";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					reg = <0>;

					tcon0_in_mixer0: endpoint {
						remote-endpoint = <&mixer0_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};
			};
		};


		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC0>,
				 <&ccu CLK_MMC0>,
				 <&ccu CLK_MMC0_OUTPUT>,
				 <&ccu CLK_MMC0_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC0>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC1>,
				 <&ccu CLK_MMC1>,
				 <&ccu CLK_MMC1_OUTPUT>,
				 <&ccu CLK_MMC1_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC1>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC2>,
				 <&ccu CLK_MMC2>,
				 <&ccu CLK_MMC2_OUTPUT>,
				 <&ccu CLK_MMC2_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC2>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		crypto@1c15000 {
			compatible = "allwinner,sun8i-v3s-crypto",
				     "allwinner,sun8i-a33-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CE>, <&ccu CLK_CE>;
			clock-names = "ahb", "mod";
			dmas = <&dma 16>, <&dma 16>;
			dma-names = "rx", "tx";
			resets = <&ccu RST_BUS_CE>;
			reset-names = "ahb";
		};

		usb_otg: usb@1c19000 {
			compatible = "allwinner,sun8i-h3-musb";
			reg = <0x01c19000 0x0400>;
			clocks = <&ccu CLK_BUS_OTG>;
			resets = <&ccu RST_BUS_OTG>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			status = "disabled";
		};

		usbphy: phy@1c19400 {
			compatible = "allwinner,sun8i-v3s-usb-phy";
			reg = <0x01c19400 0x2c>,
			      <0x01c1a800 0x4>;
			reg-names = "phy_ctrl",
				    "pmu0";
			clocks = <&ccu CLK_USB_PHY0>;
			clock-names = "usb0_phy";
			resets = <&ccu RST_USB_PHY0>;
			reset-names = "usb0_reset";
			status = "disabled";
			#phy-cells = <1>;
		};

		ehci: usb@1c1a000 {
			compatible = "allwinner,sun8i-v3s-ehci", "generic-ehci";
			reg = <0x01c1a000 0x100>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI0>, <&ccu CLK_BUS_OHCI0>;
			resets = <&ccu RST_BUS_EHCI0>, <&ccu RST_BUS_OHCI0>;
			phys = <&usbphy 0>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci: usb@1c1a400 {
			compatible = "allwinner,sun8i-v3s-ohci", "generic-ohci";
			reg = <0x01c1a400 0x100>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI0>, <&ccu CLK_BUS_OHCI0>,
				 <&ccu CLK_USB_OHCI0>;
			resets = <&ccu RST_BUS_EHCI0>, <&ccu RST_BUS_OHCI0>;
			phys = <&usbphy 0>;
			phy-names = "usb";
			status = "disabled";
		};

		ccu: clock@1c20000 {
			compatible = "allwinner,sun8i-v3s-ccu";
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&rtc CLK_OSC32K>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		rtc: rtc@1c20400 {
			#clock-cells = <1>;
			compatible = "allwinner,sun8i-v3-rtc";
			reg = <0x01c20400 0x54>;
			interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc32k>;
			clock-output-names = "osc32k", "osc32k-out";
		};

		pio: pinctrl@1c20800 {
			compatible = "allwinner,sun8i-v3s-pinctrl";
			reg = <0x01c20800 0x400>;
			interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_PIO>, <&osc24M>,
				 <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			#gpio-cells = <3>;
			interrupt-controller;
			#interrupt-cells = <3>;

			/omit-if-no-ref/
			csi0_mclk_pin: csi0-mclk-pin {
				pins = "PE20";
				function = "csi_mipi";
			};

			/omit-if-no-ref/
			csi1_8bit_pins: csi1-8bit-pins {
				pins = "PE0", "PE2", "PE3", "PE8", "PE9",
				       "PE10", "PE11", "PE12", "PE13", "PE14",
				       "PE15";
				function = "csi";
			};

			/omit-if-no-ref/
			csi1_mclk_pin: csi1-mclk-pin {
				pins = "PE1";
				function = "csi";
			};

			i2c0_pins: i2c0-pins {
				pins = "PB6", "PB7";
				function = "i2c0";
			};

			/omit-if-no-ref/
			i2c1_pb_pins: i2c1-pb-pins {
				pins = "PB8", "PB9";
				function = "i2c1";
			};

			/omit-if-no-ref/
			i2c1_pe_pins: i2c1-pe-pins {
				pins = "PE21", "PE22";
				function = "i2c1";
			};

			uart0_pb_pins: uart0-pb-pins {
				pins = "PB8", "PB9";
				function = "uart0";
			};

			/omit-if-no-ref/
			uart1_pe_pins: uart1-pe-pins {
				pins = "PE21", "PE22";
				function = "uart1";
			};

			uart2_pins: uart2-pins {
				pins = "PB0", "PB1";
				function = "uart2";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2", "PF3",
				       "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc1_pins: mmc1-pins {
				pins = "PG0", "PG1", "PG2", "PG3",
				       "PG4", "PG5";
				function = "mmc1";
				drive-strength = <30>;
				bias-pull-up;
			};

			/omit-if-no-ref/
			pwm0_pin: pwm0-pin {
				pins = "PB4";
				function = "pwm0";
			};

			/omit-if-no-ref/
			pwm1_pin: pwm1-pin {
				pins = "PB5";
				function = "pwm1";
			};

			spi0_pins: spi0-pins {
				pins = "PC0", "PC1", "PC2", "PC3";
				function = "spi0";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun8i-v3s-timer";
			reg = <0x01c20c00 0xa0>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		wdt0: watchdog@1c20ca0 {
			compatible = "allwinner,sun6i-a31-wdt";
			reg = <0x01c20ca0 0x20>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		pwm: pwm@1c21400 {
			compatible = "allwinner,sun8i-v3s-pwm",
				     "allwinner,sun7i-a20-pwm";
			reg = <0x01c21400 0xc>;
			clocks = <&osc24M>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		lradc: lradc@1c22800 {
			compatible = "allwinner,sun4i-a10-lradc-keys";
			reg = <0x01c22800 0x400>;
			interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		codec: codec@1c22c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-v3s-codec";
			reg = <0x01c22c00 0x400>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CODEC>, <&ccu CLK_AC_DIG>;
			clock-names = "apb", "codec";
			resets = <&ccu RST_BUS_CODEC>;
			dmas = <&dma 15>, <&dma 15>;
			dma-names = "rx", "tx";
			allwinner,codec-analog-controls = <&codec_analog>;
			status = "disabled";
		};

		codec_analog: codec-analog@1c23000 {
			compatible = "allwinner,sun8i-v3s-codec-analog";
			reg = <0x01c23000 0x4>;
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART0>;
			dmas = <&dma 6>, <&dma 6>;
			dma-names = "tx", "rx";
			resets = <&ccu RST_BUS_UART0>;
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART1>;
			dmas = <&dma 7>, <&dma 7>;
			dma-names = "tx", "rx";
			resets = <&ccu RST_BUS_UART1>;
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART2>;
			dmas = <&dma 8>, <&dma 8>;
			dma-names = "tx", "rx";
			resets = <&ccu RST_BUS_UART2>;
			pinctrl-0 = <&uart2_pins>;
			pinctrl-names = "default";
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C0>;
			resets = <&ccu RST_BUS_I2C0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C1>;
			resets = <&ccu RST_BUS_I2C1>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		emac: ethernet@1c30000 {
			compatible = "allwinner,sun8i-v3s-emac";
			syscon = <&syscon>;
			reg = <0x01c30000 0x10000>;
			interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq";
			resets = <&ccu RST_BUS_EMAC>;
			reset-names = "stmmaceth";
			clocks = <&ccu CLK_BUS_EMAC>;
			clock-names = "stmmaceth";
			phy-handle = <&int_mii_phy>;
			phy-mode = "mii";
			status = "disabled";

			mdio: mdio {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,dwmac-mdio";
			};

			mdio_mux: mdio-mux {
				compatible = "allwinner,sun8i-h3-mdio-mux";
				#address-cells = <1>;
				#size-cells = <0>;

				mdio-parent-bus = <&mdio>;
				/* Only one MDIO is usable at the time */
				internal_mdio: mdio@1 {
					compatible = "allwinner,sun8i-h3-mdio-internal";
					reg = <1>;
					#address-cells = <1>;
					#size-cells = <0>;

					int_mii_phy: ethernet-phy@1 {
						compatible = "ethernet-phy-ieee802.3-c22";
						reg = <1>;
						clocks = <&ccu CLK_BUS_EPHY>;
						resets = <&ccu RST_BUS_EPHY>;
					};
				};
			};
		};

		spi0: spi@1c68000 {
			compatible = "allwinner,sun8i-h3-spi";
			reg = <0x01c68000 0x1000>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPI0>, <&ccu CLK_SPI0>;
			clock-names = "ahb", "mod";
			dmas = <&dma 23>, <&dma 23>;
			dma-names = "rx", "tx";
			pinctrl-names = "default";
			pinctrl-0 = <&spi0_pins>;
			resets = <&ccu RST_BUS_SPI0>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		gic: interrupt-controller@1c81000 {
			compatible = "arm,gic-400";
			reg = <0x01c81000 0x1000>,
			      <0x01c82000 0x2000>,
			      <0x01c84000 0x2000>,
			      <0x01c86000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		csi1: camera@1cb4000 {
			compatible = "allwinner,sun8i-v3s-csi";
			reg = <0x01cb4000 0x3000>;
			interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CSI>,
				 <&ccu CLK_CSI1_SCLK>,
				 <&ccu CLK_DRAM_CSI>;
			clock-names = "bus", "mod", "ram";
			resets = <&ccu RST_BUS_CSI>;
			status = "disabled";
		};
	};
};
