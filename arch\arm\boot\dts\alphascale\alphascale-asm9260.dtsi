/*
 * Copyright 2014 Oleksi<PERSON> <<EMAIL>>
 *
 * Licensed under the X11 license or the GPL v2 (or later)
 */

#include <dt-bindings/clock/alphascale,asm9260.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&icoll>;

	memory {
		device_type = "memory";
		reg = <0x20000000 0x2000000>;
	};

	cpus {
		#address-cells = <0>;
		#size-cells = <0>;

		cpu {
			compatible = "arm,arm926ej-s";
			device_type = "cpu";
			clocks = <&acc CLKID_SYS_CPU>;
		};
	};

	osc24m: oscillator {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <24000000>;
		clock-accuracy = <30000>;
	};

	soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges;

		acc: clock-controller@80040000 {
			compatible = "alphascale,asm9260-clock-controller";
			#clock-cells = <1>;
			clocks = <&osc24m>;
			reg = <0x80040000 0x204>;
		};

		icoll: interrupt-controller@80054000 {
			compatible = "alphascale,asm9260-icoll";
			interrupt-controller;
			#interrupt-cells = <1>;
			reg = <0x80054000 0x200>;
		};

		timer0: timer@80088000 {
			compatible = "alphascale,asm9260-timer";
			reg = <0x80088000 0x4000>;
			clocks = <&acc CLKID_AHB_TIMER0>;
			interrupts = <29>;
		};
	};
};
