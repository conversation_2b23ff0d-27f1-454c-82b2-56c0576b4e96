// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Broadcom BCM470X / BCM5301X ARM platform code.
 * DTS for Meraki MR26 / Codename: Venom
 *
 * Copyright (C) 2022 <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "bcm4708.dtsi"
#include "bcm5301x-nand-cs0-bch8.dtsi"
#include <dt-bindings/leds/common.h>

/ {
	compatible = "meraki,mr26", "brcm,bcm53015", "brcm,bcm4708";
	model = "Meraki MR26";

	memory@0 {
		reg = <0x00000000 0x08000000>;
		device_type = "memory";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			function = LED_FUNCTION_FAULT;
			color = <LED_COLOR_ID_AMBER>;
			gpios = <&chipcommon 13 GPIO_ACTIVE_HIGH>;
			panic-indicator;
		};
		led-1 {
			function = LED_FUNCTION_INDICATOR;
			color = <LED_COLOR_ID_WHITE>;
			gpios = <&chipcommon 12 GPIO_ACTIVE_HIGH>;
		};
	};

	keys {
		compatible = "gpio-keys";

		key-restart {
			label = "Reset";
			linux,code = <KEY_RESTART>;
			gpios = <&chipcommon 11 GPIO_ACTIVE_LOW>;
		};
	};
};

&uart0 {
	clock-frequency = <50000000>;
	/delete-property/ clocks;
};

&uart1 {
	status = "disabled";
};

&gmac0 {
	status = "okay";

	nvmem-cells = <&macaddr_board_config_66>;
	nvmem-cell-names = "mac-address";
};

&gmac1 {
	status = "disabled";
};
&gmac2 {
	status = "disabled";
};
&gmac3 {
	status = "disabled";
};

&nandcs {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <0x1>;
		#size-cells = <0x1>;

		partition@0 {
			label = "u-boot";
			reg = <0x0 0x200000>;
			read-only;
		};

		partition@200000 {
			label = "u-boot-env";
			reg = <0x200000 0x200000>;
			/* empty */
		};

		partition@400000 {
			label = "u-boot-backup";
			reg = <0x400000 0x200000>;
			/* empty */
		};

		partition@600000 {
			label = "u-boot-env-backup";
			reg = <0x600000 0x200000>;
			/* empty */
		};

		partition@800000 {
			compatible = "linux,ubi";
			label = "ubi";
			reg = <0x800000 0x7780000>;

			volumes {
				ubi-volume-board-config {
					volname = "board-config";

					nvmem-layout {
						compatible = "fixed-layout";
						#address-cells = <1>;
						#size-cells = <1>;

						macaddr_board_config_66: macaddr@66 {
							reg = <0x66 0x6>;
						};
					};
				};
			};
		};
	};
};

&srab {
	status = "okay";

	ports {
		port@0 {
			label = "poe";
		};

		port@5 {
			label = "cpu";

			fixed-link {
				speed = <1000>;
				full-duplex;
			};
		};

		port@7 {
			status = "disabled";
		};

		port@8 {
			status = "disabled";
		};
	};
};

&i2c0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinmux_i2c>;

	clock-frequency = <100000>;

	ina219@40 {
		compatible = "ti,ina219"; /* PoE power */
		reg = <0x40>;
		shunt-resistor = <60000>; /* = 60 mOhms */
	};

	eeprom@56 {
		compatible = "atmel,24c64";
		reg = <0x56>;
		pagesize = <32>;
		read-only;
		#address-cells = <1>;
		#size-cells = <1>;

		/* it's empty */
	};
};

&thermal {
	status = "disabled";
	/* does not work, reads 418 degree Celsius */
};
