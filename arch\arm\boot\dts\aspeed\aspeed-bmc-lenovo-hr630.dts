// SPDX-License-Identifier: GPL-2.0+
/*
 * Device Tree file for Lenovo Hr630 platform
 *
 * Copyright (C) 2019-present Lenovo
 */

/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "HR630 BMC";
	compatible = "lenovo,hr630-bmc", "aspeed,ast2500";

	aliases {
		i2c14 = &i2c_rbp;
		i2c15 = &i2c_fbp1;
		i2c16 = &i2c_fbp2;
		i2c17 = &i2c_fbp3;
		i2c18 = &i2c_riser2;
		i2c19 = &i2c_pcie4;
		i2c20 = &i2c_riser1;
		i2c21 = &i2c_ocp;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=tty0 console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		flash_memory: region@98000000 {
			no-map;
			reg = <0x98000000 0x00100000>; /* 1M */
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	leds {
		compatible = "gpio-leds";

		heartbeat {
			gpios = <&gpio ASPEED_GPIO(J, 1) GPIO_ACTIVE_LOW>;
		};

		fault {
			gpios = <&gpio ASPEED_GPIO(J, 0) GPIO_ACTIVE_LOW>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
		<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
		<&adc 8>, <&adc 9>, <&adc 10>,
		<&adc 12>, <&adc 13>, <&adc 14>;
	};

};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout.dtsi"
	};
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default>;
};

&uart2 {
	/* Rear RS-232 connector */
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default
			&pinctrl_rxd2_default
			&pinctrl_nrts2_default
			&pinctrl_ndtr2_default
			&pinctrl_ndsr2_default
			&pinctrl_ncts2_default
			&pinctrl_ndcd2_default
			&pinctrl_nri2_default>;
};

&uart3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd3_default
			&pinctrl_rxd3_default>;
};

&uart5 {
	status = "okay";
};

&ibt {
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&adc {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
			&pinctrl_adc1_default
			&pinctrl_adc2_default
			&pinctrl_adc3_default
			&pinctrl_adc4_default
			&pinctrl_adc5_default
			&pinctrl_adc6_default
			&pinctrl_adc7_default
			&pinctrl_adc8_default
			&pinctrl_adc9_default
			&pinctrl_adc10_default
			&pinctrl_adc12_default
			&pinctrl_adc13_default
			&pinctrl_adc14_default>;
};

&i2c0 {
	status = "okay";
	/* temp1 inlet */
	tmp75@4e {
		compatible = "national,lm75";
		reg = <0x4e>;
	};
};

&i2c1 {
	status = "okay";
	/* temp2 outlet */
	tmp75@4d {
		compatible = "national,lm75";
		reg = <0x4d>;
	};
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
	/*	Slot 0,
	 *	Slot 1,
	 *	Slot 2,
	 *	Slot 3
	 */

	i2c-mux@70 {
		compatible = "nxp,pca9545";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;	/* may use mux@70 next. */

		i2c_rbp: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c_fbp1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c_fbp2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c_fbp3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c7 {
	status = "okay";

	/*	Slot 0,
	 *	Slot 1,
	 *	Slot 2,
	 *	Slot 3
	 */
	i2c-mux@76 {
		compatible = "nxp,pca9546";
		reg = <0x76>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;  /* may use mux@76 next. */

		i2c_riser2: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c_pcie4: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c_riser1: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c_ocp: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c8 {
	status = "okay";

	eeprom@57 {
		compatible = "atmel,24c256";
		reg = <0x57>;
		pagesize = <16>;
	};
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&gfx {
	status = "okay";
	memory-region = <&gfx_memory>;
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default
	&pinctrl_pwm1_default
	&pinctrl_pwm2_default
	&pinctrl_pwm3_default
	&pinctrl_pwm4_default
	&pinctrl_pwm5_default
	&pinctrl_pwm6_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};

	fan@4 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};

	fan@5 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05>;
	};

	fan@6 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06>;
	};

	fan@7 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x07>;
	};

	fan@8 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x08>;
	};

	fan@9 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x09>;
	};

	fan@10 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0a>;
	};

	fan@11 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0b>;
	};

	fan@12 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0c>;
	};

	fan@13 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0d>;
	};
};

&gpio {

	pin_gpio_b5 {
		gpio-hog;
		gpios = <ASPEED_GPIO(B, 5) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "IRQ_BMC_PCH_SMI_LPC_N";
	};

	pin_gpio_f0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 0) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "IRQ_BMC_PCH_NMI_R";
	};

	pin_gpio_f3 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 3) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "I2C_BUS0_RST_OUT_N";
	};

	pin_gpio_f4 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 4) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "FM_SKT0_FAULT_LED";
	};

	pin_gpio_f5 {
		gpio-hog;
		gpios = <ASPEED_GPIO(F, 5) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "FM_SKT1_FAULT_LED";
	};

	pin_gpio_g4 {
		gpio-hog;
		gpios = <ASPEED_GPIO(G, 4) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "FAN_PWR_CTL_N";
	};

	pin_gpio_g7 {
		gpio-hog;
		gpios = <ASPEED_GPIO(G, 7) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "RST_BMC_PCIE_I2CMUX_N";
	};

	pin_gpio_h2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(H, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "PSU1_FFS_N_R";
	};

	pin_gpio_h3 {
		gpio-hog;
		gpios = <ASPEED_GPIO(H, 3) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "PSU2_FFS_N_R";
	};

	pin_gpio_i3 {
		gpio-hog;
		gpios = <ASPEED_GPIO(I, 3) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_INTRUDED_COVER";
	};

	pin_gpio_j2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(J, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_BIOS_UPDATE_N";
	};

	pin_gpio_j3 {
		gpio-hog;
		gpios = <ASPEED_GPIO(J, 3) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "RST_BMC_HDD_I2CMUX_N";
	};

	pin_gpio_s2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(S, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_VGA_SW";
	};

	pin_gpio_s4 {
		gpio-hog;
		gpios = <ASPEED_GPIO(S, 4) GPIO_ACTIVE_HIGH>;
		output;
		line-name = "VBAT_EN_N";
	};

	pin_gpio_s6 {
		gpio-hog;
		gpios = <ASPEED_GPIO(S, 6) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "PU_BMC_GPIOS6";
	};

	pin_gpio_y0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(Y, 0) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "BMC_NCSI_MUX_CTL_S0";
	};

	pin_gpio_y1 {
		gpio-hog;
		gpios = <ASPEED_GPIO(Y, 1) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "BMC_NCSI_MUX_CTL_S1";
	};

	pin_gpio_z0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(Z, 0) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "I2C_RISER2_INT_N";
	};

	pin_gpio_z2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(Z, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "I2C_RISER2_RESET_N";
	};

	pin_gpio_z3 {
		gpio-hog;
		gpios = <ASPEED_GPIO(Z, 3) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "FM_BMC_PCH_SCI_LPC_N";
	};

	pin_gpio_z7 {
		gpio-hog;
		gpios = <ASPEED_GPIO(Z, 7) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "BMC_POST_CMPLT_N";
	};

	pin_gpio_aa0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(AA, 0) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "HOST_BMC_USB_SEL";
	};

	pin_gpio_aa5 {
		gpio-hog;
		gpios = <ASPEED_GPIO(AA, 5) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "I2C_BUS1_RST_OUT_N";
	};

};
