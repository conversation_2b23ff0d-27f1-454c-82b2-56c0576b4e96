// SPDX-License-Identifier: GPL-2.0+
// Copyright (C) 2021 YADRO
/dts-v1/;

#include "aspeed-bmc-vegman.dtsi"

/ {
	model = "YADRO VEGMAN Sx20 BMC";
	compatible = "yadro,vegman-sx20-bmc", "aspeed,ast2500";
};

&gpio {
	status = "okay";
	gpio-line-names =
	/*A0-A7*/	"CHASSIS_INTRUSION","CASE_OPEN_FAULT_RST","","","SPEAKER_BMC","FM_FORCE_BMC_UPDATE","","",
	/*B0-B7*/	"","","","","","","","",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"RESET_BUTTON","RESET_OUT","POWER_BUTTON","POWER_OUT","","","","",
	/*F0-F7*/	"NMI_OUT","CPU1_DISABLE_COD","","","SKT0_FAULT_LED","SKT1_FAULT_LED","RST_RGMII_PHYRST_DNP","",
	/*G0-G7*/	"CPU_ERR2","CPU_CATERR","PCH_BMC_THERMTRIP","","IRQ_NMI_EVENT","","","",
	/*H0-H7*/	"PWRGD_P3V3_RISER1","PWRGD_P3V3_RISER2","PWRGD_P3V3_RISER3","","MIO_BIOS_SEL","_SPI_FLASH_HOLD","_SPI_FLASH_WP","FM_240VA_STATUS",
	/*I0-I7*/	"","","","","","","","",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","","","","BMC_GPU_RISER_ID1","BMC_GPU_RISER_ID0","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","","","","","_SPI2_BMC_CS_SEL",
	/*P0-P7*/	"","P12V_HDDS_A_EN","P12V_HDDS_B_EN","P5V_HDDS_A_EN","PWRGD_P5V_HDDS_A","P5V_HDDS_B_EN","PWRGD_P5V_HDDS_B","",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"_SPI_RMM4_LITE_CS","","","","","","","",
	/*S0-S7*/	"_SPI2_BMC_CS1","","","IRQ_SML0_ALERT_MUX","FP_LED_STATUS_GREEN","FP_LED_STATUS_AMBER","FP_ID_LED","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"SIO_S3","SIO_S5","","SIO_ONCONTROL","","","","",
	/*Z0-Z7*/	"FM_BMC_PWR_BTN","SIO_POWER_GOOD","FM_BMC_PWRBTN_OUT","FM_BMC_PCH_SCI_LPC","","","","",
	/*AA0-AA7*/	"CPU_CLK_MUX_SEL","IRQ_SML1_PMBUS_ALERT","FM_PVCCIN_CPU0_PWR_IN_ALERT","FM_PVCCIN_CPU1_PWR_IN_ALERT","BMC_SYS_PWR_FAULT","BMC_SYS_PWR_OK","SMI","POST_COMPLETE",
	/*AB0-AB7*/	"FM_CPU_BMCINIT","NMI_BUTTON","ID_BUTTON","PS_PWROK","","","","",
	/*AC0-AC7*/	"","","","","","","","";
};

&sgpio {
	ngpios = <80>;
	bus-frequency = <2000000>;
	status = "okay";
	/* SGPIO lines. even: input, odd: output */
	gpio-line-names =
	/*A0-A7*/	"CPU1_PRESENCE","","CPU1_THERMTRIP","","CPU1_VRHOT","","CPU1_FIVR_FAULT","","CPU1_MEM_ABCD_VRHOT","","CPU1_MEM_EFGH_VRHOT","","","","","",
	/*B0-B7*/	"CPU1_MISMATCH","","CPU1_MEM_THERM_EVENT","","CPU2_PRESENCE","","CPU2_THERMTRIP","","CPU2_VRHOT","","CPU2_FIVR_FAULT","","CPU2_MEM_ABCD_VRHOT","","CPU2_MEM_EFGH_VRHOT","",
	/*C0-C7*/	"","","","","CPU2_MISMATCH","","CPU2_MEM_THERM_EVENT","","","","","","","","","",
	/*D0-D7*/	"","","","","","","","","","","","","","","","",
	/*E0-E7*/	"","","","","","","","","","","","","","","","",
	/*F0-F7*/	"SGPIO_PLD_MINOR_REV_BIT0","","SGPIO_PLD_MINOR_REV_BIT1","","SGPIO_PLD_MINOR_REV_BIT2","","SGPIO_PLD_MINOR_REV_BIT3","","SGPIO_PLD_MAJOR_REV_BIT0","","SGPIO_PLD_MAJOR_REV_BIT1","","SGPIO_PLD_MAJOR_REV_BIT2","","SGPIO_PLD_MAJOR_REV_BIT3","",
	/*G0-G7*/	"MAIN_PLD_MINOR_REV_BIT0","","MAIN_PLD_MINOR_REV_BIT1","","MAIN_PLD_MINOR_REV_BIT2","","MAIN_PLD_MINOR_REV_BIT3","","MAIN_PLD_MAJOR_REV_BIT0","","MAIN_PLD_MAJOR_REV_BIT1","","MAIN_PLD_MAJOR_REV_BIT2","","MAIN_PLD_MAJOR_REV_BIT3","",
	/*H0-H7*/	"","","","","","","","","","","","","","","","",
	/*I0-I7*/	"","","","","","","","","","","","","","","","",
	/*J0-J7*/	"","","","","","","","","","","","","","","","";
};

&i2c11 {
	/* SMB_BMC_MGMT_LVC3 */
	gpio@21 {
		compatible = "nxp,pcal9535";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names =
		/*IO0.0-0.7*/	"", "", "CPU1_PE3_0_SLOT_PRSNT", "", "CPU1_PE1_GPU_PRSNT", "CPU1_PE3_1_SLOT_PRSNT", "PE_PCH_MEZ_PRSNT", "CPU0_PE3_1_SLOT_PRSNT",
		/*IO1.0-1.7*/	"CPU0_PE1_GPU_PRSNT", "CPU0_PE2_NVME2_PRSNT", "CPU1_PE2_NVME3_PRSNT", "CPU1_PE2_SLOT_PRSNT", "CPU1_PE2_NVME4_PRSNT", "", "CPU0_PE2_NVME1_PRSNT", "CPU0_PE3_0_RAID_PRSNT";
	};
	gpio@27 {
		compatible = "nxp,pca9698";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names =
		/*IO0.0-0.7*/	"PWRGD_PS_PWROK", "PWRGD_DSW_PWROK", "PWRGD_P5V_AUX", "PWRGD_P3V3_AUX", "PWRGD_P5V", "PWRGD_P3V3", "PWRGD_P1V8_PCH_AUX", "PWRGD_PCH_PVNN_AUX",
		/*IO1.0-1.7*/	"PWRGD_P1V05_PCH_AUX", "PWRGD_PCH_AUX_VRS", "PWRGD_PVCCIN_CPU0", "PWRGD_PVCCSA_CPU0", "PWRGD_PVCCIO_CPU0", "PWRGD_PVMCP_CPU0", "PWRGD_P1V0_CPU0", "PWRGD_PVDDQ_ABC_CPU0",
		/*IO2.0-2.7*/	"PWRGD_PVPP_ABC_CPU0", "PWRGD_PVTT_ABC_CPU0", "PWRGD_PVDDQ_DEF_CPU0", "PWRGD_PVPP_DEF_CPU0", "PWRGD_PVTT_DEF_CPU0", "PWRGD_PVCCIN_CPU1", "PWRGD_PVCCSA_CPU1", "PWRGD_PVCCIO_CPU1",
		/*IO3.0-3.7*/	"PWRGD_PVMCP_CPU1", "PWRGD_P1V0_CPU1", "PWRGD_PVDDQ_GHJ_CPU1", "PWRGD_PVPP_GHJ_CPU1", "PWRGD_PVTT_GHJ_CPU1", "PWRGD_PVDDQ_KLM_CPU1", "PWRGD_PVPP_KLM_CPU1", "PWRGD_PVTT_KLM_CPU1",
		/*IO4.0-4.7*/	"PWRGD_P5V_HDDS_A_R", "PWRGD_P5V_HDDS_B_R", "", "", "", "", "", "";
	};
};

&i2c13 {
	/* SMB_PCIE2_STBY_LVC3 */
	i2c-mux@71 {
		compatible = "nxp,pca9543";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;
	};
	i2c-mux@73 {
		compatible = "nxp,pca9545";
		reg = <0x73>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;
	};
};

&i2c2 {
	/* SMB_PCIE_STBY_LVC3 */
	i2c-mux@71 {
		compatible = "nxp,pca9545";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;
	};
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default
			 &pinctrl_pwm2_default &pinctrl_pwm3_default
			 &pinctrl_pwm4_default &pinctrl_pwm5_default
			 &pinctrl_pwm6_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};
	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};
	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};
	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};
	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};
	fan@5 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05>;
	};
	fan@6 {
		reg = <0x06>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06>;
	};
};
