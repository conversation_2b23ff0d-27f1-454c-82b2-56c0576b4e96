// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * DTSI for Emlid Neutis SoMs.
 *
 * Copyright (C) 2019 <PERSON><PERSON>ls<PERSON>i <<EMAIL>>
 */

#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	wifi_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 2 7 GPIO_ACTIVE_LOW>; /* PC7 */
		post-power-on-delay-ms = <200>;
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "ext_clock";
	};
};

&cpu0 {
	cpu-supply = <&vdd_cpux>;
};

&reg_usb0_vbus {
	gpio = <&r_pio 0 9 GPIO_ACTIVE_HIGH>;   /* PL9 */
	status = "okay";
};


&de {
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&ohci2 {
	status = "okay";
};

&ohci3 {
	status = "okay";
};


&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ehci2 {
	status = "okay";
};

&ehci3 {
	status = "okay";
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	status = "okay";
};


&mmc1 {
	vmmc-supply = <&reg_vcc3v3>;
	vqmmc-supply = <&reg_vcc3v3>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	brcmf: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&r_pio>;
		interrupts = <0 5 IRQ_TYPE_LEVEL_LOW>;	/* PL5 */
		interrupt-names = "host-wake";
	};
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pa_pins>;
	status = "okay";
};


&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>, <&uart1_rts_cts_pins>;
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "lpo";
		vbat-supply = <&reg_vcc3v3>;
		vddio-supply = <&reg_vcc3v3>;
		shutdown-gpios = <&pio 2 4 GPIO_ACTIVE_HIGH>; /* PC4 */
		device-wakeup-gpios = <&r_pio 0 7 GPIO_ACTIVE_HIGH>; /* PL7 */
	};
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&r_pio 0 8 GPIO_ACTIVE_HIGH>; /* PL8 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&codec {
	allwinner,audio-routing =
		"Line Out", "LINEOUT",
		"LINEIN", "Line In",
		"MIC1", "Mic",
		"MIC2", "Mic",
		"Mic",  "MBIAS";
};

&i2c0 {
	status = "okay";
};
