// SPDX-License-Identifier: GPL-2.0
/*
 * ARM Ltd. Versatile Express
 *
 * CoreTile Express A15x2 A7x3
 * Cortex-A15_A7 MPCore (V2P-CA15_A7)
 *
 * HBI-0249A
 */

/dts-v1/;
#include "vexpress-v2m-rs1.dtsi"

/ {
	model = "V2P-CA15_CA7";
	arm,hbi = <0x249>;
	arm,vexpress,site = <0xf>;
	compatible = "arm,vexpress,v2p-ca15_a7", "arm,vexpress";
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	chosen { };

	aliases {
		serial0 = &v2m_serial0;
		serial1 = &v2m_serial1;
		serial2 = &v2m_serial2;
		serial3 = &v2m_serial3;
		i2c0 = &v2m_i2c_dvi;
		i2c1 = &v2m_i2c_pcie;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0>;
			cci-control-port = <&cci_control1>;
			cpu-idle-states = <&CLUSTER_SLEEP_BIG>;
			capacity-dmips-mhz = <1024>;
			dynamic-power-coefficient = <990>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <1>;
			cci-control-port = <&cci_control1>;
			cpu-idle-states = <&CLUSTER_SLEEP_BIG>;
			capacity-dmips-mhz = <1024>;
			dynamic-power-coefficient = <990>;
		};

		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x100>;
			cci-control-port = <&cci_control2>;
			cpu-idle-states = <&CLUSTER_SLEEP_LITTLE>;
			capacity-dmips-mhz = <516>;
			dynamic-power-coefficient = <133>;
		};

		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x101>;
			cci-control-port = <&cci_control2>;
			cpu-idle-states = <&CLUSTER_SLEEP_LITTLE>;
			capacity-dmips-mhz = <516>;
			dynamic-power-coefficient = <133>;
		};

		cpu4: cpu@4 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x102>;
			cci-control-port = <&cci_control2>;
			cpu-idle-states = <&CLUSTER_SLEEP_LITTLE>;
			capacity-dmips-mhz = <516>;
			dynamic-power-coefficient = <133>;
		};

		idle-states {
			CLUSTER_SLEEP_BIG: cluster-sleep-big {
				compatible = "arm,idle-state";
				local-timer-stop;
				entry-latency-us = <1000>;
				exit-latency-us = <700>;
				min-residency-us = <2000>;
			};

			CLUSTER_SLEEP_LITTLE: cluster-sleep-little {
				compatible = "arm,idle-state";
				local-timer-stop;
				entry-latency-us = <1000>;
				exit-latency-us = <500>;
				min-residency-us = <2500>;
			};
		};
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0 0x80000000 0 0x40000000>;
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		/* Chipselect 2 is physically at 0x18000000 */
		vram: vram@18000000 {
			/* 8 MB of designated video RAM */
			compatible = "shared-dma-pool";
			reg = <0 0x18000000 0 0x00800000>;
			no-map;
		};
	};

	wdt@2a490000 {
		compatible = "arm,sp805", "arm,primecell";
		reg = <0 0x2a490000 0 0x1000>;
		interrupts = <0 98 4>;
		clocks = <&oscclk6a>, <&oscclk6a>;
		clock-names = "wdog_clk", "apb_pclk";
	};

	hdlcd@2b000000 {
		compatible = "arm,hdlcd";
		reg = <0 0x2b000000 0 0x1000>;
		interrupts = <0 85 4>;
		clocks = <&hdlcd_clk>;
		clock-names = "pxlclk";
	};

	memory-controller@2b0a0000 {
		compatible = "arm,pl341", "arm,primecell";
		reg = <0 0x2b0a0000 0 0x1000>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
	};

	gic: interrupt-controller@2c001000 {
		compatible = "arm,cortex-a15-gic", "arm,cortex-a9-gic";
		#interrupt-cells = <3>;
		#address-cells = <0>;
		interrupt-controller;
		reg = <0 0x2c001000 0 0x1000>,
		      <0 0x2c002000 0 0x2000>,
		      <0 0x2c004000 0 0x2000>,
		      <0 0x2c006000 0 0x2000>;
		interrupts = <1 9 0xf04>;
	};

	cci@2c090000 {
		compatible = "arm,cci-400";
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0 0x2c090000 0 0x1000>;
		ranges = <0x0 0x0 0x2c090000 0x10000>;

		cci_control1: slave-if@4000 {
			compatible = "arm,cci-400-ctrl-if";
			interface-type = "ace";
			reg = <0x4000 0x1000>;
		};

		cci_control2: slave-if@5000 {
			compatible = "arm,cci-400-ctrl-if";
			interface-type = "ace";
			reg = <0x5000 0x1000>;
		};

		pmu@9000 {
			 compatible = "arm,cci-400-pmu,r0";
			 reg = <0x9000 0x5000>;
			 interrupts = <0 105 4>,
				      <0 101 4>,
				      <0 102 4>,
				      <0 103 4>,
				      <0 104 4>;
		};
	};

	memory-controller@7ffd0000 {
		compatible = "arm,pl354", "arm,primecell";
		reg = <0 0x7ffd0000 0 0x1000>;
		interrupts = <0 86 4>,
			     <0 87 4>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
	};

	dma@7ff00000 {
		compatible = "arm,pl330", "arm,primecell";
		reg = <0 0x7ff00000 0 0x1000>;
		interrupts = <0 92 4>,
			     <0 88 4>,
			     <0 89 4>,
			     <0 90 4>,
			     <0 91 4>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
	};

        scc@7fff0000 {
		compatible = "arm,vexpress-scc,v2p-ca15_a7", "arm,vexpress-scc";
		reg = <0 0x7fff0000 0 0x1000>;
		interrupts = <0 95 4>;
        };

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <1 13 0xf08>,
			     <1 14 0xf08>,
			     <1 11 0xf08>,
			     <1 10 0xf08>;
	};

	pmu-a15 {
		compatible = "arm,cortex-a15-pmu";
		interrupts = <0 68 4>,
			     <0 69 4>;
		interrupt-affinity = <&cpu0>,
				     <&cpu1>;
	};

	pmu-a7 {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <0 128 4>,
			     <0 129 4>,
			     <0 130 4>;
		interrupt-affinity = <&cpu2>,
				     <&cpu3>,
				     <&cpu4>;
	};

	oscclk6a: oscclk6a {
		/* Reference 24MHz clock */
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <24000000>;
		clock-output-names = "oscclk6a";
	};

	dcc {
		compatible = "arm,vexpress,config-bus";
		arm,vexpress,config-bridge = <&v2m_sysreg>;

		clock-controller-0 {
			/* A15 PLL 0 reference clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 0>;
			freq-range = <17000000 50000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk0";
		};

		clock-controller-1 {
			/* A15 PLL 1 reference clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 1>;
			freq-range = <17000000 50000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk1";
		};

		clock-controller-2 {
			/* A7 PLL 0 reference clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 2>;
			freq-range = <17000000 50000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk2";
		};

		clock-controller-3 {
			/* A7 PLL 1 reference clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 3>;
			freq-range = <17000000 50000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk3";
		};

		clock-controller-4 {
			/* External AXI master clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 4>;
			freq-range = <20000000 40000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk4";
		};

		hdlcd_clk: clock-controller-5 {
			/* HDLCD PLL reference clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 5>;
			freq-range = <23750000 165000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk5";
		};

		smbclk: clock-controller-6 {
			/* Static memory controller clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 6>;
			freq-range = <20000000 40000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk6";
		};

		clock-controller-7 {
			/* SYS PLL reference clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 7>;
			freq-range = <17000000 50000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk7";
		};

		clock-controller-8 {
			/* DDR2 PLL reference clock */
			compatible = "arm,vexpress-osc";
			arm,vexpress-sysreg,func = <1 8>;
			freq-range = <20000000 50000000>;
			#clock-cells = <0>;
			clock-output-names = "oscclk8";
		};

		regulator-a15 {
			/* A15 CPU core voltage */
			compatible = "arm,vexpress-volt";
			arm,vexpress-sysreg,func = <2 0>;
			regulator-name = "A15 Vcore";
			regulator-min-microvolt = <800000>;
			regulator-max-microvolt = <1050000>;
			regulator-always-on;
			label = "A15 Vcore";
		};

		regulator-a7 {
			/* A7 CPU core voltage */
			compatible = "arm,vexpress-volt";
			arm,vexpress-sysreg,func = <2 1>;
			regulator-name = "A7 Vcore";
			regulator-min-microvolt = <800000>;
			regulator-max-microvolt = <1050000>;
			regulator-always-on;
			label = "A7 Vcore";
		};

		amp-a15 {
			/* Total current for the two A15 cores */
			compatible = "arm,vexpress-amp";
			arm,vexpress-sysreg,func = <3 0>;
			label = "A15 Icore";
		};

		amp-a7 {
			/* Total current for the three A7 cores */
			compatible = "arm,vexpress-amp";
			arm,vexpress-sysreg,func = <3 1>;
			label = "A7 Icore";
		};

		temp-dcc {
			/* DCC internal temperature */
			compatible = "arm,vexpress-temp";
			arm,vexpress-sysreg,func = <4 0>;
			label = "DCC";
		};

		power-a15 {
			/* Total power for the two A15 cores */
			compatible = "arm,vexpress-power";
			arm,vexpress-sysreg,func = <12 0>;
			label = "A15 Pcore";
		};

		power-a7 {
			/* Total power for the three A7 cores */
			compatible = "arm,vexpress-power";
			arm,vexpress-sysreg,func = <12 1>;
			label = "A7 Pcore";
		};

		energy-a15 {
			/* Total energy for the two A15 cores */
			compatible = "arm,vexpress-energy";
			arm,vexpress-sysreg,func = <13 0>, <13 1>;
			label = "A15 Jcore";
		};

		energy-a7 {
			/* Total energy for the three A7 cores */
			compatible = "arm,vexpress-energy";
			arm,vexpress-sysreg,func = <13 2>, <13 3>;
			label = "A7 Jcore";
		};
	};

	etb@20010000 {
		compatible = "arm,coresight-etb10", "arm,primecell";
		reg = <0 0x20010000 0 0x1000>;

		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		in-ports {
			port {
				etb_in_port: endpoint {
					remote-endpoint = <&replicator_out_port0>;
				};
			};
		};
	};

	tpiu@20030000 {
		compatible = "arm,coresight-tpiu", "arm,primecell";
		reg = <0 0x20030000 0 0x1000>;

		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		in-ports {
			port {
				tpiu_in_port: endpoint {
					remote-endpoint = <&replicator_out_port1>;
				};
			};
		};
	};

	replicator {
		/* non-configurable replicators don't show up on the
		 * AMBA bus.  As such no need to add "arm,primecell".
		 */
		compatible = "arm,coresight-static-replicator";

		out-ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				replicator_out_port0: endpoint {
					remote-endpoint = <&etb_in_port>;
				};
			};

			port@1 {
				reg = <1>;
				replicator_out_port1: endpoint {
					remote-endpoint = <&tpiu_in_port>;
				};
			};
		};

		in-ports {
			port {
				replicator_in_port0: endpoint {
					remote-endpoint = <&funnel_out_port0>;
				};
			};
		};
	};

	funnel@20040000 {
		compatible = "arm,coresight-dynamic-funnel", "arm,primecell";
		reg = <0 0x20040000 0 0x1000>;

		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		out-ports {
			port {
				funnel_out_port0: endpoint {
					remote-endpoint =
						<&replicator_in_port0>;
				};
			};
		};

		in-ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				funnel_in_port0: endpoint {
					remote-endpoint = <&ptm0_out_port>;
				};
			};

			port@1 {
				reg = <1>;
				funnel_in_port1: endpoint {
					remote-endpoint = <&ptm1_out_port>;
				};
			};

			port@2 {
				reg = <2>;
				funnel_in_port2: endpoint {
					remote-endpoint = <&etm0_out_port>;
				};
			};

			/* Input port #3 is for ITM, not supported here */

			port@4 {
				reg = <4>;
				funnel_in_port4: endpoint {
					remote-endpoint = <&etm1_out_port>;
				};
			};

			port@5 {
				reg = <5>;
				funnel_in_port5: endpoint {
					remote-endpoint = <&etm2_out_port>;
				};
			};
		};
	};

	ptm@2201c000 {
		compatible = "arm,coresight-etm3x", "arm,primecell";
		reg = <0 0x2201c000 0 0x1000>;

		cpu = <&cpu0>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		out-ports {
			port {
				ptm0_out_port: endpoint {
					remote-endpoint = <&funnel_in_port0>;
				};
			};
		};
	};

	ptm@2201d000 {
		compatible = "arm,coresight-etm3x", "arm,primecell";
		reg = <0 0x2201d000 0 0x1000>;

		cpu = <&cpu1>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		out-ports {
			port {
				ptm1_out_port: endpoint {
					remote-endpoint = <&funnel_in_port1>;
				};
			};
		};
	};

	etm@2203c000 {
		compatible = "arm,coresight-etm3x", "arm,primecell";
		reg = <0 0x2203c000 0 0x1000>;

		cpu = <&cpu2>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		out-ports {
			port {
				etm0_out_port: endpoint {
					remote-endpoint = <&funnel_in_port2>;
				};
			};
		};
	};

	etm@2203d000 {
		compatible = "arm,coresight-etm3x", "arm,primecell";
		reg = <0 0x2203d000 0 0x1000>;

		cpu = <&cpu3>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		out-ports {
			port {
				etm1_out_port: endpoint {
					remote-endpoint = <&funnel_in_port4>;
				};
			};
		};
	};

	etm@2203e000 {
		compatible = "arm,coresight-etm3x", "arm,primecell";
		reg = <0 0x2203e000 0 0x1000>;

		cpu = <&cpu4>;
		clocks = <&oscclk6a>;
		clock-names = "apb_pclk";
		out-ports {
			port {
				etm2_out_port: endpoint {
					remote-endpoint = <&funnel_in_port5>;
				};
			};
		};
	};

	smb: bus@8000000 {
		ranges = <0x8000000 0 0x8000000 0x18000000>;
	};

	site2: hsb@40000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0 0x40000000 0x3fef0000>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 3>;
		interrupt-map = <0 0 &gic 0 36 4>,
				<0 1 &gic 0 37 4>,
				<0 2 &gic 0 38 4>,
				<0 3 &gic 0 39 4>;
	};
};

&nor_flash {
	/*
	 * Unfortunately, accessing the flash disturbs the CPU idle states
	 * (suspend) and CPU hotplug of this platform. For this reason, flash
	 * hardware access is disabled by default on this platform alone.
	 */
	status = "disabled";
};
