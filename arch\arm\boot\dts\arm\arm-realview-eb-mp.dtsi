/*
 * Copyright 2016 Linaro Ltd
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>
#include "arm-realview-eb.dtsi"

/*
 * This is the common include file for all MPCore variants of the
 * Evaluation Baseboard, i.e. ARM11MPCore, ARM11MPCore Revision B
 * and Cortex-A9 MPCore.
 */
/ {
	soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "arm,realview-eb-soc", "simple-bus";
		regmap = <&syscon>;
		ranges;

		/* Primary interrupt controller in the test chip */
		intc: interrupt-controller@1f000100 {
			compatible = "arm,eb11mp-gic";
			#interrupt-cells = <3>;
			#address-cells = <1>;
			interrupt-controller;
			reg = <0x1f001000 0x1000>,
			      <0x1f000100 0x100>;
		};

		/* Secondary interrupt controller on the FPGA */
		intc_second: interrupt-controller@10040000 {
			compatible = "arm,pl390";
			#interrupt-cells = <3>;
			#address-cells = <1>;
			interrupt-controller;
			reg = <0x10041000 0x1000>,
			      <0x10040000 0x100>;
			interrupt-parent = <&intc>;
			interrupts = <0 10 IRQ_TYPE_LEVEL_HIGH>;
		};

		L2: cache-controller {
			compatible = "arm,l220-cache";
			reg = <0x1f002000 0x1000>;
			interrupt-parent = <&intc>;
			interrupts = <0 29 IRQ_TYPE_LEVEL_HIGH>,
			     <0 30 IRQ_TYPE_LEVEL_HIGH>,
			     <0 31 IRQ_TYPE_LEVEL_HIGH>;
			cache-unified;
			cache-level = <2>;
			/*
			 * Override default cache size, sets and
			 * associativity as these may be erroneously set
			 * up by boot loader(s), probably for safety
			 * since th outer sync operation can cause the
			 * cache to hang unless disabled.
			 */
			cache-size = <1048576>; // 1MB
			cache-sets = <4096>;
			cache-line-size = <32>;
			arm,shared-override;
			arm,parity-enable;
			arm,outer-sync-disable;
		};

		scu: scu@1f000000 {
			compatible = "arm,arm11mp-scu";
			reg = <0x1f000000 0x100>;
		};

		twd_timer: timer@1f000600 {
			compatible = "arm,arm11mp-twd-timer";
			reg = <0x1f000600 0x20>;
			interrupt-parent = <&intc>;
			interrupts = <1 13 0xf04>;
		};

		twd_wdog: watchdog@1f000620 {
			compatible = "arm,arm11mp-twd-wdt";
			reg = <0x1f000620 0x20>;
			interrupt-parent = <&intc>;
			interrupts = <1 14 0xf04>;
		};

		/* PMU with one IRQ line per core */
		pmu: pmu {
			compatible = "arm,arm11mpcore-pmu";
			interrupt-parent = <&intc>;
			interrupts = <0 17 IRQ_TYPE_LEVEL_HIGH>,
			     <0 18 IRQ_TYPE_LEVEL_HIGH>,
			     <0 19 IRQ_TYPE_LEVEL_HIGH>,
			     <0 20 IRQ_TYPE_LEVEL_HIGH>;
		};
	};
};

/*
 * This adapts all the peripherals to the interrupt routing
 * to the GIC on the core tile.
 */

&ethernet {
	interrupt-parent = <&intc>;
	interrupts = <0 9 IRQ_TYPE_LEVEL_HIGH>;
};

&usb {
	interrupt-parent = <&intc>;
	interrupts = <0 3 IRQ_TYPE_LEVEL_HIGH>;
};

&aaci {
	interrupt-parent = <&intc>;
	interrupts = <0 0 IRQ_TYPE_LEVEL_HIGH>;
};

&mmc {
	interrupt-parent = <&intc>;
	interrupts = <0 14 IRQ_TYPE_LEVEL_HIGH>,
			<0 15 IRQ_TYPE_LEVEL_HIGH>;
};

&kmi0 {
	interrupt-parent = <&intc>;
	interrupts = <0 7 IRQ_TYPE_LEVEL_HIGH>;
};

&kmi1 {
	interrupt-parent = <&intc>;
	interrupts = <0 8 IRQ_TYPE_LEVEL_HIGH>;
};

&serial0 {
	interrupt-parent = <&intc>;
	interrupts = <0 4 IRQ_TYPE_LEVEL_HIGH>;
};

&serial1 {
	interrupt-parent = <&intc>;
	interrupts = <0 5 IRQ_TYPE_LEVEL_HIGH>;
};

&timer01 {
	interrupt-parent = <&intc>;
	interrupts = <0 1 IRQ_TYPE_LEVEL_HIGH>;
};

&timer23 {
	interrupt-parent = <&intc>;
	interrupts = <0 2 IRQ_TYPE_LEVEL_HIGH>;
};

&rtc {
	interrupt-parent = <&intc>;
	interrupts = <0 6 IRQ_TYPE_LEVEL_HIGH>;
};

/*
 * On revision A, these peripherals does not have their IRQ lines
 * routed to the core tile, but they can be reached on the secondary
 * GIC.
 */
&gpio0 {
	interrupt-parent = <&intc_second>;
	interrupts = <0 6 IRQ_TYPE_LEVEL_HIGH>;
};

&gpio1 {
	interrupt-parent = <&intc_second>;
	interrupts = <0 7 IRQ_TYPE_LEVEL_HIGH>;
};

&gpio2 {
	interrupt-parent = <&intc_second>;
	interrupts = <0 8 IRQ_TYPE_LEVEL_HIGH>;
};

&serial2 {
	interrupt-parent = <&intc_second>;
	interrupts = <0 14 IRQ_TYPE_LEVEL_HIGH>;
	status = "okay";
};

&serial3 {
	interrupt-parent = <&intc_second>;
	interrupts = <0 15 IRQ_TYPE_LEVEL_HIGH>;
	status = "okay";
};

&ssp {
	interrupt-parent = <&intc_second>;
	interrupts = <0 11 IRQ_TYPE_LEVEL_HIGH>;
	status = "okay";
};

&wdog {
	interrupt-parent = <&intc_second>;
	interrupts = <0 0 IRQ_TYPE_LEVEL_HIGH>;
	status = "okay";
};
