// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/leds/common.h>

/ {
	model = "Asrock Rack X570D4U BMC";
	compatible = "asrock,x570d4u-bmc", "aspeed,ast2500";

	aliases {
		i2c40 = &i2c4mux0ch0;
		i2c41 = &i2c4mux0ch1;
		i2c42 = &i2c4mux0ch2;
		i2c43 = &i2c4mux0ch3;
	};

	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		pci_memory: region@9a000000 {
			no-map;
			reg = <0x9a000000 0x00010000>; /* 64K */
		};

		video_engine_memory: jpegbuffer {
			size = <0x02800000>;	/* 40M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			/* led-heartbeat-n */
			gpios = <&gpio ASPEED_GPIO(H, 6) GPIO_ACTIVE_LOW>;
			color = <LED_COLOR_ID_GREEN>;
			function = LED_FUNCTION_HEARTBEAT;
			linux,default-trigger = "timer";
		};

		led-1 {
			/* led-fault-n */
			gpios = <&gpio ASPEED_GPIO(Z, 2) GPIO_ACTIVE_LOW>;
			color = <LED_COLOR_ID_AMBER>;
			function = LED_FUNCTION_FAULT;
			panic-indicator;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>, <&adc 4>,
			<&adc 5>, <&adc 6>, <&adc 7>, <&adc 8>, <&adc 9>,
			<&adc 10>, <&adc 11>, <&adc 12>;
	};
};

&gpio {
	status = "okay";
	gpio-line-names =
	/*  A */ "input-locatorled-n", "", "", "", "", "", "", "",
	/*  B */ "input-bios-post-cmplt-n", "", "", "", "", "", "", "",
	/*  C */ "", "", "", "", "", "", "control-locatorbutton-n", "",
	/*  D */ "button-power-n", "control-power-n", "button-reset-n",
		 "control-reset-n", "", "", "", "",
	/*  E */ "", "", "", "", "", "", "", "",
	/*  F */ "", "", "", "", "", "", "", "",
	/*  G */ "output-hwm-vbat-enable", "input-id0-n", "input-id1-n",
		 "input-id2-n", "input-aux-smb-alert-n", "",
		 "input-psu-smb-alert-n", "",
	/*  H */ "", "", "", "", "input-mfg-mode-n", "",
		 "led-heartbeat-n", "input-case-open-n",
	/*  I */ "", "", "", "", "", "", "", "",
	/*  J */ "output-bmc-ready-n", "", "", "", "", "", "", "",
	/*  K */ "", "", "", "", "", "", "", "",
	/*  L */ "", "", "", "", "", "", "", "",
	/*  M */ "", "", "", "", "", "", "", "",
	/*  N */ "", "", "", "", "", "", "", "",
	/*  O */ "", "", "", "", "", "", "", "",
	/*  P */ "", "", "", "", "", "", "", "",
	/*  Q */ "", "", "", "", "input-bmc-smb-present-n", "", "",
		 "input-pcie-wake-n",
	/*  R */ "", "", "", "", "", "", "", "",
	/*  S */ "input-bmc-pchhot-n", "", "", "", "", "", "", "",
	/*  T */ "", "", "", "", "", "", "", "",
	/*  U */ "", "", "", "", "", "", "", "",
	/*  V */ "", "", "", "", "", "", "", "",
	/*  W */ "", "", "", "", "", "", "", "",
	/*  X */ "", "", "", "", "", "", "", "",
	/*  Y */ "input-sleep-s3-n", "input-sleep-s5-n", "", "", "", "",
		 "", "",
	/*  Z */ "", "", "led-fault-n", "output-bmc-throttle-n", "", "",
		 "", "",
	/* AA */ "input-cpu1-thermtrip-latch-n", "",
		 "input-cpu1-prochot-n", "", "", "", "", "",
	/* AB */ "", "input-power-good", "", "", "", "", "", "",
	/* AC */ "", "", "", "", "", "", "", "";
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		label = "bmc";
		m25p,fast-read;
		spi-max-frequency = <10000000>;
#include "openbmc-flash-layout-64.dtsi"
	};
};

&uart5 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;

	nvmem-cells = <&eth0_macaddress>;
	nvmem-cell-names = "mac-address";
};

&mac1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii2_default &pinctrl_mdio2_default>;
	use-ncsi;

	nvmem-cells = <&eth1_macaddress>;
	nvmem-cell-names = "mac-address";
};

&i2c0 {
	/* SMBus on auxiliary panel header (AUX_PANEL1) */
	status = "okay";
};

&i2c1 {
	/* Hardware monitoring SMBus */
	status = "okay";

	w83773g@4c {
		compatible = "nuvoton,w83773g";
		reg = <0x4c>;
	};
};

&i2c2 {
	/* PSU SMBus (PSU_SMB1) */
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9545";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;

		i2c4mux0ch0: i2c@0 {
			/* SMBus on PCI express 16x slot */
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c4mux0ch1: i2c@1 {
			/* SMBus on PCI express 8x slot */
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c4mux0ch2: i2c@2 {
			/* Unknown */
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c4mux0ch3: i2c@3 {
			/* SMBus on PCI express 1x slot */
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c5 {
	/* SMBus on BMC connector (BMC_SMB_1) */
	status = "okay";
};

&i2c7 {
	/* FRU and SPD EEPROM SMBus */
	status = "okay";

	eeprom@57 {
		compatible = "st,24c128", "atmel,24c128";
		reg = <0x57>;
		pagesize = <16>;
		#address-cells = <1>;
		#size-cells = <1>;

		eth0_macaddress: macaddress@3f80 {
			reg = <0x3f80 6>;
		};

		eth1_macaddress: macaddress@3f88 {
			reg = <0x3f88 6>;
		};
	};
};

&i2c8 {
	/* SMBus on intelligent platform management bus header (IPMB_1) */
	status = "okay";
};

&gfx {
	status = "okay";
};

&vhub {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};

&kcs3 {
	aspeed,lpc-io-reg = <0xca2>;
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&p2a {
	status = "okay";
	memory-region = <&pci_memory>;
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default
				&pinctrl_pwm1_default
				&pinctrl_pwm2_default
				&pinctrl_pwm3_default
				&pinctrl_pwm4_default
				&pinctrl_pwm5_default>;

	fan@0 {
		/* FAN1 (4-pin) */
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		/* FAN2 (4-pin) */
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		/* FAN3 (4-pin) */
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		/* FAN4 (6-pin) */
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04 0x0b>;
	};

	fan@4 {
		/* FAN6 (6-pin) */
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06 0x0d>;
	};

	fan@5 {
		/* FAN5 (6-pin) */
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05 0x0c>;
	};
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default       /* 3VSB */
			&pinctrl_adc1_default    /* 5VSB */
			&pinctrl_adc2_default    /* VCPU */
			&pinctrl_adc3_default    /* VSOC */
			&pinctrl_adc4_default    /* VCCM */
			&pinctrl_adc5_default    /* APU-VDDP */
			&pinctrl_adc6_default    /* PM-VDD-CLDO */
			&pinctrl_adc7_default    /* PM-VDDCR-S5 */
			&pinctrl_adc8_default    /* PM-VDDCR */
			&pinctrl_adc9_default    /* VBAT */
			&pinctrl_adc10_default   /* 3V */
			&pinctrl_adc11_default   /* 5V */
			&pinctrl_adc12_default>; /* 12V */
};
