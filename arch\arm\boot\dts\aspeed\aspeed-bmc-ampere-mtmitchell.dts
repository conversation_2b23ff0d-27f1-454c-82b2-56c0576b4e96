// SPDX-License-Identifier: GPL-2.0-only
// Copyright (c) 2022, Ampere Computing LLC

/dts-v1/;

#include "aspeed-g6.dtsi"
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Ampere Mt.Mitchell BMC";
	compatible = "ampere,mtmitchell-bmc", "aspeed,ast2600";

	aliases {
		serial7 = &uart8;
		serial8 = &uart9;

		/*
		 * I2C temperature alias port
		 */
		i2c20 = &i2c4_bus70_chn0;
		i2c21 = &i2c4_bus70_chn1;
		i2c22 = &i2c4_bus70_chn2;
		i2c23 = &i2c4_bus70_chn3;

		/*
		 *  i2c bus 30-31 assigned to OCP slot 0-1
		 */
		i2c30 = &ocpslot_0;
		i2c31 = &ocpslot_1;

		/*
		 *  i2c bus 32-33 assigned to Riser slot 0-1
		 */
		i2c32 = &i2c_riser0;
		i2c33 = &i2c_riser1;

		/*
		 *  i2c bus 38-39 assigned to FRU on Riser slot 0-1
		 */
		i2c38 = &i2c_riser0_chn_0;
		i2c39 = &i2c_riser1_chn_0;

		/*
		 *  I2C NVMe alias port
		 */
		i2c100 = &backplane_0;
		i2c48 = &nvmeslot_0;
		i2c49 = &nvmeslot_1;
		i2c50 = &nvmeslot_2;
		i2c51 = &nvmeslot_3;
		i2c52 = &nvmeslot_4;
		i2c53 = &nvmeslot_5;
		i2c54 = &nvmeslot_6;
		i2c55 = &nvmeslot_7;

		i2c101 = &backplane_1;
		i2c56 = &nvmeslot_8;
		i2c57 = &nvmeslot_9;
		i2c58 = &nvmeslot_10;
		i2c59 = &nvmeslot_11;
		i2c60 = &nvmeslot_12;
		i2c61 = &nvmeslot_13;
		i2c62 = &nvmeslot_14;
		i2c63 = &nvmeslot_15;

		i2c102 = &backplane_2;
		i2c64 = &nvmeslot_16;
		i2c65 = &nvmeslot_17;
		i2c66 = &nvmeslot_18;
		i2c67 = &nvmeslot_19;
		i2c68 = &nvmeslot_20;
		i2c69 = &nvmeslot_21;
		i2c70 = &nvmeslot_22;
		i2c71 = &nvmeslot_23;

		i2c80 = &nvme_m2_0;
		i2c81 = &nvme_m2_1;
	};

	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gfx_memory: framebuffer {
			size = <0x01000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		video_engine_memory: video {
			size = <0x04000000>;
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};

		vga_memory: region@bf000000 {
			no-map;
			compatible = "shared-dma-pool";
			reg = <0xbf000000 0x01000000>;  /* 16M */
		};
	};

	leds {
		compatible = "gpio-leds";
		/*
		 * Use gpio-leds to configure GPIOW5 (bmc-ready) pin to be reseted when
		 * watchdog timeout.
		 */
		led-bmc-ready {
			gpios = <&gpio0 ASPEED_GPIO(W, 5) (GPIO_ACTIVE_HIGH | GPIO_TRANSITORY)>;
		};

		led-sw-heartbeat {
			gpios = <&gpio0 ASPEED_GPIO(N, 3) GPIO_ACTIVE_HIGH>;
		};

		led-identify {
			gpios = <&gpio0 ASPEED_GPIO(S, 3) GPIO_ACTIVE_HIGH>;
		};

		led-fault {
			gpios = <&gpio0 ASPEED_GPIO(P, 4) GPIO_ACTIVE_HIGH>;
		};

		led-fan-fault {
			gpios = <&gpio_expander1 0 GPIO_ACTIVE_HIGH>;
		};

		led-psu-fault {
			gpios = <&gpio_expander1 1 GPIO_ACTIVE_HIGH>;
		};
	};

	voltage_mon_reg: voltage-mon-regulator {
		compatible = "regulator-fixed";
		regulator-name = "ltc2497_reg";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	gpioI5mux: mux-controller {
		compatible = "gpio-mux";
		#mux-control-cells = <0>;
		mux-gpios = <&gpio0 ASPEED_GPIO(I, 5) GPIO_ACTIVE_HIGH>;
	};

	adc0mux: adc0mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 0>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc1mux: adc1mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 1>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc2mux: adc2mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 2>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc3mux: adc3mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 3>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc4mux: adc4mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 4>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc5mux: adc5mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 5>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc6mux: adc6mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 6>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc7mux: adc7mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 7>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc8mux: adc8mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 8>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc9mux: adc9mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 9>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc10mux: adc10mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 10>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc11mux: adc11mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 11>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc12mux: adc12mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 12>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc13mux: adc13mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 13>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc14mux: adc14mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 14>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	adc15mux: adc15mux {
		compatible = "io-channel-mux";
		io-channels = <&adc_i2c_0 15>;
		#io-channel-cells = <1>;
		io-channel-names = "parent";
		mux-controls = <&gpioI5mux>;
		settle-time-us = <10000>;
		channels = "s0", "s1";
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels =	<&adc0mux 0>, <&adc0mux 1>,
				<&adc1mux 0>, <&adc1mux 1>,
				<&adc2mux 0>, <&adc2mux 1>,
				<&adc3mux 0>, <&adc3mux 1>,
				<&adc4mux 0>, <&adc4mux 1>,
				<&adc5mux 0>, <&adc5mux 1>,
				<&adc6mux 0>, <&adc6mux 1>,
				<&adc7mux 0>, <&adc7mux 1>,
				<&adc8mux 0>, <&adc8mux 1>,
				<&adc9mux 0>, <&adc9mux 1>,
				<&adc10mux 0>, <&adc10mux 1>,
				<&adc11mux 0>, <&adc11mux 1>,
				<&adc12mux 0>, <&adc12mux 1>,
				<&adc13mux 0>, <&adc13mux 1>,
				<&adc14mux 0>, <&adc14mux 1>,
				<&adc15mux 0>, <&adc15mux 1>,
				<&adc_i2c_1 0>, <&adc_i2c_1 1>,
				<&adc_i2c_1 2>, <&adc_i2c_1 3>,
				<&adc_i2c_1 4>, <&adc_i2c_1 5>,
				<&adc_i2c_1 6>, <&adc_i2c_1 7>,
				<&adc_i2c_1 8>, <&adc_i2c_1 9>,
				<&adc_i2c_1 10>, <&adc_i2c_1 11>,
				<&adc_i2c_1 12>, <&adc_i2c_1 13>,
				<&adc_i2c_1 14>, <&adc_i2c_1 15>,
				<&adc0 0>, <&adc0 1>,
				<&adc0 2>;
	};
};

&mdio0 {
	status = "okay";

	ethphy0: ethernet-phy@0 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

&mac0 {
	status = "okay";

	phy-mode = "rgmii";
	phy-handle = <&ethphy0>;

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default>;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-64.dtsi"
	};

	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-64-alt.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
		spi-max-frequency = <20000000>;
	};
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&uart8 {
	status = "okay";
};

&uart9 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	temperature-sensor@2e {
		compatible = "adi,adt7490";
		reg = <0x2e>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	psu@58 {
		compatible = "pmbus";
		reg = <0x58>;
	};

	psu@59 {
		compatible = "pmbus";
		reg = <0x59>;
	};
};

&i2c3 {
	status = "okay";
	bus-frequency = <1000000>;
	multi-master;
	mctp-controller;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};
};

&i2c4 {
	status = "okay";

	adc_i2c_0: adc@14 {
		compatible = "lltc,ltc2497";
		reg = <0x14>;
		vref-supply = <&voltage_mon_reg>;
		#io-channel-cells = <1>;
	 };

	adc_i2c_1: adc@16 {
		compatible = "lltc,ltc2497";
		reg = <0x16>;
		vref-supply = <&voltage_mon_reg>;
		#io-channel-cells = <1>;
	 };

	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
		pagesize = <32>;
	};

	i2c-mux@70 {
		compatible = "nxp,pca9545";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		i2c4_bus70_chn0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;

			outlet_temp1: temperature-sensor@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};
			psu1_inlet_temp2: temperature-sensor@49 {
				compatible = "ti,tmp75";
				reg = <0x49>;
			};
		};

		i2c4_bus70_chn1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x1>;

			pcie_zone_temp1: temperature-sensor@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};
			psu0_inlet_temp2: temperature-sensor@49 {
				compatible = "ti,tmp75";
				reg = <0x49>;
			};
		};

		i2c4_bus70_chn2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;

			pcie_zone_temp2: temperature-sensor@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};
			outlet_temp2: temperature-sensor@49 {
				compatible = "ti,tmp75";
				reg = <0x49>;
			};
		};

		i2c4_bus70_chn3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;

			mb_inlet_temp1: temperature-sensor@7c {
				compatible = "microchip,emc1413";
				reg = <0x7c>;
			};
			mb_inlet_temp2: temperature-sensor@4c {
				compatible = "microchip,emc1413";
				reg = <0x4c>;
			};
		};
	};
};

&i2c5 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		ocpslot_0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;

			ocpslot_0_temp: temperature-sensor@1f {
				compatible = "ti,tmp421";
				reg = <0x1f>;
			};
		};

		ocpslot_1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x1>;

			ocpslot_1_temp: temperature-sensor@1f {
				compatible = "ti,tmp421";
				reg = <0x1f>;
			};
		};

		i2c_riser0: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;

			i2c-mux@72 {
				compatible = "nxp,pca9546";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x72>;
				i2c-mux-idle-disconnect;

				i2c_riser0_chn_0: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x0>;

					eeprom@50 {
						compatible = "atmel,24c02";
						reg = <0x50>;
						pagesize = <16>;
					};
				};
			};
		};

		i2c_riser1: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;

			i2c-mux@72 {
				compatible = "nxp,pca9546";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x72>;
				i2c-mux-idle-disconnect;

				i2c_riser1_chn_0: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x0>;

					eeprom@50 {
						compatible = "atmel,24c02";
						reg = <0x50>;
						pagesize = <16>;
					};
				};
			};
		};
	};
};

&i2c6 {
	status = "okay";
	rtc@51 {
		compatible = "nxp,pcf85063a";
		reg = <0x51>;
	};
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";

	temperature-sensor@48 {
		compatible = "ti,tmp112";
		reg = <0x48>;
	};

	gpio@77 {
		compatible = "nxp,pca9539";
		reg = <0x77>;
		gpio-controller;
		#address-cells = <1>;
		#size-cells = <0>;
		#gpio-cells = <2>;

		gpio-line-names =
		"ext-vref-sel","","presence-hdd-bp5-n","presence-hdd-bp6-n",
		"","bmc-riser-en-n","bmc-ocp1-en-n","bmc-ocp0-en-n",
		"","","","",
		"","","","";

		bmc-ocp0-en-hog {
			gpio-hog;
			gpios = <7 GPIO_ACTIVE_LOW>;
			output-high;
			line-name = "bmc-ocp0-en-n";
		};
	};

	fan-controller0@20 {
		compatible = "maxim,max31790";
		reg = <0x20>;
	};

	fan-controller1@2f {
		compatible = "maxim,max31790";
		reg = <0x2f>;
	};
};

&i2c9 {
	status = "okay";
	i2c-mux@70 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x70>;
		i2c-mux-idle-disconnect;

		backplane_1: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
				pagesize = <32>;
			};

			i2c-mux@71 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x71>;
				i2c-mux-idle-disconnect;

				nvmeslot_8: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x0>;
				};
				nvmeslot_9: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x1>;
				};
				nvmeslot_10: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x2>;
				};
				nvmeslot_11: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x3>;
				};
				nvmeslot_12: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x4>;
				};
				nvmeslot_13: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x5>;
				};
				nvmeslot_14: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x6>;
				};
				nvmeslot_15: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x7>;
				};
			};

			tmp432@4c {
				compatible = "ti,tmp75";
				reg = <0x4c>;
			};
		};

		backplane_2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
				pagesize = <32>;
			};

			i2c-mux@71 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x71>;
				i2c-mux-idle-disconnect;

				nvmeslot_16: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x0>;
				};
				nvmeslot_17: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x1>;
				};
				nvmeslot_18: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x2>;
				};
				nvmeslot_19: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x3>;
				};
				nvmeslot_20: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x4>;
				};
				nvmeslot_21: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x5>;
				};
				nvmeslot_22: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x6>;
				};
				nvmeslot_23: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x7>;
				};
			};

			tmp432@4c {
				compatible = "ti,tmp75";
				reg = <0x4c>;
			};
		};

		backplane_0: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x4>;

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
				pagesize = <32>;
			};

			i2c-mux@71 {
				compatible = "nxp,pca9548";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x71>;
				i2c-mux-idle-disconnect;

				nvmeslot_0: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x0>;
				};
				nvmeslot_1: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x1>;
				};
				nvmeslot_2: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x2>;
				};
				nvmeslot_3: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x3>;
				};
				nvmeslot_4: i2c@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x4>;
				};
				nvmeslot_5: i2c@5 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x5>;
				};
				nvmeslot_6: i2c@6 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x6>;
				};
				nvmeslot_7: i2c@7 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x7>;
				};
			};

			tmp432@4c {
				compatible = "ti,tmp75";
				reg = <0x4c>;
			};
		};

		i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x7>;

			i2c-mux@71 {
				compatible = "nxp,pca9546";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x71>;
				i2c-mux-idle-disconnect;

				nvme_m2_0: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x0>;
				};

				nvme_m2_1: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0x1>;
				};
			};
		};
	};
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
	ssif-bmc@10 {
		compatible = "ssif-bmc";
		reg = <0x10>;
	};
};

&i2c14 {
	status = "okay";
	eeprom@50 {
		compatible = "atmel,24c64";
		reg = <0x50>;
		pagesize = <32>;
	};

	bmc_ast2600_cpu: temperature-sensor@35 {
		compatible = "ti,tmp175";
		reg = <0x35>;
	};
};

&i2c15 {
	status = "okay";
	gpio_expander1: gpio-expander@22 {
		compatible = "nxp,pca9535";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names =
			"fan-fault","psu-fault",
			"","",
			"","",
			"gpi0","gpi1",
			"","",
			"","",
			"","",
			"","";
	};
};

&adc0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default &pinctrl_adc1_default
		&pinctrl_adc2_default>;
};

&vhub {
	status = "okay";
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&gpio0 {
	gpio-line-names =
	/*A0-A7*/	"","","","","","i2c2-reset-n","i2c6-reset-n","i2c4-reset-n",
	/*B0-B7*/	"","","","","host0-sysreset-n","host0-pmin-n","","",
	/*C0-C7*/	"s0-vrd-fault-n","s1-vrd-fault-n","bmc-debug-mode","",
			"irq-n","","vrd-sel","spd-sel",
	/*D0-D7*/	"presence-ps0","presence-ps1","hsc-12vmain-alt2-n","ext-high-temp-n",
			"","bmc-ncsi-txen","","",
	/*E0-E7*/	"","eth-phy-int-n","clk50m-bmc-ncsi","","","","","",
	/*F0-F7*/	"s0-pcp-oc-warn-n","s1-pcp-oc-warn-n","power-chassis-control",
			"cpu-bios-recover","s0-heartbeat","hs-csout-prochot",
			"s0-vr-hot-n","s1-vr-hot-n",
	/*G0-G7*/	"","","hsc-12vmain-alt1-n","","","","","",
	/*H0-H7*/	"jtag-program-sel","fpga-program-b","wd-disable-n",
			"power-chassis-good","","","","",
	/*I0-I7*/	"","","","","","adc-sw","power-button","rtc-battery-voltage-read-enable",
	/*J0-J7*/	"","","","","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"","","","","","","","",
	/*M0-M7*/	"","s0-ddr-save","soc-spi-nor-access","presence-cpu0",
			"s0-rtc-lock","","","",
	/*N0-N7*/	"hpm-fw-recovery","hpm-stby-rst-n","jtag-sel-s0","led-sw-hb",
			"jtag-dbgr-prsnt-n","s1-heartbeat","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"ps0-ac-loss-n","ps1-ac-loss-n","","",
			"led-fault","cpld-user-mode","jtag-srst-n","led-bmc-hb",
	/*Q0-Q7*/	"","","","","","","","",
	/*R0-R7*/	"","","","","","","","",
	/*S0-S7*/	"","","identify-button","led-identify",
			"s1-ddr-save","spi-nor-access","host0-ready","presence-cpu1",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"s0-hightemp-n","s0-fault-alert","s0-sys-auth-failure-n",
			"host0-reboot-ack-n","s0-fw-boot-ok","host0-shd-req-n",
			"host0-shd-ack-n","s0-overtemp-n",
	/*W0-W7*/	"ocp-aux-pwren","ocp-main-pwren","ocp-pgood","s1-pcp-pgood",
			"bmc-ok","bmc-ready","spi0-program-sel","spi0-backup-sel",
	/*X0-X7*/	"i2c-backup-sel","s1-fault-alert","s1-fw-boot-ok",
			"s1-hightemp-n","s0-spi-auth-fail-n","s1-sys-auth-failure-n",
			"s1-overtemp-n","cpld-s1-spi-auth-fail-n",
	/*Y0-Y7*/	"","","","","","","","host0-special-boot",
	/*Z0-Z7*/	"reset-button","ps0-pgood","ps1-pgood","","","","","";

	ocp-aux-pwren-hog {
		gpio-hog;
		gpios = <ASPEED_GPIO(W, 0) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "ocp-aux-pwren";
	};
};

&gpio1 {
	gpio-line-names =
	/*18A0-18A7*/	"","","","","","","","",
	/*18B0-18B7*/	"","","","","","","s0-soc-pgood","",
	/*18C0-18C7*/	"uart1-mode0","uart1-mode1","uart2-mode0","uart2-mode1",
			"uart3-mode0","uart3-mode1","uart4-mode0","uart4-mode1",
	/*18D0-18D7*/	"","","","","","","","",
	/*18E0-18E3*/	"","","","";
};
