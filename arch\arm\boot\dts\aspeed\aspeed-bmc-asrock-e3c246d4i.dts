// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/interrupt-controller/irq.h>

/{
	model = "ASRock E3C246D4I BMC";
	compatible = "asrock,e3c246d4i-bmc", "aspeed,ast2500";

	aliases {
		serial4 = &uart5;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=tty0 console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	leds {
		compatible = "gpio-leds";

		heartbeat {
			/* BMC_HB_LED_N */
			gpios = <&gpio ASPEED_GPIO(H, 6) GPIO_ACTIVE_LOW>;
			linux,default-trigger = "timer";
		};

		system-fault {
			/* SYSTEM_FAULT_LED_N */
			gpios = <&gpio ASPEED_GPIO(Z, 2) GPIO_ACTIVE_LOW>;
			panic-indicator;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		uid-button {
			label = "uid-button";
			gpios = <&gpio ASPEED_GPIO(F, 1) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(F, 1)>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>, <&adc 4>,
			<&adc 5>, <&adc 6>, <&adc 7>, <&adc 8>, <&adc 9>,
			<&adc 10>, <&adc 11>, <&adc 12>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <50000000>; /* 50 MHz */
#include "openbmc-flash-layout.dtsi"
	};
};

&uart5 {
	status = "okay";
};

&vuart {
	status = "okay";
	aspeed,lpc-io-reg = <0x2f8>;
	aspeed,lpc-interrupts = <3 IRQ_TYPE_LEVEL_HIGH>;
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;

	nvmem-cells = <&eth0_macaddress>;
	nvmem-cell-names = "mac-address";
};

&i2c1 {
	status = "okay";

	/* thermal sensor, one diode run to a disconnected header */
	w83773g@4c {
		compatible = "nuvoton,w83773g";
		reg = <0x4c>;
	};
};

&i2c3 {
	status = "okay";

	/* FRU EEPROM */
	eeprom@57 {
		compatible = "st,24c128", "atmel,24c128";
		reg = <0x57>;
		pagesize = <16>;
		#address-cells = <1>;
		#size-cells = <1>;

		eth0_macaddress: macaddress@3f80 {
			reg = <0x3f80 6>;
		};
	};
};

&video {
	status = "okay";
};

&vhub {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&gpio {
	status = "okay";
	gpio-line-names =
		/*  A */ "BMC_MAC1_INTB", "BMC_MAC2_INTB", "NMI_BTN_N", "BMC_NMI",
			"", "", "", "",
		/*  B */ "", "", "", "", "", "IRQ_BMC_PCH_SMI_LPC_N", "", "",
		/*  C */ "", "", "", "", "", "", "", "",
		/*  D */ "BMC_PSIN", "BMC_PSOUT", "BMC_RESETCON", "RESETCON",
			"", "", "", "",
		/*  E */ "", "", "", "", "", "", "", "",
		/*  F */ "LOCATORLED_STATUS_N", "LOCATORBTN", "", "",
			"", "", "BMC_PCH_SCI_LPC", "BMC_NCSI_MUX_CTL",
		/*  G */ "HWM_BAT_EN", "CHASSIS_ID0", "CHASSIS_ID1", "CHASSIS_ID2",
			"BMC_ALERT1_N_R", "BMC_ALERT2_N_R", "BMC_ALERT3_N", "SML0ALERT",
		/*  H */ "FM_ME_RCVR_N", "O_PWROK", "SKL_CNL_R", "D4_DIMM_EVENT_3V_N",
			"MFG_MODE_N", "BMC_RTCRST", "BMC_HB_LED_N", "BMC_CASEOPEN",
		/*  I */ "", "", "", "", "", "", "", "",
		/*  J */ "BMC_READY", "BMC_PCH_BIOS_CS_N", "BMC_SMI", "",
			"", "", "", "",
		/*  K */ "", "", "", "", "", "", "", "",
		/*  L */ "BMC_CTS1", "BMC_DCD1", "BMC_DSR1", "BMC_RI1",
			"BMC_DTR1", "BMC_RTS1", "BMC_TXD1", "BMC_RXD1",
		/*  M */ "BMC_LAN0_DIS_N", "BMC_LAN1_DIS_N", "", "",
			"", "", "", "",
		/*  N */ "", "", "", "", "", "", "", "",
		/*  O */ "", "", "", "", "", "", "", "",
		/*  P */ "", "", "", "", "", "", "", "",
		/*  Q */ "", "", "", "",
			"BMC_SBM_PRESENT_1_N", "BMC_SBM_PRESENT_2_N",
			"BMC_SBM_PRESENT_3_N", "BMC_PCIE_WAKE_N",
		/*  R */ "", "", "", "", "", "", "", "",
		/*  S */ "PCHHOT_BMC_N", "", "RSMRST",
			"", "", "", "", "",
		/*  T */ "", "", "", "", "", "", "", "",
		/*  U */ "", "", "", "", "", "", "", "",
		/*  V */ "", "", "", "", "", "", "", "",
		/*  W */ "PS_PWROK", /* dummy always-high signal */
			"", "", "", "", "", "", "",
		/*  X */ "", "", "", "", "", "", "", "",
		/*  Y */ "SLP_S3", "SLP_S5", "", "", "", "", "", "",
		/*  Z */ "CPU_CATERR_BMC_PCH_N", "", "SYSTEM_FAULT_LED_N", "BMC_THROTTLE_N",
			"", "", "", "",
		/* AA */ "CPU1_THERMTRIP_LATCH_N", "", "CPU1_PROCHOT_N", "",
			"", "", "IRQ_SMI_ACTIVE_N", "FM_BIOS_POST_CMPLT_N",
		/* AB */ "", "", "ME_OVERRIDE", "BMC_DMI_MODIFY",
			"", "", "", "",
		/* AC */ "LAD0", "LAD1", "LAD2", "LAD3",
			"CK_33M_BMC", "LFRAME", "SERIRQ", "S_PLTRST";

	/* Assert BMC_READY so BIOS doesn't sit around waiting for it */
	bmc-ready {
		gpio-hog;
		gpios = <ASPEED_GPIO(J, 0) GPIO_ACTIVE_LOW>;
		output-high;
	};
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
			&pinctrl_adc1_default
			&pinctrl_adc2_default
			&pinctrl_adc3_default
			&pinctrl_adc4_default
			&pinctrl_adc5_default
			&pinctrl_adc6_default
			&pinctrl_adc7_default
			&pinctrl_adc8_default
			&pinctrl_adc9_default
			&pinctrl_adc10_default
			&pinctrl_adc11_default
			&pinctrl_adc12_default>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&peci0 {
	status = "okay";
};
