// SPDX-License-Identifier: GPL-2.0
// Copyright (c) 2017 Facebook Inc.
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Portwell Neptune BMC";
	compatible = "portwell,neptune-bmc", "aspeed,ast2500";
	aliases {
		serial0 = &uart1;
		serial4 = &uart5;
	};
	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	leds {
		compatible = "gpio-leds";
		postcode0 {
			label = "BMC_UP";
			gpios = <&gpio ASPEED_GPIO(H, 0) GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
		postcode1 {
			label = "BMC_HB";
			gpios = <&gpio ASPEED_GPIO(H, 1) GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};
		postcode2 {
			label = "FAULT";
			gpios = <&gpio ASPEED_GPIO(H, 2) GPIO_ACTIVE_HIGH>;
		};
		// postcode3-7 are GPIOH3-H7
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
	};
};

&uart1 {
	// Host Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default>;
};

&uart5 {
	// BMC Console
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default
		     &pinctrl_mdio1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
};

&mac1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii2_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC2CLK>,
		 <&syscon ASPEED_CLK_MAC2RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&i2c1 {
	status = "okay";
	// To PCIe slot SMBUS
};

&i2c2 {
	status = "okay";
	// To LAN I210
};

&i2c3 {
	status = "okay";
	// SMBus to COMe AB
};

&i2c4 {
	status = "okay";
	// I2C to COMe AB
};

&i2c5 {
	status = "okay";
// 	USB Debug card
	pca9555@27 {
		compatible = "nxp,pca9555";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

&i2c6 {
	status = "okay";
	tpm@20 {
		compatible = "infineon,slb9645tt";
		reg = <0x20>;
	};
	tmp421@4e {
		compatible = "ti,tmp421";
		reg = <0x4e>;
	};
	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};
};

&i2c8 {
	status = "okay";
	eeprom@51 {
		compatible = "atmel,24c128";
		reg = <0x51>;
		pagesize = <32>;
	};
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default>;
	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};
};
