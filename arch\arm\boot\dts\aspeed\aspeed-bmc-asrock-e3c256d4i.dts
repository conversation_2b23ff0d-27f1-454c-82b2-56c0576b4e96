// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/watchdog/aspeed-wdt.h>

/{
	model = "ASRock E3C256D4I BMC";
	compatible = "asrock,e3c256d4i-bmc", "aspeed,ast2500";

	aliases {
		serial4 = &uart5;

		i2c20 = &i2c2mux0ch0;
		i2c21 = &i2c2mux0ch1;
		i2c22 = &i2c2mux0ch2;
		i2c23 = &i2c2mux0ch3;
	};

	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	leds {
		compatible = "gpio-leds";

		/* BMC heartbeat */
		led-0 {
			gpios = <&gpio ASPEED_GPIO(H, 6) GPIO_ACTIVE_LOW>;
			function = LED_FUNCTION_HEARTBEAT;
			color = <LED_COLOR_ID_GREEN>;
			linux,default-trigger = "timer";
		};

		/* system fault */
		led-1 {
			gpios = <&gpio ASPEED_GPIO(Z, 2) GPIO_ACTIVE_LOW>;
			function = LED_FUNCTION_FAULT;
			color = <LED_COLOR_ID_RED>;
			panic-indicator;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 12>, <&adc 13>, <&adc 14>, <&adc 15>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-max-frequency = <*********>; /* 100 MHz */
#include "openbmc-flash-layout-64.dtsi"
	};
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&uart_routing {
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;

	nvmem-cells = <&eth0_macaddress>;
	nvmem-cell-names = "mac-address";
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	i2c-mux@70 {
		compatible = "nxp,pca9545";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;

		i2c2mux0ch0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		i2c2mux0ch1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		i2c2mux0ch2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		i2c2mux0ch3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";

	vrm@60 {
		compatible = "isil,isl69269";
		reg = <0x60>;
	};
};

&i2c12 {
	status = "okay";

	/* FRU eeprom */
	eeprom@57 {
		compatible = "st,24c128", "atmel,24c128";
		reg = <0x57>;
		pagesize = <16>;
		#address-cells = <1>;
		#size-cells = <1>;

		eth0_macaddress: macaddress@3f80 {
			reg = <0x3f80 6>;
		};
	};
};

&video {
	status = "okay";
};

&vhub {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&kcs3 {
	status = "okay";
	aspeed,lpc-io-reg = <0xca2>;
};

&peci0 {
	status = "okay";
};

&wdt1 {
	aspeed,reset-mask = <(AST2500_WDT_RESET_DEFAULT & ~AST2500_WDT_RESET_LPC)>;
};

&wdt2 {
	aspeed,reset-mask = <(AST2500_WDT_RESET_DEFAULT & ~AST2500_WDT_RESET_LPC)>;
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default /* CPU */
		&pinctrl_pwm2_default      /* rear */
		&pinctrl_pwm4_default>;    /* front */

	/* CPU */
	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	/* rear */
	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	/* front */
	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};
};

&gpio {
	status = "okay";
	gpio-line-names =
		/*  A */ "", "", "NMI_BTN_N", "BMC_NMI", "", "", "", "",
		/*  B */ "", "", "", "", "", "", "", "",
		/*  C */ "", "", "", "", "", "", "", "",
		/*  D */ "BMC_PSIN", "BMC_PSOUT", "BMC_RESETCON", "RESETCON",
			"", "", "", "",
		/*  E */ "", "", "", "", "", "", "", "",
		/*  F */ "LOCATORLED_STATUS_N", "LOCATORBTN", "", "",
			"", "", "BMC_PCH_SCI_LPC", "BMC_NCSI_MUX_CTL",
		/*  G */ "HWM_BAT_EN", "CHASSIS_ID0", "CHASSIS_ID1", "CHASSIS_ID2",
			"", "", "", "",
		/*  H */ "FM_ME_RCVR_N", "O_PWROK", "", "D4_DIMM_EVENT_3V_N",
			"MFG_MODE_N", "BMC_RTCRST", "BMC_HB_LED_N", "BMC_CASEOPEN",
		/*  I */ "", "", "", "", "", "", "", "",
		/*  J */ "BMC_READY", "BMC_PCH_BIOS_CS_N", "BMC_SMI", "", "", "", "", "",
		/*  K */ "", "", "", "", "", "", "", "",
		/*  L */ "", "", "", "", "", "", "", "",
		/*  M */ "", "", "", "", "", "", "", "",
		/*  N */ "", "", "", "", "", "", "", "",
		/*  O */ "", "", "", "", "", "", "", "",
		/*  P */ "", "", "", "", "", "", "", "",
		/*  Q */ "", "", "", "", "", "", "", "",
		/*  R */ "", "", "", "", "", "", "", "",
		/*  S */ "PCHHOT_BMC_N", "", "RSMRST", "", "", "", "", "",
		/*  T */ "", "", "", "", "", "", "", "",
		/*  U */ "", "", "", "", "", "", "", "",
		/*  V */ "", "", "", "", "", "", "", "",
		/*  W */ "", "", "", "", "", "", "", "",
		/*  X */ "", "", "", "", "", "", "", "",
		/*  Y */ "SLP_S3", "SLP_S5", "", "", "", "", "", "",
		/*  Z */ "CPU_CATERR_BMC_N", "", "SYSTEM_FAULT_LED_N", "BMC_THROTTLE_N",
			"", "", "", "",
		/* AA */ "CPU1_THERMTRIP_LATCH_N", "", "CPU1_PROCHOT_N", "",
			"", "", "IRQ_SMI_ACTIVE_N", "FM_BIOS_POST_CMPLT_N",
		/* AB */ "", "", "ME_OVERRIDE", "BMC_DMI_MODIFY", "", "", "", "",
		/* AC */ "", "", "", "", "", "", "", "";
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default /* 3VSB */
		&pinctrl_adc1_default	   /* 5VSB */
		&pinctrl_adc2_default	   /* CPU1 */
		&pinctrl_adc3_default	   /* VCCSA */
		&pinctrl_adc4_default	   /* VCCM */
		&pinctrl_adc5_default	   /* V10M */
		&pinctrl_adc6_default	   /* VCCIO */
		&pinctrl_adc7_default	   /* VCCGT */
		&pinctrl_adc8_default	   /* VPPM */
		&pinctrl_adc9_default	   /* BAT */
		&pinctrl_adc10_default	   /* 3V */
		&pinctrl_adc11_default	   /* 5V */
		&pinctrl_adc12_default	   /* 12V */
		&pinctrl_adc13_default	   /* GND */
		&pinctrl_adc14_default	   /* GND */
		&pinctrl_adc15_default>;   /* GND */
};
