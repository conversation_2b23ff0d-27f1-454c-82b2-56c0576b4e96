// SPDX-License-Identifier: GPL-2.0+
// Copyright (c) 2018 Inventec Corporation
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Lanyang BMC";
	compatible = "inventec,lanyang-bmc", "aspeed,ast2500";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		flash_memory: region@98000000 {
			no-map;
			reg = <0x98000000 0x04000000>; /* 64M */
		};
	};

	leds {
		compatible = "gpio-leds";

		sys_boot_status {
			label = "System_boot_status";
			gpios = <&gpio ASPEED_GPIO(B, 6) GPIO_ACTIVE_LOW>;
		};

		attention {
			label = "Attention_locator";
			gpios = <&gpio ASPEED_GPIO(B, 7) GPIO_ACTIVE_HIGH>;
		};

		plt_fault {
			label = "Platform_fault";
			gpios = <&gpio ASPEED_GPIO(B, 1) GPIO_ACTIVE_HIGH>;
		};

		hdd_fault {
			label = "Onboard_drive_fault";
			gpios = <&gpio ASPEED_GPIO(B, 3) GPIO_ACTIVE_HIGH>;
		};
		bmc_err {
			lable = "BMC_fault";
			gpios = <&gpio ASPEED_GPIO(H, 6) GPIO_ACTIVE_HIGH>;
		};

		sys_err {
			lable = "Sys_fault";
			gpios = <&gpio ASPEED_GPIO(H, 7) GPIO_ACTIVE_HIGH>;
		};
	};

	fsi: gpio-fsi {
		compatible = "fsi-master-gpio", "fsi-master";
		#address-cells = <2>;
		#size-cells = <0>;

		clock-gpios = <&gpio ASPEED_GPIO(J, 0) GPIO_ACTIVE_HIGH>;
		data-gpios = <&gpio ASPEED_GPIO(J, 1) GPIO_ACTIVE_HIGH>;
		trans-gpios = <&gpio ASPEED_GPIO(D, 5) GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio ASPEED_GPIO(D, 0) GPIO_ACTIVE_HIGH>;
		mux-gpios = <&gpio ASPEED_GPIO(H, 2) GPIO_ACTIVE_HIGH>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 13>, <&adc 14>, <&adc 15>;
	};

	iio-hwmon-battery {
		compatible = "iio-hwmon";
		io-channels = <&adc 12>;
	};
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default
		&pinctrl_pwm2_default &pinctrl_pwm3_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		label = "pnor";
		m25p,fast-read;
	};
};

&spi2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi2ck_default
		     &pinctrl_spi2cs0_default
		     &pinctrl_spi2cs1_default
		     &pinctrl_spi2miso_default
		     &pinctrl_spi2mosi_default>;

	flash@0 {
		status = "okay";
	};
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default>;
};

&lpc_ctrl {
	status = "okay";
	memory-region = <&flash_memory>;
	flash = <&spi1>;
};

&lpc_snoop {
	status = "okay";
	snoop-ports = <0x80>;
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c0 {
	status = "okay";

	eeprom@55 {
		compatible = "atmel,24c64";
		reg = <0x55>;
		pagesize = <32>;
	};

	rtc@68 {
		compatible = "nxp,pcf8523";
		reg = <0x68>;
	};

	tmp75@48 {
		compatible = "ti,tmp75";
		reg = <0x48>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
};

&i2c11 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&gfx {
	status = "okay";
};

&gpio {
	pin_gpio_b0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(B, 0) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_HDD1_PWR_EN";
	};

	pin_gpio_b5 {
		gpio-hog;
		gpios = <ASPEED_GPIO(B, 5) GPIO_ACTIVE_HIGH>;
		input;
		line-name = "BMC_USB1_OCI2";
	};

	pin_gpio_h5 {
		gpio-hog;
		gpios = <ASPEED_GPIO(H, 5) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_CP0_PERST_ENABLE_R";
	};

	pin_gpio_z2 {
		gpio-hog;
		gpios = <ASPEED_GPIO(Z, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "RST_PCA9546_U177_N";
	};

	pin_gpio_aa6 {
		gpio-hog;
		gpios = <ASPEED_GPIO(AA, 6) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_CP0_RESET_N";
	};

	pin_gpio_aa7 {
		gpio-hog;
		gpios = <ASPEED_GPIO(AA, 7) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "BMC_TPM_RESET_N";
	};

	pin_gpio_ab0 {
		gpio-hog;
		gpios = <ASPEED_GPIO(AB, 0) GPIO_ACTIVE_LOW>;
		output-high;
		line-name = "BMC_USB_PWRON_N";
	};
};

&ibt {
	status = "okay";
};

&adc {
	status = "okay";
};

#include "ibm-power9-dual.dtsi"
