/*
 * Copyright (C) 2017 Icenowy Zheng <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/* Orange Pi R1 is based on Orange Pi Zero design */
#include "sun8i-h2-plus-orangepi-zero.dts"

/delete-node/ &reg_vcc_wifi;

/ {
	model = "Xunlong Orange Pi R1";
	compatible = "xunlong,orangepi-r1", "allwinner,sun8i-h2-plus";


	/*
	 * Ths pin of this regulator is the same with the Wi-Fi extra
	 * regulator on the original Zero. However it's used for USB
	 * Ethernet rather than the Wi-Fi now.
	 */
	reg_vcc_usb_eth: reg-vcc-usb-ethernet {
		compatible = "regulator-fixed";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-name = "vcc-usb-ethernet";
		enable-active-high;
		gpio = <&pio 0 20 GPIO_ACTIVE_HIGH>;
	};

	aliases {
		ethernet1 = &rtl8189etv;
	};
};

&spi0 {
	status = "okay";

	flash@0 {
		compatible = "mxicy,mx25l12805d", "jedec,spi-nor";
	};
};

&ohci1 {
	/*
	 * RTL8152B USB-Ethernet adapter is connected to USB1,
	 * and it's a USB 2.0 device. So the OHCI1 controller
	 * can be left disabled.
	 */
	status = "disabled";
};

&mmc1 {
	vmmc-supply = <&reg_vcc3v3>;
	vqmmc-supply = <&reg_vcc3v3>;

	rtl8189etv: wifi@1 {
		reg = <1>;
	};
};

&usbphy {
	usb1_vbus-supply = <&reg_vcc_usb_eth>;
};
