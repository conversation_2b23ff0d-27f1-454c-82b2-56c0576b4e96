// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2022 Broadcom Ltd.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	compatible = "brcm,bcm6846", "brcm,bcmbca";
	#address-cells = <1>;
	#size-cells = <1>;

	interrupt-parent = <&gic>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		CA7_0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x0>;
			next-level-cache = <&L2_0>;
			enable-method = "psci";
		};

		CA7_1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x1>;
			next-level-cache = <&L2_0>;
			enable-method = "psci";
		};

		L2_0: l2-cache0 {
			compatible = "cache";
			cache-level = <2>;
			cache-unified;
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>;
		arm,cpu-registers-not-fw-configured;
	};

	pmu: pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&CA7_0>, <&CA7_1>;
	};

	clocks: clocks {
		periph_clk: periph-clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <200000000>;
		};

		hsspi_pll: hsspi-pll {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <400000000>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	axi@81000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x81000000 0x8000>;

		gic: interrupt-controller@1000 {
			compatible = "arm,cortex-a7-gic";
			#interrupt-cells = <3>;
			interrupt-controller;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_HIGH)>;
			reg = <0x1000 0x1000>,
				<0x2000 0x2000>,
				<0x4000 0x2000>,
				<0x6000 0x2000>;
		};
	};

	bus@ff800000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xff800000 0x800000>;

		watchdog@480 {
			compatible = "brcm,bcm6345-wdt";
			reg = <0x480 0x10>;
		};

		/* GPIOs 0 .. 31 */
		gpio0: gpio@500 {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x500 0x04>, <0x520 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		/* GPIOs 32 .. 63 */
		gpio1: gpio@504 {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x504 0x04>, <0x524 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		/* GPIOs 64 .. 95 */
		gpio2: gpio@508 {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x508 0x04>, <0x528 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		/* GPIOs 96 .. 127 */
		gpio3: gpio@50c {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x50c 0x04>, <0x52c 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		/* GPIOs 128 .. 159 */
		gpio4: gpio@510 {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x510 0x04>, <0x530 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		/* GPIOs 160 .. 191 */
		gpio5: gpio@514 {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x514 0x04>, <0x534 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		/* GPIOs 192 .. 223 */
		gpio6: gpio@518 {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x518 0x04>, <0x538 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		/* GPIOs 224 .. 255 */
		gpio7: gpio@51c {
			compatible = "brcm,bcm6345-gpio";
			reg = <0x51c 0x04>, <0x53c 0x04>;
			reg-names = "dirout", "dat";
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		uart0: serial@640 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x640 0x1b>;
			interrupts = <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&periph_clk>;
			clock-names = "refclk";
			status = "disabled";
		};

		rng@b80 {
			compatible = "brcm,iproc-rng200";
			reg = <0xb80 0x28>;
		};

		leds: led-controller@800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,bcm63138-leds";
			reg = <0x800 0xdc>;
			status = "disabled";
		};

		hsspi: spi@1000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,bcm6846-hsspi", "brcm,bcmbca-hsspi-v1.0";
			reg = <0x1000 0x600>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&hsspi_pll &hsspi_pll>;
			clock-names = "hsspi", "pll";
			num-cs = <8>;
			status = "disabled";
		};

		nand_controller: nand-controller@1800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,nand-bcm63138", "brcm,brcmnand-v7.1", "brcm,brcmnand";
			reg = <0x1800 0x600>, <0x2000 0x10>;
			reg-names = "nand", "nand-int-base";
			status = "disabled";

			nandcs: nand@0 {
				compatible = "brcm,nandcs";
				reg = <0>;
			};
		};

		mdio: mdio@2060 {
			compatible = "brcm,bcm6846-mdio";
			reg = <0x02060 0x10>, <0x5a068 0x4>;
			reg-names = "mdio", "mdio_indir_rw";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		pl081_dma: dma-controller@59000 {
			compatible = "arm,pl081", "arm,primecell";
			// The magic B105F00D info is missing
			arm,primecell-periphid = <0x00041081>;
			reg = <0x59000 0x1000>;
			interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
			memcpy-burst-size = <256>;
			memcpy-bus-width = <32>;
			clocks = <&periph_clk>;
			clock-names = "apb_pclk";
			#dma-cells = <2>;
		};
	};
};
