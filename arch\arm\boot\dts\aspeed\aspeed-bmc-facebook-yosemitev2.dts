// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright (c) 2018 Facebook Inc.
/dts-v1/;
#include "aspeed-g5.dtsi"
#include <dt-bindings/i2c/i2c.h>

/ {
	model = "Facebook Yosemitev2 BMC";
	compatible = "facebook,yosemitev2-bmc", "aspeed,ast2500";
	aliases {
		serial4 = &uart5;
	};
	chosen {
		stdout-path = &uart5;
	};

	memory@80000000 {
		reg = <0x80000000 0x20000000>;
	};

	iio-hwmon {
		// VOLATAGE SENSOR
		compatible = "iio-hwmon";
		io-channels = <&adc 0> , <&adc 1> , <&adc 2> ,  <&adc 3> ,
		<&adc 4> , <&adc 5> , <&adc 6> ,  <&adc 7> ,
		<&adc 8> , <&adc 9> , <&adc 10>, <&adc 11> ,
		<&adc 12> , <&adc 13> , <&adc 14> , <&adc 15> ;
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
#include "openbmc-flash-layout.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
	};
};
&uart1 {
	// Host1 Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
		     &pinctrl_rxd1_default>;
};

&uart2 {
	// Host2 Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default
		     &pinctrl_rxd2_default>;

};

&uart3 {
	// Host3 Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd3_default
		     &pinctrl_rxd3_default>;
};

&uart4 {
	// Host4 Console
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd4_default
		     &pinctrl_rxd4_default>;
};

&uart5 {
	// BMC Console
	status = "okay";
};

&vuart {
	// Virtual UART
	status = "okay";
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	use-ncsi;
	mellanox,multi-host;
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_adc0_default
			&pinctrl_adc1_default
			&pinctrl_adc2_default
			&pinctrl_adc3_default
			&pinctrl_adc4_default
			&pinctrl_adc5_default
			&pinctrl_adc6_default
			&pinctrl_adc7_default
			&pinctrl_adc8_default
			&pinctrl_adc9_default
			&pinctrl_adc10_default
			&pinctrl_adc11_default
			&pinctrl_adc12_default
			&pinctrl_adc13_default
			&pinctrl_adc14_default
			&pinctrl_adc15_default>;
};

&i2c1 {
	//Host1 IPMB bus
	status = "okay";
	multi-master;
	ipmb1@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c3 {
	//Host2 IPMB bus
	status = "okay";
	multi-master;
	ipmb3@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c5 {
	//Host3 IPMB bus
	status = "okay";
	multi-master;
	ipmb5@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c7 {
	//Host4 IPMB bus
	status = "okay";
	multi-master;
	ipmb7@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c8 {
	status = "okay";
	//FRU EEPROM
	eeprom@51 {
		compatible = "atmel,24c64";
		reg = <0x51>;
		pagesize = <32>;
	};
};

&i2c9 {
	status = "okay";
	tmp421@4e {
	//INLET TEMP
		compatible = "ti,tmp421";
		reg = <0x4e>;
	};
	//OUTLET TEMP
	tmp421@4f {
		compatible = "ti,tmp421";
		reg = <0x4f>;
	};
};

&i2c10 {
	status = "okay";
	//HSC
	adm1278@40 {
		compatible = "adi,adm1278";
		reg = <0x40>;
	};
};

&i2c11 {
	status = "okay";
	//MEZZ_TEMP_SENSOR
	tmp421@1f {
		compatible = "ti,tmp421";
		reg = <0x1f>;
	};
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
	// Debug Card
	multi-master;
	ipmb13@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&pwm_tacho {
	status = "okay";
	//FSC
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default>;
	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};
	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};
};
