/*
 * Copyright 2016 Linaro Ltd
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "arm,realview-pbx";

	chosen { };

	aliases {
		serial0 = &serial0;
		serial1 = &serial1;
		serial2 = &serial2;
		serial3 = &serial3;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
	};

	memory {
		device_type = "memory";
		/* 128 MiB memory @ 0x0 */
		reg = <0x00000000 0x08000000>;
	};

	/* The voltage to the MMC card is hardwired at 3.3V */
	vmmc: regulator-vmmc {
		compatible = "regulator-fixed";
		regulator-name = "vmmc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
        };

	veth: regulator-veth {
		compatible = "regulator-fixed";
		regulator-name = "veth";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
	};

	xtal24mhz: mclk: kmiclk: sspclk: uartclk: wdogclk: clock-24000000 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
	};

	refclk32khz: clock-32768 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <32768>;
	};

	timclk: clock-1000000 {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-div = <24>;
		clock-mult = <1>;
		clocks = <&xtal24mhz>;
	};

	/* FIXME: this actually hangs off the PLL clocks */
	pclk: clock-pclk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <0>;
	};

	flash0@******** {
		/* 2 * 32MiB NOR Flash memory */
		compatible = "arm,versatile-flash", "cfi-flash";
		reg = <0x******** 0x04000000>;
		bank-width = <4>;
		partitions {
			compatible = "arm,arm-firmware-suite";
		};
	};

	flash1@******** {
		/* 2 * 32MiB NOR Flash memory */
		compatible = "arm,versatile-flash", "cfi-flash";
		reg = <0x******** 0x04000000>;
		bank-width = <4>;
		partitions {
			compatible = "arm,arm-firmware-suite";
		};
	};

	/* SMSC 9118 ethernet with PHY and EEPROM */
	ethernet: ethernet@4e000000 {
		compatible = "smsc,lan9118", "smsc,lan9115";
		reg = <0x4e000000 0x10000>;
		phy-mode = "mii";
		reg-io-width = <4>;
		smsc,irq-active-high;
		smsc,irq-push-pull;
		vdd33a-supply = <&veth>;
		vddvario-supply = <&veth>;
	};

	usb: usb@4f000000 {
		compatible = "nxp,usb-isp1761";
		reg = <0x4f000000 0x20000>;
		dr_mode = "peripheral";
	};

	bridge {
		compatible = "ti,ths8134a", "ti,ths8134";
		#address-cells = <1>;
		#size-cells = <0>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				vga_bridge_in: endpoint {
					remote-endpoint = <&clcd_pads>;
				};
			};

			port@1 {
				reg = <1>;

				vga_bridge_out: endpoint {
					remote-endpoint = <&vga_con_in>;
				};
			};
		};
	};

	vga {
		/*
		 * This DDC I2C is connected directly to the DVI portions
		 * of the connector, so it's not really working when the
		 * monitor is connected to the VGA connector.
		 */
		compatible = "vga-connector";
		ddc-i2c-bus = <&i2c1>;

		port {
			vga_con_in: endpoint {
				remote-endpoint = <&vga_bridge_out>;
			};
		};
	};

	soc: soc {
		compatible = "arm,realview-pbx-soc", "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		regmap = <&syscon>;
		ranges;

		syscon: syscon@10000000 {
			compatible = "arm,realview-pbx-syscon", "syscon", "simple-mfd";
			reg = <0x10000000 0x1000>;
			ranges = <0x0 0x10000000 0x1000>;
			#address-cells = <1>;
			#size-cells = <1>;

			led@8,0 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x01>;
				label = "versatile:0";
				linux,default-trigger = "heartbeat";
				default-state = "on";
			};
			led@8,1 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x02>;
				label = "versatile:1";
				linux,default-trigger = "mmc0";
				default-state = "off";
			};
			led@8,2 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x04>;
				label = "versatile:2";
				linux,default-trigger = "cpu0";
				default-state = "off";
			};
			led@8,3 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x08>;
				label = "versatile:3";
				default-state = "off";
			};
			led@8,4 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x10>;
				label = "versatile:4";
				default-state = "off";
			};
			led@8,5 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x20>;
				label = "versatile:5";
				default-state = "off";
			};
			led@8,6 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x40>;
				label = "versatile:6";
				default-state = "off";
			};
			led@8,7 {
				compatible = "register-bit-led";
				reg = <0x08 0x04>;
				offset = <0x08>;
				mask = <0x80>;
				label = "versatile:7";
				default-state = "off";
			};
			oscclk0: clock-controller@c {
				compatible = "arm,syscon-icst307";
				reg = <0x0c 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x0C>;
				clocks = <&xtal24mhz>;
			};
			oscclk1: clock-controller@10 {
				compatible = "arm,syscon-icst307";
				reg = <0x10 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x10>;
				clocks = <&xtal24mhz>;
			};
			oscclk2: clock-controller@14 {
				compatible = "arm,syscon-icst307";
				reg = <0x14 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x14>;
				clocks = <&xtal24mhz>;
			};
			oscclk3: clock-controller@18 {
				compatible = "arm,syscon-icst307";
				reg = <0x18 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x18>;
				clocks = <&xtal24mhz>;
			};
			oscclk4: clock-controller@1c {
				compatible = "arm,syscon-icst307";
				reg = <0x1c 0x04>;
				#clock-cells = <0>;
				lock-offset = <0x20>;
				vco-offset = <0x1c>;
				clocks = <&xtal24mhz>;
			};
		};

		sp810_syscon0: sysctl@10001000 {
			compatible = "arm,sp810", "arm,primecell";
			reg = <0x10001000 0x1000>;
			clocks = <&refclk32khz>, <&timclk>, <&xtal24mhz>;
			clock-names = "refclk", "timclk", "apb_pclk";
			#clock-cells = <1>;
			clock-output-names = "timerclk0",
					     "timerclk1",
					     "timerclk2",
					     "timerclk3";
			assigned-clocks = <&sp810_syscon0 0>,
					  <&sp810_syscon0 1>,
					  <&sp810_syscon0 2>,
					  <&sp810_syscon0 3>;
			assigned-clock-parents = <&timclk>,
					       <&timclk>,
					       <&timclk>,
					       <&timclk>;
		};

		i2c0: i2c@10002000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "arm,versatile-i2c";
			reg = <0x10002000 0x1000>;

			rtc@68 {
				compatible = "dallas,ds1338";
				reg = <0x68>;
			};
		};

		serial0: serial@10009000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x10009000 0x1000>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		serial1: serial@1000a000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x1000a000 0x1000>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		serial2: serial@1000b000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x1000b000 0x1000>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};

		ssp: spi@1000d000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x1000d000 0x1000>;
			clocks = <&sspclk>, <&pclk>;
			clock-names = "sspclk", "apb_pclk";
		};

		wdog0: watchdog@1000f000 {
			compatible = "arm,sp805", "arm,primecell";
			reg = <0x1000f000 0x1000>;
			clocks = <&wdogclk>, <&pclk>;
			clock-names = "wdog_clk", "apb_pclk";
			status = "disabled";
		};

		wdog1: watchdog@10010000 {
			compatible = "arm,sp805", "arm,primecell";
			reg = <0x10010000 0x1000>;
			clocks = <&wdogclk>, <&pclk>;
			clock-names = "wdog_clk", "apb_pclk";
			status = "disabled";
		};

		timer01: timer@10011000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x10011000 0x1000>;
			clocks = <&sp810_syscon0 0>,
			         <&sp810_syscon0 1>,
				 <&pclk>;
			clock-names = "timerclk0",
				    "timerclk1",
				    "apb_pclk";
		};

		timer23: timer@10012000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x10012000 0x1000>;
			clocks = <&sp810_syscon0 2>,
			         <&sp810_syscon0 3>,
				 <&pclk>;
			clock-names = "timerclk2",
				    "timerclk3",
				    "apb_pclk";
		};

		gpio0: gpio@10013000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x10013000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		gpio1: gpio@10014000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x10014000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		gpio2: gpio@10015000 {
			compatible = "arm,pl061", "arm,primecell";
			reg = <0x10015000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		i2c1: i2c@10016000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "arm,versatile-i2c";
			reg = <0x10016000 0x1000>;
		};

		rtc: rtc@10017000 {
			compatible = "arm,pl031", "arm,primecell";
			reg = <0x10017000 0x1000>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		timer45: timer@10018000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x10018000 0x1000>;
			clocks = <&timclk>, <&timclk>, <&pclk>;
			clock-names = "timerclk4", "timerclk5", "apb_pclk";
		};

		timer67: timer@10019000 {
			compatible = "arm,sp804", "arm,primecell";
			reg = <0x10019000 0x1000>;
			clocks = <&timclk>, <&timclk>, <&pclk>;
			clock-names = "timerclk6", "timerclk7", "apb_pclk";
		};

		sp810_syscon1: sysctl@1001a000 {
			compatible = "arm,sp810", "arm,primecell";
			reg = <0x1001a000 0x1000>;
			clocks = <&refclk32khz>, <&timclk>, <&xtal24mhz>;
			clock-names = "refclk", "timclk", "apb_pclk";
			#clock-cells = <1>;
			clock-output-names = "timerclk4",
					     "timerclk5",
					     "timerclk6",
					     "timerclk7";
			assigned-clocks = <&sp810_syscon1 0>,
					  <&sp810_syscon1 1>,
					  <&sp810_syscon1 2>,
					  <&sp810_syscon1 3>;
			assigned-clock-parents = <&timclk>,
					       <&timclk>,
					       <&timclk>,
					       <&timclk>;
		};
	};


	/* These peripherals are inside the FPGA */
	fpga {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges;

		aaci: aaci@10004000 {
			compatible = "arm,pl041", "arm,primecell";
			reg = <0x10004000 0x1000>;
			clocks = <&pclk>;
			clock-names = "apb_pclk";
		};

		mmc: mmcsd@10005000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x10005000 0x1000>;

			/* Due to frequent FIFO overruns, use just 500 kHz */
			max-frequency = <500000>;
			bus-width = <4>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			clocks = <&mclk>, <&pclk>;
			clock-names = "mclk", "apb_pclk";
			vmmc-supply = <&vmmc>;
			cd-gpios = <&gpio2 0 GPIO_ACTIVE_LOW>;
			wp-gpios = <&gpio2 1 GPIO_ACTIVE_HIGH>;
		};

		kmi0: kmi@10006000 {
			compatible = "arm,pl050", "arm,primecell";
			reg = <0x10006000 0x1000>;
			clocks = <&kmiclk>, <&pclk>;
			clock-names = "KMIREFCLK", "apb_pclk";
		};

		kmi1: kmi@10007000 {
			compatible = "arm,pl050", "arm,primecell";
			reg = <0x10007000 0x1000>;
			clocks = <&kmiclk>, <&pclk>;
			clock-names = "KMIREFCLK", "apb_pclk";
		};

		serial3: serial@1000c000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x1000c000 0x1000>;
			clocks = <&uartclk>, <&pclk>;
			clock-names = "uartclk", "apb_pclk";
		};
	};

	/* These peripherals are inside the NEC ISSP */
	issp {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges;

		clcd: clcd@10020000 {
			compatible = "arm,pl111", "arm,primecell";
			reg = <0x10020000 0x1000>;
			interrupt-names = "combined";
			clocks = <&oscclk4>, <&pclk>;
			clock-names = "clcdclk", "apb_pclk";
			/* 1024x768 16bpp @65MHz works fine */
			max-memory-bandwidth = <95000000>;

			port {
				clcd_pads: endpoint {
					remote-endpoint = <&vga_bridge_in>;
					arm,pl11x,tft-r0g0b0-pads = <0 8 16>;
				};
			};
		};
	};
};
