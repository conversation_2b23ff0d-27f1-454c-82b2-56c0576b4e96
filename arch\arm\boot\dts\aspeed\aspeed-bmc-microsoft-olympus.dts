//SPDX-License-Identifier: GPL-2.0+

/dts-v1/;

#include "aspeed-g4.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>

/ {
	model = "Olympus BMC";
	compatible = "microsoft,olympus-bmc", "aspeed,ast2400";

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@40000000 {
		reg = <0x40000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@5f000000 {
			no-map;
			reg = <0x5f000000 0x01000000>; /* 16M */
		};
	};

	leds {
		compatible = "gpio-leds";

		bmc_heartbeat {
			gpios = <&gpio ASPEED_GPIO(B, 0) GPIO_ACTIVE_LOW>;
		};

		power_green {
			gpios = <&gpio ASPEED_GPIO(U, 2) GPIO_ACTIVE_HIGH>;
		};

		power_amber {
			gpios = <&gpio ASPEED_GPIO(U, 3) GPIO_ACTIVE_HIGH>;
		};

		identify {
			gpios = <&gpio ASPEED_GPIO(Q, 5) GPIO_ACTIVE_LOW>;
		};

		fault {
			gpios = <&gpio ASPEED_GPIO(A, 1) GPIO_ACTIVE_LOW>;
		};
	};


	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
		<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>;
	};
};

&adc {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 =    <&pinctrl_adc0_default
			&pinctrl_adc1_default
			&pinctrl_adc2_default
			&pinctrl_adc3_default
			&pinctrl_adc4_default
			&pinctrl_adc5_default
			&pinctrl_adc6_default
			&pinctrl_adc7_default>;
};

&fmc {
	status = "okay";

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
#include "openbmc-flash-layout.dtsi"
	};
};

&spi {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;

	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "pnor";
	};
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii1_default &pinctrl_mdio1_default>;
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";

	tmp421@4c {
		compatible = "ti,tmp421";
		reg = <0x4c>;
	};
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
	clock-frequency = <100000>;
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";

	tmp421@4c {
		compatible = "ti,tmp421";
		reg = <0x4c>;
	};
};

&i2c7 {
	status = "okay";
};

&vuart {
	status = "okay";
};

&wdt2 {
	status = "okay";
};

&lpc_ctrl {
	status = "okay";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 =    <&pinctrl_pwm0_default
			&pinctrl_pwm1_default
			&pinctrl_pwm2_default
			&pinctrl_pwm3_default
			&pinctrl_pwm4_default
			&pinctrl_pwm5_default
			&pinctrl_pwm6_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01>;
	};

	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02>;
	};

	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x03>;
	};

	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04>;
	};

	fan@5 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x05>;
	};

};
