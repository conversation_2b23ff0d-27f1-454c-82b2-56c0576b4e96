/*
 * Copyright 2014 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "sun8i-a23-a33.dtsi"
#include <dt-bindings/thermal/thermal.h>

/ {
	cpu0_opp_table: opp-table-cpu {
		compatible = "operating-points-v2";
		opp-shared;

		opp-120000000 {
			opp-hz = /bits/ 64 <120000000>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-********* {
			opp-hz = /bits/ 64 <*********>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-312000000 {
			opp-hz = /bits/ 64 <312000000>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-408000000 {
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-480000000 {
			opp-hz = /bits/ 64 <480000000>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-504000000 {
			opp-hz = /bits/ 64 <504000000>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-648000000 {
			opp-hz = /bits/ 64 <648000000>;
			opp-microvolt = <1040000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-720000000 {
			opp-hz = /bits/ 64 <720000000>;
			opp-microvolt = <1100000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <1100000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-912000000 {
			opp-hz = /bits/ 64 <912000000>;
			opp-microvolt = <1200000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <1200000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};
	};

	cpus {
		cpu@0 {
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};

		cpu1: cpu@1 {
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};

		cpu2: cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <2>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};

		cpu3: cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <3>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&ths>;
	};

	mali_opp_table: opp-table-gpu {
		compatible = "operating-points-v2";

		opp-********* {
			opp-hz = /bits/ 64 <*********>;
		};

		opp-********* {
			opp-hz = /bits/ 64 <*********>;
		};

		opp-********* {
			opp-hz = /bits/ 64 <*********>;
		};
	};

	sound: sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "sun8i-a33-audio";
		simple-audio-card,format = "i2s";
		simple-audio-card,frame-master = <&link_codec>;
		simple-audio-card,bitclock-master = <&link_codec>;
		simple-audio-card,mclk-fs = <128>;
		simple-audio-card,aux-devs = <&codec_analog>;
		simple-audio-card,routing =
			"Left DAC", "DACL",
			"Right DAC", "DACR";
		status = "disabled";

		simple-audio-card,cpu {
			sound-dai = <&dai>;
		};

		link_codec: simple-audio-card,codec {
			sound-dai = <&codec 0>;
		};
	};

	soc {
		video-codec@1c0e000 {
			compatible = "allwinner,sun8i-a33-video-engine";
			reg = <0x01c0e000 0x1000>;
			clocks = <&ccu CLK_BUS_VE>, <&ccu CLK_VE>,
				 <&ccu CLK_DRAM_VE>;
			clock-names = "ahb", "mod", "ram";
			resets = <&ccu RST_BUS_VE>;
			interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>;
			allwinner,sram = <&ve_sram 1>;
		};

		crypto: crypto-engine@1c15000 {
			compatible = "allwinner,sun8i-a33-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SS>, <&ccu CLK_SS>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_SS>;
			reset-names = "ahb";
		};

		dai: dai@1c22c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun6i-a31-i2s";
			reg = <0x01c22c00 0x200>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CODEC>, <&ccu CLK_AC_DIG>;
			clock-names = "apb", "mod";
			resets = <&ccu RST_BUS_CODEC>;
			dmas = <&dma 15>, <&dma 15>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		codec: codec@1c22e00 {
			#sound-dai-cells = <1>;
			compatible = "allwinner,sun8i-a33-codec";
			reg = <0x01c22e00 0x400>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CODEC>, <&ccu CLK_AC_DIG>;
			clock-names = "bus", "mod";
			status = "disabled";
		};

		ths: ths@1c25000 {
			compatible = "allwinner,sun8i-a33-ths";
			reg = <0x01c25000 0x100>;
			#thermal-sensor-cells = <0>;
			#io-channel-cells = <0>;
		};

		dsi: dsi@1ca0000 {
			compatible = "allwinner,sun6i-a31-mipi-dsi";
			reg = <0x01ca0000 0x1000>;
			interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_MIPI_DSI>,
				 <&ccu CLK_DSI_SCLK>;
			clock-names = "bus", "mod";
			resets = <&ccu RST_BUS_MIPI_DSI>;
			phys = <&dphy>;
			phy-names = "dphy";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;

			port {
				dsi_in_tcon0: endpoint {
					remote-endpoint = <&tcon0_out_dsi>;
				};
			};
		};

		dphy: d-phy@1ca1000 {
			compatible = "allwinner,sun6i-a31-mipi-dphy";
			reg = <0x01ca1000 0x1000>;
			interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_MIPI_DSI>,
				 <&ccu CLK_DSI_DPHY>;
			clock-names = "bus", "mod";
			resets = <&ccu RST_BUS_MIPI_DSI>;
			status = "disabled";
			#phy-cells = <0>;
		};
	};

	thermal-zones {
		cpu-thermal {
			/* milliseconds */
			polling-delay-passive = <250>;
			polling-delay = <1000>;
			thermal-sensors = <&ths>;

			cooling-maps {
				map0 {
					trip = <&cpu_alert0>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
				map1 {
					trip = <&cpu_alert1>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};

				map2 {
					trip = <&gpu_alert0>;
					cooling-device = <&mali 1 THERMAL_NO_LIMIT>;
				};

				map3 {
					trip = <&gpu_alert1>;
					cooling-device = <&mali 2 THERMAL_NO_LIMIT>;
				};
			};

			trips {
				cpu_alert0: cpu-alert0 {
					/* milliCelsius */
					temperature = <75000>;
					hysteresis = <2000>;
					type = "passive";
				};

				gpu_alert0: gpu-alert0 {
					/* milliCelsius */
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_alert1: cpu-alert1 {
					/* milliCelsius */
					temperature = <90000>;
					hysteresis = <2000>;
					type = "hot";
				};

				gpu_alert1: gpu-alert1 {
					/* milliCelsius */
					temperature = <95000>;
					hysteresis = <2000>;
					type = "hot";
				};

				cpu_crit: cpu-crit {
					/* milliCelsius */
					temperature = <110000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};
};

&be0 {
	compatible = "allwinner,sun8i-a33-display-backend";
	/* A33 has an extra "SAT" module packed inside the display backend */
	reg = <0x01e60000 0x10000>, <0x01e80000 0x1000>;
	reg-names = "be", "sat";
	clocks = <&ccu CLK_BUS_DE_BE>, <&ccu CLK_DE_BE>,
		 <&ccu CLK_DRAM_DE_BE>, <&ccu CLK_BUS_SAT>;
	clock-names = "ahb", "mod",
		      "ram", "sat";
	resets = <&ccu RST_BUS_DE_BE>, <&ccu RST_BUS_SAT>;
	reset-names = "be", "sat";
};

&ccu {
	compatible = "allwinner,sun8i-a33-ccu";
};

&de {
	compatible = "allwinner,sun8i-a33-display-engine";
};

&drc0 {
	compatible = "allwinner,sun8i-a33-drc";
};

&fe0 {
	compatible = "allwinner,sun8i-a33-display-frontend";
};

&mali {
	operating-points-v2 = <&mali_opp_table>;
};

&pio {
	compatible = "allwinner,sun8i-a33-pinctrl";
	interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;

	uart0_pb_pins: uart0-pb-pins {
		pins = "PB0", "PB1";
		function = "uart0";
	};

};

&tcon0 {
	compatible = "allwinner,sun8i-a33-tcon";
};

&tcon0_out {
	#address-cells = <1>;
	#size-cells = <0>;

	tcon0_out_dsi: endpoint@1 {
		reg = <1>;
		remote-endpoint = <&dsi_in_tcon0>;
	};
};

&usb_otg {
	compatible = "allwinner,sun8i-a33-musb";
};

&usbphy {
	compatible = "allwinner,sun8i-a33-usb-phy";
	reg = <0x01c19400 0x14>, <0x01c1a800 0x4>;
	reg-names = "phy_ctrl", "pmu1";
};
