// SPDX-License-Identifier: (GPL-2.0+ OR X11)
/*
 * Copyright 2018 Ice<PERSON>wy Zheng <<EMAIL>>
 * Copyright 2018 Me<PERSON><PERSON> <<EMAIL>>
 */

#include <dt-bindings/clock/suniv-ccu-f1c100s.h>
#include <dt-bindings/reset/suniv-ccu-f1c100s.h>
#include <dt-bindings/dma/sun4i-a10.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&intc>;

	clocks {
		osc24M: clk-24M {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-output-names = "osc24M";
		};

		osc32k: clk-32k {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-output-names = "osc32k";
		};
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			compatible = "arm,arm926ej-s";
			device_type = "cpu";
			reg = <0x0>;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		sram-controller@1c00000 {
			compatible = "allwinner,suniv-f1c100s-system-control",
				     "allwinner,sun4i-a10-system-control";
			reg = <0x01c00000 0x30>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			sram_d: sram@10000 {
				compatible = "mmio-sram";
				reg = <0x00010000 0x1000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x00010000 0x1000>;

				otg_sram: sram-section@0 {
					compatible = "allwinner,suniv-f1c100s-sram-d",
						     "allwinner,sun4i-a10-sram-d";
					reg = <0x0000 0x1000>;
					status = "disabled";
				};
			};
		};

		spi0: spi@1c05000 {
			compatible = "allwinner,suniv-f1c100s-spi",
				     "allwinner,sun8i-h3-spi";
			reg = <0x01c05000 0x1000>;
			interrupts = <10>;
			clocks = <&ccu CLK_BUS_SPI0>, <&ccu CLK_BUS_SPI0>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_SPI0>;
			status = "disabled";
			num-cs = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi1: spi@1c06000 {
			compatible = "allwinner,suniv-f1c100s-spi",
				     "allwinner,sun8i-h3-spi";
			reg = <0x01c06000 0x1000>;
			interrupts = <11>;
			clocks = <&ccu CLK_BUS_SPI1>, <&ccu CLK_BUS_SPI1>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_SPI1>;
			status = "disabled";
			num-cs = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,suniv-f1c100s-mmc",
				     "allwinner,sun7i-a20-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC0>,
				 <&ccu CLK_MMC0>,
				 <&ccu CLK_MMC0_OUTPUT>,
				 <&ccu CLK_MMC0_SAMPLE>;
			clock-names = "ahb", "mmc", "output", "sample";
			resets = <&ccu RST_BUS_MMC0>;
			reset-names = "ahb";
			interrupts = <23>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,suniv-f1c100s-mmc",
				     "allwinner,sun7i-a20-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC1>,
				 <&ccu CLK_MMC1>,
				 <&ccu CLK_MMC1_OUTPUT>,
				 <&ccu CLK_MMC1_SAMPLE>;
			clock-names = "ahb", "mmc", "output", "sample";
			resets = <&ccu RST_BUS_MMC1>;
			reset-names = "ahb";
			interrupts = <24>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		usb_otg: usb@1c13000 {
			compatible = "allwinner,suniv-f1c100s-musb";
			reg = <0x01c13000 0x0400>;
			clocks = <&ccu CLK_BUS_OTG>;
			resets = <&ccu RST_BUS_OTG>;
			interrupts = <26>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			allwinner,sram = <&otg_sram 1>;
			status = "disabled";
		};

		usbphy: phy@1c13400 {
			compatible = "allwinner,suniv-f1c100s-usb-phy";
			reg = <0x01c13400 0x10>;
			reg-names = "phy_ctrl";
			clocks = <&ccu CLK_USB_PHY0>;
			clock-names = "usb0_phy";
			resets = <&ccu RST_USB_PHY0>;
			reset-names = "usb0_reset";
			#phy-cells = <1>;
			status = "disabled";
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,suniv-f1c100s-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <18>;
			clocks = <&ccu CLK_BUS_DMA>;
			resets = <&ccu RST_BUS_DMA>;
			#dma-cells = <2>;
		};

		ccu: clock@1c20000 {
			compatible = "allwinner,suniv-f1c100s-ccu";
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&osc32k>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		intc: interrupt-controller@1c20400 {
			compatible = "allwinner,suniv-f1c100s-ic";
			reg = <0x01c20400 0x400>;
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			compatible = "allwinner,suniv-f1c100s-pinctrl";
			reg = <0x01c20800 0x400>;
			interrupts = <38>, <39>, <40>;
			clocks = <&ccu CLK_BUS_PIO>, <&osc24M>, <&osc32k>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2", "PF3", "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
			};

			/omit-if-no-ref/
			i2c0_pd_pins: i2c0-pd-pins {
				pins = "PD0", "PD12";
				function = "i2c0";
			};

			spi0_pc_pins: spi0-pc-pins {
				pins = "PC0", "PC1", "PC2", "PC3";
				function = "spi0";
			};

			uart0_pe_pins: uart0-pe-pins {
				pins = "PE0", "PE1";
				function = "uart0";
			};

			/omit-if-no-ref/
			uart1_pa_pins: uart1-pa-pins {
				pins = "PA2", "PA3";
				function = "uart1";
			};
		};

		i2c0: i2c@1c27000 {
			compatible = "allwinner,suniv-f1c100s-i2c",
				     "allwinner,sun6i-a31-i2c";
			reg = <0x01c27000 0x400>;
			interrupts = <7>;
			clocks = <&ccu CLK_BUS_I2C0>;
			resets = <&ccu RST_BUS_I2C0>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c1: i2c@1c27400 {
			compatible = "allwinner,suniv-f1c100s-i2c",
				     "allwinner,sun6i-a31-i2c";
			reg = <0x01c27400 0x400>;
			interrupts = <8>;
			clocks = <&ccu CLK_BUS_I2C1>;
			resets = <&ccu RST_BUS_I2C1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c2: i2c@1c27800 {
			compatible = "allwinner,suniv-f1c100s-i2c",
				     "allwinner,sun6i-a31-i2c";
			reg = <0x01c27800 0x400>;
			interrupts = <9>;
			clocks = <&ccu CLK_BUS_I2C2>;
			resets = <&ccu RST_BUS_I2C2>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		timer@1c20c00 {
			compatible = "allwinner,suniv-f1c100s-timer";
			reg = <0x01c20c00 0x90>;
			interrupts = <13>, <14>, <15>;
			clocks = <&osc24M>;
		};

		wdt: watchdog@1c20ca0 {
			compatible = "allwinner,suniv-f1c100s-wdt",
				     "allwinner,sun6i-a31-wdt";
			reg = <0x01c20ca0 0x20>;
			interrupts = <16>;
			clocks = <&osc32k>;
		};

		pwm: pwm@1c21000 {
			compatible = "allwinner,suniv-f1c100s-pwm",
				     "allwinner,sun7i-a20-pwm";
			reg = <0x01c21000 0x400>;
			clocks = <&osc24M>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		ir: ir@1c22c00 {
			compatible = "allwinner,suniv-f1c100s-ir",
				     "allwinner,sun6i-a31-ir";
			reg = <0x01c22c00 0x400>;
			clocks = <&ccu CLK_BUS_IR>, <&ccu CLK_IR>;
			clock-names = "apb", "ir";
			resets = <&ccu RST_BUS_IR>;
			interrupts = <6>;
			status = "disabled";
		};

		lradc: lradc@1c23400 {
			compatible = "allwinner,suniv-f1c100s-lradc",
				     "allwinner,sun8i-a83t-r-lradc";
			reg = <0x01c23400 0x400>;
			interrupts = <22>;
			status = "disabled";
		};

		uart0: serial@1c25000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c25000 0x400>;
			interrupts = <1>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART0>;
			resets = <&ccu RST_BUS_UART0>;
			status = "disabled";
		};

		uart1: serial@1c25400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c25400 0x400>;
			interrupts = <2>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART1>;
			resets = <&ccu RST_BUS_UART1>;
			status = "disabled";
		};

		uart2: serial@1c25800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c25800 0x400>;
			interrupts = <3>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART2>;
			resets = <&ccu RST_BUS_UART2>;
			status = "disabled";
		};

		codec: codec@1c23c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,suniv-f1c100s-codec";
			reg = <0x01c23c00 0x400>;
			interrupts = <21>;
			clocks = <&ccu CLK_BUS_CODEC>, <&ccu CLK_CODEC>;
			clock-names = "apb", "codec";
			dmas = <&dma SUN4I_DMA_NORMAL 12>,
			       <&dma SUN4I_DMA_NORMAL 12>;
			dma-names = "rx", "tx";
			resets = <&ccu RST_BUS_CODEC>;
			status = "disabled";
		};
	};
};
