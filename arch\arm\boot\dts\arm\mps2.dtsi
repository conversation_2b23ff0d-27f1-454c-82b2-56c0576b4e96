/*
 * Copyright (C) 2015 ARM Limited
 *
 * Author: <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "../armv7-m.dtsi"

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	oscclk0: clock-50000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <50000000>;
	};

	oscclk1: clock-24576000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <24576000>;
	};

	oscclk2: clock-25000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <25000000>;
	};

	cfgclk: clock-5000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <5000000>;
	};

	spicfgclk: clock-75000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <75000000>;
	};

	sysclk: spiclcd: spicon: i2cclcd: i2caud: clock-sys {
		compatible = "fixed-factor-clock";
		clocks = <&oscclk0>;
		#clock-cells = <0>;
		clock-div = <2>;
		clock-mult = <1>;
	};

	audmclk: clk-12388000 {
		compatible = "fixed-factor-clock";
		clocks = <&oscclk1>;
		#clock-cells = <0>;
		clock-div = <2>;
		clock-mult = <1>;
	};

	audsclk: clk-3072000 {
		compatible = "fixed-factor-clock";
		clocks = <&oscclk1>;
		#clock-cells = <0>;
		clock-div = <8>;
		clock-mult = <1>;
	};

	soc {
		compatible = "simple-bus";
		ranges;

		apb@40000000 {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x40000000 0x10000>;

			timer0: mps2-timer0@0 {
				compatible = "arm,mps2-timer";
				reg = <0x0 0x1000>;
				interrupts = <8>;
				clocks = <&sysclk>;
				status = "disabled";
			};

			timer1: mps2-timer1@1000 {
				compatible = "arm,mps2-timer";
				reg = <0x1000 0x1000>;
				interrupts = <9>;
				clocks = <&sysclk>;
				status = "disabled";
			};

			timer2: dual-timer@2000 {
				compatible = "arm,sp804", "arm,primecell";
				reg = <0x2000 0x1000>;
				clocks = <&sysclk>, <&sysclk>, <&sysclk>;
				clock-names = "timer0clk", "timer1clk",
					       "apb_pclk";
				interrupts = <10>;
				status = "disabled";
			};

			uart0: serial@4000 {
				compatible = "arm,mps2-uart";
				reg = <0x4000 0x1000>;
				interrupts = <0>, <1>, <12>;
				clocks = <&sysclk>;
				status = "disabled";
			};

			uart1: serial@5000 {
				compatible = "arm,mps2-uart";
				reg = <0x5000 0x1000>;
				interrupts = <2>, <3>, <12>;
				clocks = <&sysclk>;
				status = "disabled";
			};

			uart2: serial@6000 {
				compatible = "arm,mps2-uart";
				reg = <0x6000 0x1000>;
				interrupts = <4>, <5>, <12>;
				clocks = <&sysclk>;
				status = "disabled";
			};

			wdt: watchdog@8000 {
				compatible = "arm,sp805", "arm,primecell";
				arm,primecell-periphid = <0x00141805>;
				reg = <0x8000 0x1000>;
				interrupts = <0>;
				clocks = <&sysclk>, <&sysclk>;
				clock-names = "wdog_clk", "apb_pclk";
				status = "disabled";
			};
		};
	};

	fpga@40020000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x40020000 0x10000>;

		fpgaio@8000 {
			compatible = "syscon", "simple-mfd";
			reg = <0x8000 0x10>;

			ranges = <0x0 0x8000 0x10>;
			#address-cells = <1>;
			#size-cells = <1>;

			led@0,0 {
				compatible = "register-bit-led";
				reg = <0x00 0x04>;
				offset = <0x0>;
				mask = <0x01>;
				label = "userled:0";
				linux,default-trigger = "heartbeat";
				default-state = "on";
			};

			led@0,1 {
				compatible = "register-bit-led";
				reg = <0x00 0x04>;
				offset = <0x0>;
				mask = <0x02>;
				label = "userled:1";
				linux,default-trigger = "usr";
				default-state = "off";
			};
		};
	};

	smb {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <1>;
		ranges = <0 0 0x40200000 0x10000>,
			 <1 0 0xa0000000 0x10000>;
	};
};
