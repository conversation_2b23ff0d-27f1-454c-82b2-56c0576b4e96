// SPDX-License-Identifier: GPL-2.0+
// Copyright (C) 2020 Bytedance.
/dts-v1/;

#include "aspeed-g5.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/leds/leds-pca955x.h>

/ {
	model = "Bytedance G220A BMC";
	compatible = "bytedance,g220a-bmc", "aspeed,ast2500";

	aliases {
		serial4 = &uart5;
		i2c14 = &channel_3_0;
		i2c15 = &channel_3_1;
		i2c16 = &channel_3_2;
		i2c17 = &channel_3_3;
		i2c18 = &channel_6_0;
		i2c19 = &channel_6_1;
		i2c20 = &channel_6_2;
		i2c21 = &channel_6_3;
		i2c22 = &channel_6_4;
		i2c23 = &channel_6_5;
		i2c24 = &channel_6_6;
		i2c25 = &channel_6_7;
		i2c26 = &channel_6_8;
		i2c27 = &channel_6_9;
		i2c28 = &channel_6_10;
		i2c29 = &channel_6_11;
		i2c30 = &channel_6_12;
		i2c31 = &channel_6_13;
		i2c32 = &channel_6_14;
		i2c33 = &channel_6_15;
		i2c34 = &channel_6_16;
		i2c35 = &channel_6_17;
		i2c36 = &channel_6_18;
		i2c37 = &channel_6_19;
		i2c38 = &channel_6_20;
		i2c39 = &channel_6_21;
		i2c40 = &channel_6_22;
		i2c41 = &channel_6_23;
		i2c42 = &channel_6_24;
		i2c43 = &channel_6_25;
		i2c44 = &channel_10_0;
		i2c45 = &channel_10_1;
		i2c46 = &channel_10_2;
		i2c47 = &channel_10_3;
		i2c48 = &channel_10_4;
		i2c49 = &channel_10_5;
		i2c50 = &channel_10_6;
		i2c51 = &channel_10_7;
	};

	chosen {
		stdout-path = &uart5;
		bootargs = "console=ttyS4,115200 earlycon";
	};

	memory@80000000 {
		reg = <0x80000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		vga_memory: framebuffer@bc000000 {
			no-map;
			reg = <0xbc000000 0x04000000>; /* 64M */
		};

		video_engine_memory: jpegbuffer {
			size = <0x02000000>;	/* 32M */
			alignment = <0x01000000>;
			compatible = "shared-dma-pool";
			reusable;
		};
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc 0>, <&adc 1>, <&adc 2>, <&adc 3>,
			<&adc 4>, <&adc 5>, <&adc 6>, <&adc 7>,
			<&adc 8>, <&adc 9>, <&adc 10>, <&adc 11>,
			<&adc 12>, <&adc 13>, <&adc 14>, <&adc 15>;
	};

	leds {
		compatible = "gpio-leds";
		bmc_alive {
			label = "bmc_alive";
			gpios = <&gpio ASPEED_GPIO(B, 0) GPIO_ACTIVE_LOW>;
			linux,default-trigger = "timer";
			led-pattern = <1000 1000>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		event-burn-in-signal {
			label = "burn-in";
			gpios = <&gpio ASPEED_GPIO(R, 5) GPIO_ACTIVE_LOW>;
			linux,code = <ASPEED_GPIO(R, 5)>;
		};
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <1000>;

		event-rear-riser1-presence {
			label = "rear-riser1-presence";
			gpios = <&pca0 1 GPIO_ACTIVE_LOW>;
			linux,code = <1>;
		};

		event-alrt-pvddq-cpu0 {
			label = "alrt-pvddq-cpu0";
			gpios = <&pca0 8 GPIO_ACTIVE_LOW>;
			linux,code = <2>;
		};

		event-rear-riser0-presence {
			label = "rear-riser0-presence";
			gpios = <&pca0 9 GPIO_ACTIVE_LOW>;
			linux,code = <3>;
		};

		event-fault-pvddq-cpu0 {
			label = "fault-pvddq-cpu0";
			gpios = <&pca0 10 GPIO_ACTIVE_LOW>;
			linux,code = <4>;
		};

		event-alrt-pvddq-cpu1 {
			label = "alrt-pvddq-cpu1";
			gpios = <&pca0 11 GPIO_ACTIVE_LOW>;
			linux,code = <5>;
		};

		event-fault-pvddq-cpu1 {
			label = "alrt-pvddq-cpu1";
			gpios = <&pca0 12 GPIO_ACTIVE_LOW>;
			linux,code = <6>;
		};

		event-fault-pvccin-cpu1 {
			label = "fault-pvccin-cpuq";
			gpios = <&pca0 13 GPIO_ACTIVE_LOW>;
			linux,code = <7>;
		};

		event-bmc-rom0-wp {
			label = "bmc-rom0-wp";
			gpios = <&pca1 0 GPIO_ACTIVE_LOW>;
			linux,code = <8>;
		};

		event-bmc-rom1-wp {
			label = "bmc-rom1-wp";
			gpios = <&pca1 1 GPIO_ACTIVE_LOW>;
			linux,code = <9>;
		};

		event-fan0-presence {
			label = "fan0-presence";
			gpios = <&pca1 2 GPIO_ACTIVE_LOW>;
			linux,code = <10>;
		};

		event-fan1-presence {
			label = "fan1-presence";
			gpios = <&pca1 3 GPIO_ACTIVE_LOW>;
			linux,code = <11>;
		};

		event-fan2-presence {
			label = "fan2-presence";
			gpios = <&pca1 4 GPIO_ACTIVE_LOW>;
			linux,code = <12>;
		};

		event-fan3-presence {
			label = "fan3-presence";
			gpios = <&pca1 5 GPIO_ACTIVE_LOW>;
			linux,code = <13>;
		};

		event-fan4-presence {
			label = "fan4-presence";
			gpios = <&pca1 6 GPIO_ACTIVE_LOW>;
			linux,code = <14>;
		};

		event-fan5-presence {
			label = "fan5-presence";
			gpios = <&pca1 7 GPIO_ACTIVE_LOW>;
			linux,code = <15>;
		};

		event-front-bp1-presence {
			label = "front-bp1-presence";
			gpios = <&pca1 8 GPIO_ACTIVE_LOW>;
			linux,code = <16>;
		};

		event-rear-bp-presence {
			label = "rear-bp-presence";
			gpios = <&pca1 9 GPIO_ACTIVE_LOW>;
			linux,code = <17>;
		};

		event-fault-pvccin-cpu0 {
			label = "fault-pvccin-cpu0";
			gpios = <&pca1 10 GPIO_ACTIVE_LOW>;
			linux,code = <18>;
		};

		event-alrt-p1v05-pvcc {
			label = "alrt-p1v05-pvcc1";
			gpios = <&pca1 11 GPIO_ACTIVE_LOW>;
			linux,code = <19>;
		};

		event-fault-p1v05-pvccio {
			label = "alrt-p1v05-pvcc1";
			gpios = <&pca1 12 GPIO_ACTIVE_LOW>;
			linux,code = <20>;
		};

		event-alrt-p1v8-pvccio {
			label = "alrt-p1v8-pvccio";
			gpios = <&pca1 13 GPIO_ACTIVE_LOW>;
			linux,code = <21>;
		};

		event-fault-p1v8-pvccio {
			label = "fault-p1v8-pvccio";
			gpios = <&pca1 14 GPIO_ACTIVE_LOW>;
			linux,code = <22>;
		};

		event-front-bp0-presence {
			label = "front-bp0-presence";
			gpios = <&pca1 15 GPIO_ACTIVE_LOW>;
			linux,code = <23>;
		};
	};
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		label = "bmc";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-64.dtsi"
	};
	flash@1 {
		status = "okay";
		label = "alt-bmc";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-64-alt.dtsi"
	};
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spi1_default>;
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bios";
		spi-max-frequency = <100000000>;
	};
};

&adc {
	status = "okay";
};

&wdt2 {
	status = "okay";
	aspeed,alt-boot;
};

&gpio {
	status = "okay";
	gpio-line-names =
	/*A0-A7*/	"SMRST_OCP_N","MAC2_LINK","BMC_CPLD_SMB_RST_R_N","BMC_CPLD_GPIO0",
			"","","","",
	/*B0-B7*/	"BMC_INIT_R_OK","FM_BOARD_REV_ID2","FM_PROJECT_ID7","FAULT_P12V_STBY_N",
			"","CPU0_PROCHOT_LVT3_N","","BIOS_LOAD_DEFAULT_R_N",
	/*C0-C7*/	"","","","","","","","",
	/*D0-D7*/	"","","","","","","","",
	/*E0-E7*/	"FM_PROJECT_ID0","FM_PROJECT_ID1","FM_PROJECT_ID2","FM_PROJECT_ID3",
			"FM_PROJECT_ID4","FM_PROJECT_ID5","","",
	/*F0-F7*/	"PSU0_PRSNT_N","PSU1_PRSNT_N","","FAULT_P12V_NVME_N",
			"BIOS_DEBUG_MODE_R_N","DISABLE_CPU_DDR_R_SPD","COOLING_STRATEGY",
			"PCH_GLB_RST_N",
	/*G0-G7*/	"P12V_PMBUS_ALERT_N","CPLD_ALERT_N","BMC_RELOAD_N",
			"P12V_PVDDQ_PMBUS_ALERT_N","BMC_JTAG_TCK_MUX_R_SEL","","NMI_OUT",
			"NMI_BUTTON",
	/*H0-H7*/	"BMC_CPLD_JTAG_TDI","BMC_CPLD_JTAG_TDO","BMC_CPLD_JTAG_TCK",
			"BMC_CPLD_JTAG_TMS","FM_PROJECT_ID6","FM_BOARD_REV_ID0",
			"PCA9546_U70_RST_N","IRQ_SML0_ALERT_N",
	/*I0-I7*/	"FAULT_FRONT_RISER_P12V_N","FAULT_OCP_P12V_N","FM_BMC_PCH_SCI_R_N",
			"","","","","",
	/*J0-J7*/	"FM_CPU0_SKTOCC_N","FM_CPU1_SKTOCC_N","FM_CPU1_DISABLE_COD_N",
			"","","","","",
	/*K0-K7*/	"","","","","","","","",
	/*L0-L7*/	"P12V_FAULT_N","PWRGD_P12V_PCIE_RISER","","LEAKAGE_DETECT_INPUT_N",
			"","IRQ_SML1_PMBUS_ALERT_N","","",
	/*M0-M7*/	"","","","","","","","",
	/*N0-N7*/	"","","","","","","","",
	/*O0-O7*/	"","","","","","","","",
	/*P0-P7*/	"","","","","","","","",
	/*Q0-Q7*/	"","","","","","","FM_PCH_THERMTRIP_N","CHASSIS_INTRUSION",
	/*R0-R7*/	"","PVCCIN_CPU1_SMBALERT_N","BMC_PREQ_R_N","FAULT_P12V_PCIE_RISER_N",
			"ALT_P12V_PCIE_RISER_N","BURN_BOARD_N","PVCCIN_CPU0_SMBALERT_N","",
	/*S0-S7*/	"BMC_PRDY_N","SIO_POWER_GOOD","FM_BMC_PWR_DEBUG_R_N",
			"FM_BMC_XDP_DEBUG_EN","","STRAP_BMC_BATTERY_GPIOS5","","",
	/*T0-T7*/	"","","","","","","","",
	/*U0-U7*/	"","","","","","","","",
	/*V0-V7*/	"","","","","","","","",
	/*W0-W7*/	"","","","","","","","",
	/*X0-X7*/	"","","","","","","","",
	/*Y0-Y7*/	"","PWRGD_PSU0_PWROK","CPU1_PROCHOT_LVT3_N","IRQ_BMC_PCH_SMI_LPC_N",
			"","","","",
	/*Z0-Z7*/	"XDP_PRSNT_N","BMC_XDP_SYS_PWROK","BMC_XDP_JTAG_SEL",
			"PCH_BMC_SMI_ACTIVE_R_N","","","","",
	/*AA0-AA7*/	"PWRGD_P12V_STBY_OCP","PS_PWROK","RST_PLTRST_BMC_R_N","HDA_SDO_R",
			"FM_SLPS4_R_N","PWRGD_PSU1_PWROK","POWER_BUTTON","POWER_OUT",
	/*AB0-AB7*/	"","RESET_OUT","SPI_BIOS_MODE_SELECT","POST_COMPLETE","","","","",
	/*AC0-AC7*/	"","","","","","","","CPLD_PLTRST_B_N";
};

&kcs3 {
	aspeed,lpc-io-reg = <0xCA2>;
	status = "okay";
};

&kcs4 {
	aspeed,lpc-io-reg = <0xCA4>;
	status = "okay";
};

&lpc_snoop {
	snoop-ports = <0x80>;
	status = "okay";
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd1_default
			&pinctrl_rxd1_default
			&pinctrl_nrts1_default
			&pinctrl_ndtr1_default
			&pinctrl_ndsr1_default
			&pinctrl_ncts1_default
			&pinctrl_ndcd1_default
			&pinctrl_nri1_default>;
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_txd2_default
			&pinctrl_rxd2_default
			&pinctrl_nrts2_default
			&pinctrl_ndtr2_default
			&pinctrl_ndsr2_default
			&pinctrl_ncts2_default
			&pinctrl_ndcd2_default
			&pinctrl_nri2_default>;
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&mac0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii1_default>;
	clocks = <&syscon ASPEED_CLK_GATE_MAC1CLK>,
		 <&syscon ASPEED_CLK_MAC1RCLK>;
	clock-names = "MACCLK", "RCLK";
	use-ncsi;
};

&mac1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rgmii2_default &pinctrl_mdio2_default>;
};

&i2c0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;

		channel_3_0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		channel_3_1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_3_2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_3_3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c4 {
	status = "okay";
	ipmb0@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c5 {
	status = "okay";
};

&i2c6 {
	status = "okay";
	i2c-mux@72 {
		compatible = "nxp,pca9548";
		reg = <0x72>;
		#address-cells = <1>;
		#size-cells = <0>;
		channel_6_0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		channel_6_1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_6_2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_6_3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
		channel_6_4: i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
		};

		channel_6_5: i2c@5 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <5>;
		};

		channel_6_6: i2c@6 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <6>;
		};

		channel_6_7: i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
		};
	};

	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		channel_6_8: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			i2c-mux@71 {
				compatible = "nxp,pca9546";
				reg = <0x71>;
				#address-cells = <1>;
				#size-cells = <0>;
				channel_6_12: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

				};

				channel_6_13: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				channel_6_14: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				channel_6_15: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};
			};
		 };

		channel_6_9: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			i2c-mux@71 {
				compatible = "nxp,pca9546";
				reg = <0x71>;
				#address-cells = <1>;
				#size-cells = <0>;
				channel_6_16: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

				};

				channel_6_17: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				channel_6_18: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				channel_6_19: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};
			};
		 };

		channel_6_10: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
			i2c-mux@71 {
				compatible = "nxp,pca9546";
				reg = <0x71>;
				#address-cells = <1>;
				#size-cells = <0>;
				channel_6_20: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				channel_6_21: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};

				channel_6_22: i2c@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;
				};

				channel_6_23: i2c@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;
				};
			};
		 };

		channel_6_11: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
			i2c-mux@71 {
				compatible = "nxp,pca9546";
				reg = <0x71>;
				#address-cells = <1>;
				#size-cells = <0>;
				channel_6_24: i2c@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
				};

				channel_6_25: i2c@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
				};
			 };
		};
	};
};

&i2c7 {
	status = "okay";
};

&i2c8 {
	status = "okay";
	pca0:pca9555@24 {
		compatible = "nxp,pca9555";
		reg = <0x24>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio@1 {
			reg = <1>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@8 {
			reg = <8>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@9 {
			reg = <9>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@10 {
			reg = <10>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@11 {
			reg = <11>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@12 {
			reg = <12>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@13 {
			reg = <13>;
			type = <PCA955X_TYPE_GPIO>;
		};
	};

	pca1:pca9555@25 {
		compatible = "nxp,pca9555";
		reg = <0x25>;

		#address-cells = <1>;
		#size-cells = <0>;

		gpio-controller;
		#gpio-cells = <2>;

		gpio@0 {
			reg = <0>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@1 {
			reg = <1>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@2 {
			reg = <2>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@3 {
			reg = <3>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@4 {
			reg = <4>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@5 {
			reg = <5>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@6 {
			reg = <6>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@7 {
			reg = <7>;
			type = <PCA955X_TYPE_GPIO>;
		};
		gpio@8 {
			reg = <8>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@9 {
			reg = <9>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@10 {
			reg = <10>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@11 {
			reg = <11>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@12 {
			reg = <12>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@13 {
			reg = <13>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@14 {
			reg = <14>;
			type = <PCA955X_TYPE_GPIO>;
		};

		gpio@15 {
			reg = <15>;
			type = <PCA955X_TYPE_GPIO>;
		};
	};
};

&i2c9 {
	status = "okay";
};

&i2c10 {
	status = "okay";
	i2c-mux@70 {
		compatible = "nxp,pca9546";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		channel_10_0: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		channel_10_1: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_10_2: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_10_3: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};

	i2c-mux@71 {
		compatible = "nxp,pca9546";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		channel_10_4: i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
		};

		channel_10_5: i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
		};

		channel_10_6: i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
		};

		channel_10_7: i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
		};
	};
};

&i2c11 {
	status = "okay";
};

&i2c12 {
	status = "okay";
};

&i2c13 {
	status = "okay";
};

&pwm_tacho {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default
			 &pinctrl_pwm2_default &pinctrl_pwm3_default
			 &pinctrl_pwm4_default &pinctrl_pwm5_default>;

	fan@0 {
		reg = <0x00>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00 0x01>;
	};
	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x02 0x03>;
	};
	fan@2 {
		reg = <0x02>;
		aspeed,fan-tach-ch = /bits/ 8 <0x04 0x05>;
	};
	fan@3 {
		reg = <0x03>;
		aspeed,fan-tach-ch = /bits/ 8 <0x06 0x07>;
	};
	fan@4 {
		reg = <0x04>;
		aspeed,fan-tach-ch = /bits/ 8 <0x08 0x09>;
	};
	fan@5 {
		reg = <0x05>;
		aspeed,fan-tach-ch = /bits/ 8 <0x0a 0x0b>;
	};
};

&gpio {
	pin_gpio_i3 {
		gpio-hog;
		gpios = <ASPEED_GPIO(I, 3) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "NCSI_BMC_R_SEL";
	};

	pin_gpio_b6 {
		gpio-hog;
		gpios = <ASPEED_GPIO(B, 6) GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "EN_NCSI_SWITCH_N";
	};
};

&video {
	status = "okay";
	memory-region = <&video_engine_memory>;
};

&vhub {
	status = "okay";
};
