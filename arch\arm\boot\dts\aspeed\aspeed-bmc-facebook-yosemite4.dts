// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright 2022 Facebook Inc.

/dts-v1/;
#include "aspeed-g6.dtsi"
#include <dt-bindings/gpio/aspeed-gpio.h>
#include <dt-bindings/leds/leds-pca955x.h>
#include <dt-bindings/i2c/i2c.h>

/ {
	model = "Facebook Yosemite 4 BMC";
	compatible = "facebook,yosemite4-bmc", "aspeed,ast2600";

	aliases {
		serial4 = &uart5;
		serial5 = &uart6;
		serial6 = &uart7;
		serial7 = &uart8;
		serial8 = &uart9;

		i2c16 = &imux16;
		i2c17 = &imux17;
		i2c18 = &imux18;
		i2c19 = &imux19;
		i2c20 = &imux20;
		i2c21 = &imux21;
		i2c22 = &imux22;
		i2c23 = &imux23;
		i2c24 = &imux24;
		i2c25 = &imux25;
		i2c26 = &imux26;
		i2c27 = &imux27;
		i2c28 = &imux28;
		i2c29 = &imux29;
		i2c30 = &imux30;
		i2c31 = &imux31;
		i2c32 = &imux32;
		i2c33 = &imux33;
		i2c34 = &imux34;
		i2c35 = &imux35;
	};

	chosen {
		stdout-path = "serial4:57600n8";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	iio-hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc0 0>, <&adc0 1>, <&adc0 2>, <&adc0 3>,
				<&adc0 4>, <&adc0 5>, <&adc0 6>, <&adc0 7>,
				<&adc1 0>, <&adc1 1>, <&adc1 7>;
	};

	spi {
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		sck-gpios = <&gpio0 ASPEED_GPIO(X, 3) GPIO_ACTIVE_HIGH>;
		mosi-gpios = <&gpio0 ASPEED_GPIO(X, 4) GPIO_ACTIVE_HIGH>;
		miso-gpios = <&gpio0 ASPEED_GPIO(X, 5) GPIO_ACTIVE_HIGH>;
		cs-gpios = <&gpio0 ASPEED_GPIO(X, 0) GPIO_ACTIVE_LOW>;
		num-chipselects = <1>;

		tpm@0 {
			compatible = "infineon,slb9670", "tcg,tpm_tis-spi";
			reg = <0>;
			spi-max-frequency = <33000000>;
		};
	};
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&uart5 {
	status = "okay";
};

&uart6 {
	status = "okay";
};

&uart7 {
	status = "okay";
};

&uart8 {
	status = "okay";
};

&uart9 {
	status = "okay";
};

&wdt1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst1_default>;
	aspeed,reset-type = "soc";
	aspeed,external-signal;
	aspeed,ext-push-pull;
	aspeed,ext-active-high;
	aspeed,ext-pulse-duration = <256>;
};

&wdt2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdtrst2_default>;
	aspeed,reset-type = "system";
};

&mac2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii3_default>;
	use-ncsi;
	mellanox,multi-host;
};

&mac3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_rmii4_default>;
	use-ncsi;
	mellanox,multi-host;
};

&fmc {
	status = "okay";
	flash@0 {
		status = "okay";
		m25p,fast-read;
		label = "bmc";
		spi-tx-bus-width = <2>;
		spi-rx-bus-width = <2>;
		spi-max-frequency = <50000000>;
#include "openbmc-flash-layout-128.dtsi"
	};
	flash@1 {
		status = "okay";
		m25p,fast-read;
		label = "alt-bmc";
		spi-tx-bus-width = <2>;
		spi-rx-bus-width = <2>;
		spi-max-frequency = <50000000>;
	};
};

&i2c0 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c1 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c2 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c3 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c4 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c5 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c6 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c7 {
	status = "okay";
	mctp-controller;
	bus-frequency = <400000>;
	multi-master;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	gpio@21 {
		compatible = "nxp,pca9506";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@22 {
		compatible = "nxp,pca9506";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@23 {
		compatible = "nxp,pca9506";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio@24 {
		compatible = "nxp,pca9506";
		reg = <0x24>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	power-sensor@40 {
		compatible = "adi,adm1281";
		reg = <0x40>;
		shunt-resistor-micro-ohms = <500>;
	};
};

&i2c8 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";
	bus-frequency = <400000>;
	i2c-mux@70 {
		compatible = "nxp,pca9544";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		imux16: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};

		imux17: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};

		imux18: i2c@2 {
			reg = <2>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};

		imux19: i2c@3 {
			reg = <3>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};
	};
};

&i2c9 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";
	bus-frequency = <400000>;
	i2c-mux@71 {
		compatible = "nxp,pca9544";
		reg = <0x71>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		imux20: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};

		imux21: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};

		imux22: i2c@2 {
			reg = <2>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};

		imux23: i2c@3 {
			reg = <3>;
			#address-cells = <1>;
			#size-cells = <0>;
			gpio@49 {
				compatible = "nxp,pca9537";
				reg = <0x49>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@51 {
				compatible = "atmel,24c128";
				reg = <0x51>;
			};

			eeprom@54 {
				compatible = "atmel,24c128";
				reg = <0x54>;
			};
		};
	};
};

&i2c10 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";
	bus-frequency = <400000>;
	i2c-mux@74 {
		compatible = "nxp,pca9544";
		reg = <0x74>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		imux28: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			gpio@20 {
				compatible = "nxp,pca9506";
				reg = <0x20>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			gpio@21 {
				compatible = "nxp,pca9506";
				reg = <0x21>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			gpio@22 {
				compatible = "nxp,pca9506";
				reg = <0x22>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			gpio@23 {
				compatible = "nxp,pca9506";
				reg = <0x23>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			gpio@24 {
				compatible = "nxp,pca9506";
				reg = <0x24>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio-line-names = "","","","",
						  "NIC0_MAIN_PWR_EN",
						  "NIC1_MAIN_PWR_EN",
						  "NIC2_MAIN_PWR_EN",
						  "NIC3_MAIN_PWR_EN",
						  "","","","","","","","",
						  "","","","","","","","",
						  "","","","","","","","";
			};
		};

		imux29: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};
};

&i2c11 {
	status = "okay";
	power-sensor@10 {
		compatible = "adi,adm1272";
		reg = <0x10>;
	};

	power-sensor@12 {
		compatible = "adi,adm1272";
		reg = <0x12>;
	};

	gpio@20 {
		compatible = "nxp,pca9555";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-parent = <&gpio0>;
		interrupts = <98 IRQ_TYPE_LEVEL_LOW>;
		gpio-line-names = "P48V_OCP_GPIO1", "P48V_OCP_GPIO2",
				  "P48V_OCP_GPIO3", "FAN_BOARD_0_REVISION_0_R",
				  "FAN_BOARD_0_REVISION_1_R",
				  "FAN_BOARD_1_REVISION_0_R",
				  "FAN_BOARD_1_REVISION_1_R", "RST_MUX_R_N",
				  "RST_LED_CONTROL_FAN_BOARD_0_N",
				  "RST_LED_CONTROL_FAN_BOARD_1_N",
				  "RST_IOEXP_FAN_BOARD_0_N",
				  "RST_IOEXP_FAN_BOARD_1_N",
				  "PWRGD_LOAD_SWITCH_FAN_BOARD_0_R",
				  "PWRGD_LOAD_SWITCH_FAN_BOARD_1_R",
				  "", "";
	};

	gpio@21 {
		compatible = "nxp,pca9555";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-parent = <&gpio0>;
		interrupts = <98 IRQ_TYPE_LEVEL_LOW>;
		gpio-line-names = "HSC_OCP_SLOT_ODD_GPIO1",
				  "HSC_OCP_SLOT_ODD_GPIO2",
				  "HSC_OCP_SLOT_ODD_GPIO3",
				  "HSC_OCP_SLOT_EVEN_GPIO1",
				  "HSC_OCP_SLOT_EVEN_GPIO2",
				  "HSC_OCP_SLOT_EVEN_GPIO3",
				  "ADC_TYPE_0_R", "ADC_TYPE_1_R",
				  "MEDUSA_BOARD_REV_0", "MEDUSA_BOARD_REV_1",
				  "MEDUSA_BOARD_REV_2", "MEDUSA_BOARD_TYPE",
				  "DELTA_MODULE_TYPE", "P12V_HSC_TYPE",
				  "", "";
	};

	gpio@22 {
		compatible = "nxp,pca9555";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-parent = <&gpio0>;
		interrupts = <98 IRQ_TYPE_LEVEL_LOW>;
		gpio-line-names = "CARD_TYPE_SLOT1", "CARD_TYPE_SLOT2",
				  "CARD_TYPE_SLOT3", "CARD_TYPE_SLOT4",
				  "CARD_TYPE_SLOT5", "CARD_TYPE_SLOT6",
				  "CARD_TYPE_SLOT7", "CARD_TYPE_SLOT8",
				  "OC_P48V_HSC_0_N", "FLT_P48V_HSC_0_N",
				  "OC_P48V_HSC_1_N", "FLT_P48V_HSC_1_N",
				  "EN_P48V_AUX_0", "EN_P48V_AUX_1",
				  "PWRGD_P12V_AUX_0", "PWRGD_P12V_AUX_1";
	};

	gpio@23 {
		compatible = "nxp,pca9555";
		reg = <0x23>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-parent = <&gpio0>;
		interrupts = <98 IRQ_TYPE_LEVEL_LOW>;
		gpio-line-names = "HSC1_ALERT1_R_N", "HSC2_ALERT1_R_N",
				  "HSC3_ALERT1_R_N", "HSC4_ALERT1_R_N",
				  "HSC5_ALERT1_R_N", "HSC6_ALERT1_R_N",
				  "HSC7_ALERT1_R_N", "HSC8_ALERT1_R_N",
				  "HSC1_ALERT2_R_N", "HSC2_ALERT2_R_N",
				  "HSC3_ALERT2_R_N", "HSC4_ALERT2_R_N",
				  "HSC5_ALERT2_R_N", "HSC6_ALERT2_R_N",
				  "HSC7_ALERT2_R_N", "HSC8_ALERT2_R_N";
	};

	temperature-sensor@48 {
		compatible = "ti,tmp75";
		reg = <0x48>;
	};

	temperature-sensor@49 {
		compatible = "ti,tmp75";
		reg = <0x49>;
	};

	eeprom@54 {
		compatible = "atmel,24c128";
		reg = <0x54>;
	};
};

&i2c12 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";
	bus-frequency = <400000>;

	i2c-mux@70 {
		compatible = "nxp,pca9544";
		reg = <0x70>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		imux34: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			temperature-sensor@48 {
				compatible = "ti,tmp75";
				reg = <0x48>;
			};

			eeprom@50 {
				compatible = "atmel,24c128";
				reg = <0x50>;
			};

			eeprom@54 {
				compatible = "atmel,24c64";
				reg = <0x54>;
			};

			rtc@6f {
				compatible = "nuvoton,nct3018y";
				reg = <0x6f>;
			};

			gpio@20 {
				compatible = "nxp,pca9506";
				reg = <0x20>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			gpio@21 {
				compatible = "nxp,pca9506";
				reg = <0x21>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			gpio@22 {
				compatible = "nxp,pca9506";
				reg = <0x22>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			gpio@23 {
				compatible = "nxp,pca9506";
				reg = <0x23>;
				gpio-controller;
				#gpio-cells = <2>;
			};
		};

		imux35: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};
};

&i2c13 {
	status = "okay";
	bus-frequency = <100000>;
	multi-master;

	ipmb@10 {
		compatible = "ipmb-dev";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
		i2c-protocol;
	};
};

&i2c14 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";
	bus-frequency = <400000>;
	adc@1d {
		compatible = "ti,adc128d818";
		reg = <0x1d>;
		ti,mode = /bits/ 8 <1>;
	};

	adc@36 {
		compatible = "ti,adc128d818";
		reg = <0x36>;
		ti,mode = /bits/ 8 <1>;
	};

	adc@37 {
		compatible = "ti,adc128d818";
		reg = <0x37>;
		ti,mode = /bits/ 8 <1>;
	};

	power-sensor@40 {
		compatible = "ti,ina230";
		reg = <0x40>;
	};

	power-sensor@41 {
		compatible = "ti,ina230";
		reg = <0x41>;
	};

	power-sensor@42 {
		compatible = "ti,ina230";
		reg = <0x42>;
	};

	power-sensor@43 {
		compatible = "ti,ina230";
		reg = <0x43>;
	};

	power-sensor@44 {
		compatible = "ti,ina230";
		reg = <0x44>;
	};

	temperature-sensor@4e {
		compatible = "ti,tmp75";
		reg = <0x4e>;
	};

	temperature-sensor@4f {
		compatible = "ti,tmp75";
		reg = <0x4f>;
	};

	eeprom@51 {
		compatible = "atmel,24c128";
		reg = <0x51>;
	};

	i2c-mux@73 {
		compatible = "nxp,pca9544";
		reg = <0x73>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		imux32: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			adc@35 {
				compatible = "maxim,max11617";
				reg = <0x35>;
			};
		};

		imux33: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			adc@35 {
				compatible = "maxim,max11617";
				reg = <0x35>;
			};
		};
	};

	i2c-mux@74 {
		compatible = "nxp,pca9546";
		reg = <0x74>;
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-mux-idle-disconnect;

		imux30: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			adc@1f {
				compatible = "ti,adc128d818";
				reg = <0x1f>;
				ti,mode = /bits/ 8 <1>;
			};

			pwm@20{
				compatible = "maxim,max31790";
				reg = <0x20>;
			};

			gpio@22{
				compatible = "ti,tca6424";
				reg = <0x22>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			pwm@2f{
				compatible = "maxim,max31790";
				reg = <0x2f>;
			};

			adc@33 {
				compatible = "maxim,max11615";
				reg = <0x33>;
			};

			eeprom@52 {
				compatible = "atmel,24c128";
				reg = <0x52>;
			};

			gpio@61 {
				compatible = "nxp,pca9552";
				reg = <0x61>;
				#address-cells = <1>;
				#size-cells = <0>;
				gpio-controller;
				#gpio-cells = <2>;
			};
		};

		imux31: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			adc@1f {
				compatible = "ti,adc128d818";
				reg = <0x1f>;
				ti,mode = /bits/ 8 <1>;
			};

			pwm@20{
				compatible = "maxim,max31790";
				reg = <0x20>;
			};

			gpio@22{
				compatible = "ti,tca6424";
				reg = <0x22>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			pwm@2f{
				compatible = "maxim,max31790";
				reg = <0x2f>;
			};

			adc@33 {
				compatible = "maxim,max11615";
				reg = <0x33>;
			};

			eeprom@52 {
				compatible = "atmel,24c128";
				reg = <0x52>;
			};

			gpio@61 {
				compatible = "nxp,pca9552";
				reg = <0x61>;
				#address-cells = <1>;
				#size-cells = <0>;
				gpio-controller;
				#gpio-cells = <2>;
			};
		};
	};
};

&i2c15 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";
	multi-master;
	bus-frequency = <400000>;

	mctp@10 {
		compatible = "mctp-i2c-controller";
		reg = <(0x10 | I2C_OWN_SLAVE_ADDRESS)>;
	};

	i2c-mux@72 {
		compatible = "nxp,pca9544";
		reg = <0x72>;
		#address-cells = <1>;
		#size-cells = <0>;

		imux24: i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			mctp-controller;
			temperature-sensor@1f {
				compatible = "ti,tmp421";
				reg = <0x1f>;
			};

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};

		imux25: i2c@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			mctp-controller;
			temperature-sensor@1f {
				compatible = "ti,tmp421";
				reg = <0x1f>;
			};

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};

		imux26: i2c@2 {
			reg = <2>;
			#address-cells = <1>;
			#size-cells = <0>;
			mctp-controller;
			temperature-sensor@1f {
				compatible = "ti,tmp421";
				reg = <0x1f>;
			};

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};

		imux27: i2c@3 {
			reg = <3>;
			#address-cells = <1>;
			#size-cells = <0>;
			mctp-controller;
			temperature-sensor@1f {
				compatible = "ti,tmp421";
				reg = <0x1f>;
			};

			eeprom@50 {
				compatible = "atmel,24c64";
				reg = <0x50>;
			};
		};
	};
};

&adc0 {
	status = "okay";
	pinctrl-0 = <&pinctrl_adc0_default &pinctrl_adc1_default
			&pinctrl_adc2_default &pinctrl_adc3_default
			&pinctrl_adc4_default &pinctrl_adc5_default
			&pinctrl_adc6_default &pinctrl_adc7_default>;
};

&adc1 {
	status = "okay";
	pinctrl-0 = <&pinctrl_adc8_default &pinctrl_adc9_default
			&pinctrl_adc15_default>;
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&uhci {
	status = "okay";
};
