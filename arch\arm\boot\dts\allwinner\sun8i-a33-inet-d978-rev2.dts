/*
 * Copyright 2015 <PERSON> <<EMAIL>>
 * Copyright 2016 <PERSON><PERSON>wy <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun8i-a33.dtsi"
#include "sun8i-reference-design-tablet.dtsi"

/ {
	model = "INet-D978 Rev 02";
	compatible = "primux,inet-d978-rev2", "allwinner,sun8i-a33";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		/* Delete debug UART as serial0 is the UART for bluetooth */
		/delete-property/stdout-path;
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&led_pin_d978>;

		led {
			label = "d978:blue:home";
			gpios = <&r_pio 0 5 GPIO_ACTIVE_HIGH>; /* PL5 */
		};
	};
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pg_pins>;
	vmmc-supply = <&reg_dldo1>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	rtl8723bs: wifi@1 {
		reg = <1>;
	};
};

&r_pio {
	led_pin_d978: led-pin {
		pins = "PL5";
		function = "gpio_out";
		drive-strength = <20>;
	};
};

&r_uart {
	status = "disabled";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>,
		    <&uart1_cts_rts_pg_pins>;
	status = "okay";
};
