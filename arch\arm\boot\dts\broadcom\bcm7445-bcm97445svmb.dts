// SPDX-License-Identifier: GPL-2.0
/dts-v1/;
#include "bcm7445.dtsi"

/ {
	model = "Broadcom STB (bcm7445), SVMB reference board";
	compatible = "brcm,bcm7445", "brcm,brcmstb";

	memory@0 {
		device_type = "memory";
		reg = <0x00 0x00000000 0x00 0x40000000>,
		      <0x00 0x40000000 0x00 0x40000000>,
		      <0x00 0x80000000 0x00 0x40000000>;
	};
};

&nand_controller {
	status = "okay";

	nand@1 {
		compatible = "brcm,nandcs";
		reg = <1>;
		nand-ecc-step-size = <512>;
		nand-ecc-strength = <8>;
		nand-on-flash-bbt;

		#size-cells = <2>;
		#address-cells = <2>;

		flash1.rootfs0@0 {
			reg = <0x0 0x0 0x0 0x80000000>;
		};

		flash1.rootfs1@80000000 {
			reg = <0x0 0x80000000 0x0 0x80000000>;
		};
	};
};
